{"openapi": "3.0.3", "info": {"title": "Kakao API", "version": "App version 1.0,Spring Boot Version:2.7.14"}, "servers": [{"url": "http://dev.thinkoncloud.cn", "description": "Inferred Url"}], "tags": [{"name": "TK seller 流程计划相关API", "description": "Tk Shop Plan Controller"}, {"name": "TK seller 流程调度相关API", "description": "Tk Shop Job Controller"}, {"name": "TK shop Product API", "description": "Tkshop Product Controller"}, {"name": "TK shop buyer API", "description": "Tkshop Buyer Controller"}, {"name": "TK shop order API", "description": "Tkshop Order Controller"}, {"name": "TK shop 设置相关API", "description": "Tkshop Setting Controller"}, {"name": "TK shop任务抽屉API", "description": "Tkshop Task Drawer Controller"}, {"name": "TKshop定向邀约计划API", "description": "Tkshop Invitation Controller"}, {"name": "TK店铺API", "description": "Tkshop Shop Controller"}, {"name": "TK店铺交互API", "description": "Tkshop Interaction Controller"}, {"name": "TK店铺公海达人API", "description": "Tkshop Global Creator Controller"}, {"name": "TK店铺带货直播API", "description": "Tkshop Live Controller"}, {"name": "TK店铺带货视频API", "description": "Tkshop Video Controller"}, {"name": "TK店铺达人API", "description": "Tkshop Creator Controller"}, {"name": "TK店铺达人索样API", "description": "Tkshop Sample Request Controller"}, {"name": "TK查询方案API", "description": "Tkshop Search Profile Controller"}, {"name": "TkShop 手机账号相关 API", "description": "TK Shop Mobile Account Controller"}, {"name": "Tkshop system API", "description": "Tkshop System Controller"}], "paths": {"/api/tkshop/jobs/batchSyncBuyerInfo": {"post": {"tags": ["TkShopJobController"], "summary": "批量同步买家信息", "operationId": "tkshopJobsBatchSyncBuyerInfoPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchSyncBuyerInfoRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/jobs/batchSyncCreators": {"post": {"tags": ["TkShopJobController"], "summary": "批量对达人执行基础信息更新", "operationId": "tkshopJobsBatchSyncCreatorsPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchSyncCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/jobs/batchSyncSampleCreatorRequest": {"post": {"tags": ["TkShopJobController"], "summary": "批量创建待审批的索样达人抓取任务", "operationId": "tkshopJobsBatchSyncSampleCreatorRequestPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchSyncSampleCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/jobs/batchSyncVideos": {"post": {"tags": ["TkShopJobController"], "summary": "批量同步视频信息", "operationId": "tkshopJobsBatchSyncVideosPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchSyncVideoRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/jobs/sendAddContacts": {"put": {"tags": ["TkShopJobController"], "summary": "添加达人手机到手机通讯录", "operationId": "tkshopJobsSendAddContactsPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendAddContactsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/jobs/sendAddFriends": {"put": {"tags": ["TkShopJobController"], "summary": "添加Line,WhatsApp等好友", "operationId": "tkshopJobsSendAddFriendsPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendAddFriendsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/jobs/sendBuyerIMChat": {"post": {"tags": ["TkShopJobController"], "summary": "给买家发送站内信", "operationId": "tkshopJobsSendBuyerIMChatPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendBuyerIMChatRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/jobs/sendDemandPayment": {"post": {"tags": ["TkShopJobController"], "summary": "未付款订单催付", "operationId": "tkshopJobsSendDemandPaymentPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DemandPaymentRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/jobs/sendDrawerToJobs": {"post": {"tags": ["TkShopJobController"], "summary": "批量执行选中的抽屉中的任务", "operationId": "tkshopJobsSendDrawerToJobsPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/批量执行抽屉里选中的任务"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/jobs/sendEmail": {"post": {"tags": ["TkShopJobController"], "summary": "创建发送邮件的任务", "operationId": "tkshopJobsSendEmailPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendEmailRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/jobs/sendIMChatByFilter": {"post": {"tags": ["TkShopJobController"], "summary": "创建发送站内消息（指定筛选条件）", "operationId": "tkshopJobsSendIMChatByFilterPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendIMChatByFilterRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/jobs/sendIMChatByHandle": {"post": {"tags": ["TkShopJobController"], "summary": "创建发送站内消息（指定达人列表）", "operationId": "tkshopJobsSendIMChatByHandlePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendIMChatByHandleRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/jobs/sendInstantMessage": {"put": {"tags": ["TkShopJobController"], "summary": "发送即时信息", "operationId": "tkshopJobsSendInstantMessagePut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendInstantMessageRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/jobs/sendLoginCheck": {"post": {"tags": ["TkShopJobController"], "summary": "对某个店铺执行登录状态检查流程", "operationId": "tkshopJobsSendLoginCheckPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendLoginCheckRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/jobs/sendSyncBuyerInfo": {"post": {"tags": ["TkShopJobController"], "summary": "同步买家信息", "operationId": "tkshopJobsSendSyncBuyerInfoPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncBuyerInfoRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/jobs/sendSyncOrders": {"post": {"tags": ["TkShopJobController"], "summary": "对选定店铺执行同步订单流程", "operationId": "tkshopJobsSendSyncOrdersPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncOrdersRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/jobs/sendSyncProducts": {"post": {"tags": ["TkShopJobController"], "summary": "对选定店铺执行同步商品流程", "operationId": "tkshopJobsSendSyncProductsPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncProductsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/jobs/sendSyncSampleCreatorRequest": {"post": {"tags": ["TkShopJobController"], "summary": "创建待审批的索样达人抓取任务", "operationId": "tkshopJobsSendSyncSampleCreatorRequestPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncSampleCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/jobs/sendSyncShopInfo": {"post": {"tags": ["TkShopJobController"], "summary": "对选定店铺执行信息更新的流程", "operationId": "tkshopJobsSendSyncShopInfoPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncShopInfoRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/jobs/sendTargetPlanByFilter": {"post": {"tags": ["TkShopJobController"], "summary": "创建定向邀约(指定筛选条件)", "operationId": "tkshopJobsSendTargetPlanByFilterPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendTargetPlanByFilterRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/jobs/sendTargetPlanByHandle": {"post": {"tags": ["TkShopJobController"], "summary": "创建定向邀约（指定达人列表)", "operationId": "tkshopJobsSendTargetPlanByHandlePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendTargetPlanByHandleRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/jobs/sendTargetPlanClear": {"post": {"tags": ["TkShopJobController"], "summary": "清理定向邀约计划", "operationId": "tkshopJobsSendTargetPlanClearPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TargetPlanClearRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/jobs/sendVideoAdCode": {"post": {"tags": ["TkShopJobController"], "summary": "选中达人列表获取视频投流码", "operationId": "tkshopJobsSendVideoAdCodePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendVideoADCodeRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/jobs/task/fetchJob": {"get": {"tags": ["TkShopJobController"], "summary": "流程执行时获取任务", "operationId": "tkshopJobsTaskFetchJobGet", "parameters": [{"name": "rpaTaskId", "in": "query", "description": "rpaTaskId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«GhJobDto»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/jobs/task/reportJob": {"put": {"tags": ["TkShopJobController"], "summary": "汇报ghJob执行结果", "operationId": "tkshopJobsTaskReportJobPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReportGhJobRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/mobile/account/batchUpdateMessageConfig": {"put": {"tags": ["TKShopMobileAccountController"], "summary": "更改手机账号的发送消息设置", "operationId": "tkshopMobileAccountBatchUpdateMessageConfigPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchUpdateMessageConfigRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/mobile/account/messageConfig": {"get": {"tags": ["TKShopMobileAccountController"], "summary": "获取某个手机账号的发送消息设置", "operationId": "tkshopMobileAccountMessageConfigGet", "parameters": [{"name": "mobileAccountId", "in": "query", "description": "mobileAccountId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«MobileAccountMessageConfig»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/plan/applyBestPractice": {"post": {"tags": ["TkShopPlanController"], "summary": "根据最佳实践创建一个计划链", "operationId": "tkshopPlanApplyBestPracticePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApplyBestPracticeRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/plan/createFollowingPlan": {"post": {"tags": ["TkShopPlanController"], "summary": "创建一个触发计划", "operationId": "tkshopPlanCreateFollowingPlanPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTkshopFollowingPlanRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/plan/createGroup": {"post": {"tags": ["TkShopPlanController"], "summary": "往tkshop计划编排里添加一个店铺", "operationId": "tkshopPlanCreateGroupPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddShopToPlanLayoutRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/plan/createLayout": {"post": {"tags": ["TkShopPlanController"], "summary": "创建一个tkshop计划编排", "operationId": "tkshopPlanCreateLayoutPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePlanChainLayoutRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/plan/createTimingPlan": {"post": {"tags": ["TkShopPlanController"], "summary": "创建一个定时计划", "operationId": "tkshopPlanCreateTimingPlanPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/创建一个tkshop定时计划"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/plan/deleteGroup": {"delete": {"tags": ["TkShopPlanController"], "summary": "删除编排计划下的一个店铺，会同时删除该分身下的所有计划链", "operationId": "tkshopPlanDeleteGroupDelete", "parameters": [{"name": "groupId", "in": "query", "description": "groupId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/plan/deleteLayout": {"delete": {"tags": ["TkShopPlanController"], "summary": "删除一个计划编排，会删除该编排下所有的计划", "operationId": "tkshopPlanDeleteLayoutDelete", "parameters": [{"name": "layoutId", "in": "query", "description": "layoutId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/plan/deletePlan": {"delete": {"tags": ["TkShopPlanController"], "summary": "删除一个计划。会导致链上该计划的后续计划全部删除", "operationId": "tkshopPlanDeletePlanDelete", "parameters": [{"name": "planId", "in": "query", "description": "planId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/plan/executeAllPlansInGroup": {"put": {"tags": ["TkShopPlanController"], "summary": "执行一个group里的所有timing计划", "operationId": "tkshopPlanExecuteAllPlansInGroupPut", "parameters": [{"name": "groupId", "in": "query", "description": "groupId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/plan/executeAllPlansInLayout": {"put": {"tags": ["TkShopPlanController"], "summary": "执行一个layout里的所有timing计划", "operationId": "tkshopPlanExecuteAllPlansInLayoutPut", "parameters": [{"name": "layoutId", "in": "query", "description": "layoutId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/plan/getLayout": {"get": {"tags": ["TkShopPlanController"], "summary": "获取单个tkshop计划编排信息", "operationId": "tkshopPlanGetLayoutGet", "parameters": [{"name": "layoutId", "in": "query", "description": "layoutId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkshopPlanChainLayoutVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/plan/loadGroupChains": {"get": {"tags": ["TkShopPlanController"], "summary": "获取某个计划分组下的计划链信息", "operationId": "tkshopPlanLoadGroupChainsGet", "parameters": [{"name": "groupId", "in": "query", "description": "groupId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«PlanGroupChain»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/plan/loadLayoutGroups": {"get": {"tags": ["TkShopPlanController"], "summary": "获取某个tkshop计划编的店铺信息，每个店铺的多条计划链称作一个分组", "operationId": "tkshopPlanLoadLayoutGroupsGet", "parameters": [{"name": "layoutId", "in": "query", "description": "layoutId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TkshopPlanChainGroupVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/plan/loadLayouts": {"get": {"tags": ["TkShopPlanController"], "summary": "获取tkshop计划编排列表", "operationId": "tkshopPlanLoadLayoutsGet", "parameters": [{"name": "all", "in": "query", "description": "只有当用户角色为超管和BOSS的时候才有意义", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TkshopPlanChainLayoutVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/plan/runPlan": {"put": {"tags": ["TkShopPlanController"], "summary": "立即执行一个计划", "operationId": "tkshopPlanRunPlanPut", "parameters": [{"name": "planId", "in": "query", "description": "planId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/plan/toggleLayoutEnabled": {"put": {"tags": ["TkShopPlanController"], "summary": "切换一个计划编排的开启状态，关闭之后会导致该编排里所有计划都不调度", "operationId": "tkshopPlanToggleLayoutEnabledPut", "parameters": [{"name": "layoutId", "in": "query", "description": "layoutId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "enabled", "in": "query", "description": "enabled", "required": true, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/plan/togglePlanEnabled": {"put": {"tags": ["TkShopPlanController"], "summary": "切换一个计划的开启状态", "operationId": "tkshopPlanTogglePlanEnabledPut", "parameters": [{"name": "planId", "in": "query", "description": "planId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "enabled", "in": "query", "description": "enabled", "required": true, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/plan/updateLayoutDevice": {"post": {"tags": ["TkShopPlanController"], "summary": "修改计划所属设备", "operationId": "tkshopPlanUpdateLayoutDevicePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateLayoutDeviceRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/plan/updateLayoutName": {"put": {"tags": ["TkShopPlanController"], "summary": "更改一个计划编排的名称", "operationId": "tkshopPlanUpdateLayoutNamePut", "parameters": [{"name": "layoutId", "in": "query", "description": "layoutId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "name", "in": "query", "description": "name", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/plan/updatePlan": {"post": {"tags": ["TkShopPlanController"], "summary": "修改一个计划的执行时间（自动计划）/ 触发间隔（触发计划），计划参数", "operationId": "tkshopPlanUpdatePlanPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePlanRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/buyer/byHandle": {"get": {"tags": ["TkshopBuyerController"], "summary": "获取买家详情", "operationId": "tkshopBuyerByHandleGet", "parameters": [{"name": "handle", "in": "query", "description": "handle", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkshopBuyerDto»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/buyer/calcBuyerShops": {"post": {"tags": ["TkshopBuyerController"], "summary": "计算一批买家关联的店铺情况", "operationId": "tkshopBuyerCalcBuyerShopsPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkshopShopBuyerResult»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/buyer/countTodayInteractions": {"get": {"tags": ["TkshopBuyerController"], "summary": "查询店铺特定类型交互的次数", "operationId": "tkshopBuyerCountTodayInteractionsGet", "parameters": [{"name": "shopId", "in": "query", "description": "shopId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "interactType", "in": "query", "description": "interactType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["add_contact", "add_friend", "im_chat", "manual_remark", "send_email", "send_facebook", "send_line", "send_whatsapp", "send_zalo"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/buyer/friendship": {"post": {"tags": ["TkshopBuyerController"], "summary": "新增好友关系", "operationId": "tkshopBuyerFriendshipPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TkshopAddFriendshipRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/buyer/interaction/addManualRemark": {"post": {"tags": ["TkshopBuyerController"], "summary": "批量添加手动记录", "operationId": "tkshopBuyerInteractionAddManualRemarkPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddTkshopManualRemarkRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/buyer/interaction/deleteManualRemark": {"put": {"tags": ["TkshopBuyerController"], "summary": "批量删除手动记录", "operationId": "tkshopBuyerInteractionDeleteManualRemarkPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/buyer/interaction/updateManualRemark": {"put": {"tags": ["TkshopBuyerController"], "summary": "批量修改手动记录", "operationId": "tkshopBuyerInteractionUpdateManualRemarkPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddTkshopManualRemarkRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/buyer/interactions": {"get": {"tags": ["TkshopBuyerController"], "summary": "分页查询买家交互记录", "operationId": "tkshopBuyerInteractionsGet", "parameters": [{"name": "buyerId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "shopIds", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "sortField", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«TkshopBuyerInteractionDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/buyer/lastInteraction": {"get": {"tags": ["TkshopBuyerController"], "summary": "查询特定类型的最后一次交互", "operationId": "tkshopBuyerLastInteractionGet", "parameters": [{"name": "buyerId", "in": "query", "description": "buyerId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "interactType", "in": "query", "description": "interactType", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkshopBuyerInteractionDto»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/buyer/page": {"post": {"tags": ["TkshopBuyerController"], "summary": "分页查询团队买家", "operationId": "tkshopBuyerPagePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageTkshopBuyerRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«TkshopBuyerDetailVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/buyer/regions": {"get": {"tags": ["TkshopBuyerController"], "summary": "获取达人的区域（根据用户权限过滤）", "operationId": "tkshopBuyerRegionsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«string»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/buyer/{id}": {"get": {"tags": ["TkshopBuyerController"], "summary": "获取买家详情", "operationId": "tkshopBuyerByIdGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkshopBuyerDetailVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/buyer/{id}/contact": {"put": {"tags": ["TkshopBuyerController"], "summary": "更新买家联系方式", "operationId": "tkshopBuyerByIdContactPut", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateContactRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/buyer/{id}/oneContact": {"put": {"tags": ["TkshopBuyerController"], "summary": "修改一种联系方式", "operationId": "tkshopBuyerByIdOneContactPut", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateOneContactRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/buyer/{id}/remark": {"put": {"tags": ["TkshopBuyerController"], "summary": "更新买家备注", "operationId": "tkshopBuyerByIdRemarkPut", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "remark", "in": "query", "description": "remark", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/syncBuyers": {"post": {"tags": ["TkshopBuyerController"], "summary": "同步买家（批量）", "operationId": "tkshopSyncBuyersPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncBuyerRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/acquire": {"put": {"tags": ["TkshopCreatorController"], "summary": "认领达人", "operationId": "tkshopCreatorAcquirePut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": true, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/allocate": {"put": {"tags": ["TkshopCreatorController"], "summary": "分配达人", "operationId": "tkshopCreatorAllocatePut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TkshopCreatorAllocateRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/autoTagExpiredTag": {"post": {"tags": ["TkshopCreatorController"], "summary": "自动化标签", "operationId": "tkshopCreatorAutoTagExpiredTagPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TkshopAutoTagRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/cancelAllocate": {"put": {"tags": ["TkshopCreatorController"], "summary": "取消分配", "operationId": "tkshopCreatorCancelAllocatePut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/categories": {"get": {"tags": ["TkshopCreatorController"], "summary": "获取分类列表", "operationId": "tkshopCreatorCategoriesGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TkshopCategoryDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/contactList": {"post": {"tags": ["TkshopCreatorController"], "summary": "获取达人联系方式列表", "operationId": "tkshopCreatorContactListPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorHandleRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TkshopContactVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/contacts": {"post": {"tags": ["TkshopCreatorController"], "summary": "获取达人特定联系方式列表", "operationId": "tkshopCreatorContactsPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorContactRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«string»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/count": {"post": {"tags": ["TkshopCreatorController"], "summary": "查询店铺达人总数", "operationId": "tkshopCreatorCountPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FindTkshopCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«long»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/delete": {"post": {"tags": ["TkshopCreatorController"], "summary": "删除达人", "operationId": "tkshopCreatorDeletePost", "parameters": [{"name": "force", "in": "query", "description": "force", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "deleteRemark", "in": "query", "description": "deleteRemark", "required": false, "style": "form", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/deleteByQuery": {"post": {"tags": ["TkshopCreatorController"], "summary": "删除达人（按查询）", "operationId": "tkshopCreatorDeleteByQueryPost", "parameters": [{"name": "force", "in": "query", "description": "force", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "deleteRemark", "in": "query", "description": "deleteRemark", "required": false, "style": "form", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageTkshopCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/filterBasicSynced": {"post": {"tags": ["TkshopCreatorController"], "summary": "过滤出在若干天内同步过的达人", "operationId": "tkshopCreatorFilterBasicSyncedPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FilterBasicSyncedRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«string»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/friendship": {"post": {"tags": ["TkshopCreatorController"], "summary": "新增好友关系", "operationId": "tkshopCreatorFriendshipPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TkshopAddFriendshipRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/getAutoTagValue": {"get": {"tags": ["TkshopCreatorController"], "summary": "获取当天自动化标签值", "operationId": "tkshopCreatorGetAutoTagValueGet", "parameters": [{"name": "<PERSON><PERSON><PERSON>", "in": "query", "description": "<PERSON><PERSON><PERSON>", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/groupByRegion": {"post": {"tags": ["TkshopCreatorController"], "summary": "按区域分组查询达人", "operationId": "tkshopCreatorGroupByRegionPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageTkshopCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«RegionBatchVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/loadCid": {"post": {"tags": ["TkshopCreatorController"], "summary": "获取达人的cid", "operationId": "tkshopCreatorLoadCidPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«Map«long,string»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/loadCidByHandles": {"post": {"tags": ["TkshopCreatorController"], "summary": "根据handles获取达人的cid", "operationId": "tkshopCreatorLoadCidByHandlesPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HandlesRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«Map«string,string»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/loadContactDetails": {"post": {"tags": ["TkshopCreatorController"], "summary": "批量获取达人联系方式详情", "operationId": "tkshopCreatorLoadContactDetailsPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TkshopCreatorContactDetailVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/page": {"post": {"tags": ["TkshopCreatorController"], "summary": "分页查询达人（不返回总数）", "operationId": "tkshopCreatorPagePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageTkshopCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«TkshopCreatorDetailVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/recover": {"post": {"tags": ["TkshopCreatorController"], "summary": "恢复达人", "operationId": "tkshopCreatorRecoverPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/region": {"put": {"tags": ["TkshopCreatorController"], "summary": "修改区域", "operationId": "tkshopCreatorRegionPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRegionRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/regions": {"get": {"tags": ["TkshopCreatorController"], "summary": "获取达人的区域（根据用户权限过滤）", "operationId": "tkshopCreatorRegionsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«string»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/release": {"put": {"tags": ["TkshopCreatorController"], "summary": "取消认领达人", "operationId": "tkshopCreatorReleasePut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": true, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/responsibleUserList": {"get": {"tags": ["TkshopCreatorController"], "summary": "getResponsibleUserList", "operationId": "tkshopCreatorResponsibleUserListGet", "parameters": [{"name": "platformType", "in": "query", "description": "platformType", "required": false, "style": "form", "schema": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«long»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/scrapFBProfile": {"get": {"tags": ["TkshopCreatorController"], "summary": "获取facebook的profile，会返回fbid数字id，用于跳转到facebook的消息页面", "operationId": "tkshopCreatorScrapFBProfileGet", "parameters": [{"name": "messenger", "in": "query", "description": "messenger", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«FBUserProfile»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/status": {"put": {"tags": ["TkshopCreatorController"], "summary": "根据handles更新达人状态", "operationId": "tkshopCreatorStatusPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateStatusRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/syncBatchDetail": {"post": {"tags": ["TkshopCreatorController"], "summary": "批量同步达人带货能力数据", "operationId": "tkshopCreatorSyncBatchDetailPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncCreatorDetailRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/syncBatchOnShop": {"post": {"tags": ["TkshopCreatorController"], "summary": "批量同步达人在店铺中带货信息", "operationId": "tkshopCreatorSyncBatchOnShopPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncCreatorShopRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/{id}": {"get": {"tags": ["TkshopCreatorController"], "summary": "获取达人详情", "operationId": "tkshopCreatorByIdGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkshopCreatorDetailVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/{id}/achievementShop": {"get": {"tags": ["TkshopCreatorController"], "summary": "获取达人分身带货数据", "operationId": "tkshopCreatorByIdAchievementShopGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "shopId", "in": "query", "description": "shopId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkshopCreatorShopDto»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/{id}/achievementShops": {"get": {"tags": ["TkshopCreatorController"], "summary": "获取达人带货的店铺列表", "operationId": "tkshopCreatorByIdAchievementShopsGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«ShopBriefVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/{id}/achievementStat": {"get": {"tags": ["TkshopCreatorController"], "summary": "获取达人带货成果", "operationId": "tkshopCreatorByIdAchievementStatGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkshopCreatorAchievementStat»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/{id}/achievements": {"get": {"tags": ["TkshopCreatorController"], "summary": "获取达人带货成果", "operationId": "tkshopCreatorByIdAchievementsGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "shopId", "in": "query", "description": "shopId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkshopCreatorAchievementResult»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": true, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/{id}/contact": {"put": {"tags": ["TkshopCreatorController"], "summary": "修改达人联系方式（全量更新）", "operationId": "tkshopCreatorByIdContactPut", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateContactRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/{id}/interactShops": {"get": {"tags": ["TkshopCreatorController"], "summary": "获取达人交互的店铺列表", "operationId": "tkshopCreatorByIdInteractShopsGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«ShopBriefVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/{id}/lives": {"get": {"tags": ["TkshopCreatorController"], "summary": "获取达人带货直播", "operationId": "tkshopCreatorByIdLivesGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "shopId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "sortField", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«TkshopCreatorLiveVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/{id}/oneContact": {"put": {"tags": ["TkshopCreatorController"], "summary": "修改一种联系方式", "operationId": "tkshopCreatorByIdOneContactPut", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateOneContactRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/{id}/products": {"get": {"tags": ["TkshopCreatorController"], "summary": "获取达人带货商品", "operationId": "tkshopCreatorByIdProductsGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "shopId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "sortField", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«TkshopCreatorProductDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/{id}/remark": {"put": {"tags": ["TkshopCreatorController"], "summary": "修改达人备注", "operationId": "tkshopCreatorByIdRemarkPut", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "remark", "in": "query", "description": "remark", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/{id}/statusAndRemark": {"put": {"tags": ["TkshopCreatorController"], "summary": "修改达人备注", "operationId": "tkshopCreatorByIdStatusAndRemarkPut", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "status", "in": "query", "description": "status", "required": true, "style": "form", "schema": {"type": "string", "enum": ["Collaborating", "HasOrder", "NotContacted", "<PERSON><PERSON>"]}}, {"name": "remark", "in": "query", "description": "remark", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/creator/{id}/videos": {"get": {"tags": ["TkshopCreatorController"], "summary": "获取达人视频直播", "operationId": "tkshopCreatorByIdVideosGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "shopId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "sortField", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«TkshopCreatorVideoVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/fields": {"get": {"tags": ["TkshopCreatorController"], "summary": "达人可导出的字段元信息", "operationId": "tkshopFieldsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TkshopCreatorFiledVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/hasInteractions": {"post": {"tags": ["TkshopCreatorController"], "summary": "是否与达人产生过交互（批量）", "operationId": "tkshopHasInteractionsPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TkshopHasInteractionRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«HasInteractionVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/shop/{shopId}/loginStatus": {"put": {"tags": ["TkshopCreatorController"], "summary": "更新分身的登录态", "operationId": "tkshopShopByShopIdLoginStatusPut", "parameters": [{"name": "shopId", "in": "path", "description": "shopId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "loginStatus", "in": "query", "description": "loginStatus", "required": true, "style": "form", "schema": {"type": "string", "enum": ["Offline", "Online", "Unknown"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/global/creator/count": {"post": {"tags": ["TkshopGlobalCreatorController"], "summary": "查询店铺达人总数", "operationId": "tkshopGlobalCreatorCountPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FindTkshopGlobalCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«long»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/global/creator/groupByRegion": {"post": {"tags": ["TkshopGlobalCreatorController"], "summary": "按区域分组查询达人", "operationId": "tkshopGlobalCreatorGroupByRegionPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageTkshopGlobalCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«RegionBatchVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/global/creator/importToTeam": {"post": {"tags": ["TkshopGlobalCreatorController"], "summary": "按查询批量导入到团队", "operationId": "tkshopGlobalCreatorImportToTeamPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ImportTkshopGlobalCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/global/creator/importToTeamByHandles": {"post": {"tags": ["TkshopGlobalCreatorController"], "summary": "若干天内同步过的达人导入团队", "description": "返回未同步的达人列表", "operationId": "tkshopGlobalCreatorImportToTeamByHandlesPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FilterBasicSyncedRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«string»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/global/creator/importToTeamByIds": {"post": {"tags": ["TkshopGlobalCreatorController"], "summary": "按查询批量导入到团队", "operationId": "tkshopGlobalCreatorImportToTeamByIdsPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ImportGlobalCreatorByIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/global/creator/page": {"post": {"tags": ["TkshopGlobalCreatorController"], "summary": "分页查询达人（不返回总数）", "operationId": "tkshopGlobalCreatorPagePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageTkshopGlobalCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«TkshopGlobalCreatorDetailVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/global/fields": {"get": {"tags": ["TkshopGlobalCreatorController"], "summary": "达人可导出的字段元信息", "operationId": "tkshopGlobalFieldsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TkshopCreatorFiledVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/countTodayInteractions": {"get": {"tags": ["TkshopInteractionController"], "summary": "查询店铺特定类型交互的次数", "operationId": "tkshopCountTodayInteractionsGet", "parameters": [{"name": "shopId", "in": "query", "description": "shopId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "interactType", "in": "query", "description": "interactType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["add_friend", "collaborating", "has_order", "im_chat", "manual_remark", "sample_request", "send_email", "send_facebook", "send_line", "send_whatsapp", "send_zalo", "target_plan"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/interaction": {"post": {"tags": ["TkshopInteractionController"], "summary": "新增交互记录", "operationId": "tkshopInteractionPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddTkshopInteractionRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkshopInteractionDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/interaction/addManualRemark": {"post": {"tags": ["TkshopInteractionController"], "summary": "批量添加手动记录", "operationId": "tkshopInteractionAddManualRemarkPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddTkshopManualRemarkRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/interaction/deleteManualRemark": {"put": {"tags": ["TkshopInteractionController"], "summary": "批量删除手动记录", "operationId": "tkshopInteractionDeleteManualRemarkPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/interaction/updateManualRemark": {"put": {"tags": ["TkshopInteractionController"], "summary": "批量修改手动记录", "operationId": "tkshopInteractionUpdateManualRemarkPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddTkshopManualRemarkRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/interactions": {"get": {"tags": ["TkshopInteractionController"], "summary": "分页查询交互记录", "operationId": "tkshopInteractionsGet", "parameters": [{"name": "creatorId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "shopIds", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "sortField", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«TkshopInteractionDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "post": {"tags": ["TkshopInteractionController"], "summary": "新增交互记录（批量）", "operationId": "tkshopInteractionsPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddTkshopInteractionsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/lastInteraction": {"get": {"tags": ["TkshopInteractionController"], "summary": "查询特定类型的最后一次交互", "operationId": "tkshopLastInteractionGet", "parameters": [{"name": "creatorId", "in": "query", "description": "creatorId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "interactType", "in": "query", "description": "interactType", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkshopInteractionDto»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/checkInvitationCreators": {"post": {"tags": ["TkshopInvitationController"], "summary": "检查哪些达人是否可以邀约", "description": "返回可邀约的达人列表", "operationId": "tkshopCheckInvitationCreatorsPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TkshopCheckInvitationCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«string»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/checkInvitations": {"post": {"tags": ["TkshopInvitationController"], "summary": "检查哪些计划需要同步", "operationId": "tkshopCheckInvitationsPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TkshopCheckInvitationRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«string»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/syncInvitations": {"post": {"tags": ["TkshopInvitationController"], "summary": "同步计划", "operationId": "tkshopSyncInvitationsPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TkshopSyncInvitationRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/live/creator/page": {"post": {"tags": ["TkshopLiveController"], "summary": "查询带视频的达人", "operationId": "tkshopLiveCreatorPagePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageVideoCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«TkshopCreatorProductVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/live/detail": {"get": {"tags": ["TkshopLiveController"], "summary": "返回直播的详情", "operationId": "tkshopLiveDetailGet", "parameters": [{"name": "creatorId", "in": "query", "description": "creatorId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "mediaId", "in": "query", "description": "mediaId", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkshopLiveVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/live/page": {"post": {"tags": ["TkshopLiveController"], "summary": "分页查询直播", "operationId": "tkshopLivePagePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageLiveRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«TkshopLiveVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/live/remark": {"put": {"tags": ["TkshopLiveController"], "summary": "更新备注", "operationId": "tkshopLiveRemarkPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRemarkRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/live/shops": {"get": {"tags": ["TkshopLiveController"], "summary": "查询带视频的店铺", "operationId": "tkshopLiveShopsGet", "parameters": [{"name": "query", "in": "query", "description": "query", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«long»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/findBuyerLastOrderNo": {"get": {"tags": ["TkshopOrderController"], "summary": "查询某个tkshop买家的数据库里的最后一个订单的orderNo，返回空表示没有订单（应该不可能生）", "operationId": "tkshopFindBuyerLastOrderNoGet", "parameters": [{"name": "buyerId", "in": "query", "description": "buyerId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "shopId", "in": "query", "description": "shopId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/findBuyerOrders": {"get": {"tags": ["TkshopOrderController"], "summary": "查询某个tkshop买家的订单", "operationId": "tkshopFindBuyerOrdersGet", "parameters": [{"name": "buyerId", "in": "query", "description": "buyerId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "sortField", "in": "query", "description": "sortField", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "description": "sortOrder", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TkshopOrderVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/order/calcProduct": {"post": {"tags": ["TkshopOrderController"], "summary": "统计商品数量和销量", "operationId": "tkshopOrderCalcProductPost", "parameters": [{"name": "currency1", "in": "query", "description": "currency1", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "currency2", "in": "query", "description": "currency2", "required": false, "style": "form", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageTkshopOrderRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«OrderProductStatVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/order/count": {"post": {"tags": ["TkshopOrderController"], "summary": "统计订单数量", "operationId": "tkshopOrderCountPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageTkshopOrderRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkshopOrderCountVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/order/hasNoPhoneList": {"get": {"tags": ["TkshopOrderController"], "summary": "查询特定状态订单是否有手机号", "operationId": "tkshopOrderHasNoPhoneListGet", "parameters": [{"name": "shopId", "in": "query", "description": "shopId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«string»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/order/hasPhoneList": {"get": {"tags": ["TkshopOrderController"], "summary": "查询特定状态订单是否有手机号", "operationId": "tkshopOrderHasPhoneListGet", "parameters": [{"name": "shopId", "in": "query", "description": "shopId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TkshopOrderHasPhoneVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": true, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/order/markSyncDone": {"get": {"tags": ["TkshopOrderController"], "summary": "标记同步订单结束", "operationId": "tkshopOrderMarkSyncDoneGet", "parameters": [{"name": "shopId", "in": "query", "description": "shopId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/order/page": {"post": {"tags": ["TkshopOrderController"], "summary": "分页查询订单", "operationId": "tkshopOrderPagePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageTkshopOrderRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«TkshopOrderVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/order/stat": {"post": {"tags": ["TkshopOrderController"], "summary": "统计订单", "operationId": "tkshopOrderStatPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageTkshopOrderRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkshopOrderStatVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": true, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/order/statusStat": {"post": {"tags": ["TkshopOrderController"], "summary": "按状态统计店铺数据", "operationId": "tkshopOrderStatusStatPost", "parameters": [{"name": "currency1", "in": "query", "description": "currency1", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "currency2", "in": "query", "description": "currency2", "required": false, "style": "form", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageTkshopOrderRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«OrderStatusStatVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/order/statusStatOnShop/{shopId}": {"post": {"tags": ["TkshopOrderController"], "summary": "按状态统计单个店铺数据", "operationId": "tkshopOrderStatusStatOnShopByShopIdPost", "parameters": [{"name": "shopId", "in": "path", "description": "shopId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "currency", "in": "query", "description": "currency", "required": false, "style": "form", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageTkshopOrderRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«OrderStatusStatVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/order/sync": {"post": {"tags": ["TkshopOrderController"], "summary": "同步订单", "operationId": "tkshopOrderSyncPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TkshopSyncOrderRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/order/syncAffiliate": {"post": {"tags": ["TkshopOrderController"], "summary": "同步联盟订单", "operationId": "tkshopOrderSyncAffiliatePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncAffiliateOrderRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/order/syncAffiliateNoData": {"post": {"tags": ["TkshopOrderController"], "summary": "同步无数据的联盟订单", "operationId": "tkshopOrderSyncAffiliateNoDataPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncAffiliateNoDataRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/order/unfinishedAffiliateOrders": {"get": {"tags": ["TkshopOrderController"], "summary": "获取未同步完的联盟订单", "operationId": "tkshopOrderUnfinishedAffiliateOrdersGet", "parameters": [{"name": "shopId", "in": "query", "description": "shopId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "limit", "in": "query", "description": "limit", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«string»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/order/unfinishedList": {"get": {"tags": ["TkshopOrderController"], "summary": "获取未完成订单", "operationId": "tkshopOrderUnfinishedListGet", "parameters": [{"name": "shopId", "in": "query", "description": "shopId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«string»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/product/avatarsByNo": {"post": {"tags": ["TkshopProductController"], "summary": "获取产品的主图", "operationId": "tkshopProductAvatarsByNoPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductNoRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«Map«string,string»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/product/byNo": {"get": {"tags": ["TkshopProductController"], "summary": "根据productNo查询商品", "operationId": "tkshopProductByNoGet", "parameters": [{"name": "shopId", "in": "query", "description": "shopId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "productNo", "in": "query", "description": "productNo", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkshopProductDto»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/product/fields": {"get": {"tags": ["TkshopProductController"], "summary": "商品的字段元数据", "operationId": "tkshopProductFieldsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TkshopCreatorFiledVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/product/makeExists": {"post": {"tags": ["TkshopProductController"], "summary": "标记TK商品Live", "operationId": "tkshopProductMakeExistsPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MarkProductLiveRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/product/page": {"get": {"tags": ["TkshopProductController"], "summary": "分页查询TK商品", "operationId": "tkshopProductPageGet", "parameters": [{"name": "itemsSoldFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "itemsSoldTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "name", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "platformId", "in": "query", "description": "所属站点", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "priceFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "bigdecimal"}}, {"name": "priceTo", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "bigdecimal"}}, {"name": "productNo", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "quantityFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "quantityTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "query", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "shopId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "shopIds", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "sortField", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}, {"name": "status", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Deactivated", "Deleted", "Draft", "Live", "Reviewing", "Suspended"]}}, {"name": "updateTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "updateTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«TkshopProductDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/product/sync": {"post": {"tags": ["TkshopProductController"], "summary": "同步TK商品", "operationId": "tkshopProductSyncPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncProductRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/product/{id}/remark": {"put": {"tags": ["TkshopProductController"], "summary": "设置商品的备注", "operationId": "tkshopProductByIdRemarkPut", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "remark", "in": "query", "description": "remark", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/expressionSymbols": {"get": {"tags": ["TkshopSampleRequestController"], "summary": "获取表达式符号", "operationId": "tkshopExpressionSymbolsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«ExpressionSymbolVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/sample-request/batchDelete": {"post": {"tags": ["TkshopSampleRequestController"], "summary": "添加索样策略", "operationId": "tkshopSampleRequestBatchDeletePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/sample-request/completedApplyIds": {"get": {"tags": ["TkshopSampleRequestController"], "summary": "获取若干天已完成的索样记录ID", "operationId": "tkshopSampleRequestCompletedApplyIdsGet", "parameters": [{"name": "days", "in": "query", "description": "days", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "shopId", "in": "query", "description": "shopId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«string»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/sample-request/page": {"get": {"tags": ["TkshopSampleRequestController"], "summary": "分页查询索样记录", "operationId": "tkshopSampleRequestPageGet", "parameters": [{"name": "applyId", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "creatorId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "followerCntFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "followerCntTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "includeMedia", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "needCreatorDetail", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "needProductDetail", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "notStatus", "in": "query", "description": "排除的状态", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Cancelled", "Completed", "Expired", "InProgress", "ReadyToShip", "Reject", "Shipped", "ToReview"]}}, {"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "query", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "region", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "shopId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "shopIds", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "sortField", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}, {"name": "status", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Cancelled", "Completed", "Expired", "InProgress", "ReadyToShip", "Reject", "Shipped", "ToReview"]}}, {"name": "statusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["Cancelled", "Completed", "Expired", "InProgress", "ReadyToShip", "Reject", "Shipped", "ToReview"]}}, {"name": "tagIds", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "tagLc", "in": "query", "description": "标签的逻辑条件，支持OR|AND", "required": false, "style": "form", "schema": {"type": "string", "enum": ["AND", "NOT", "OR"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«TkshopSampleRequestDetailVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/sample-request/pageShopsToReview": {"get": {"tags": ["TkshopSampleRequestController"], "summary": "获取索样审批的店铺", "operationId": "tkshopSampleRequestPageShopsToReviewGet", "parameters": [{"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "sortField", "in": "query", "description": "可取值：lastSyncTime,count", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["ASC", "DESC"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«SampleRequestShopBriefVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/sample-request/pageV2": {"post": {"tags": ["TkshopSampleRequestController"], "summary": "分页查询索样记录", "operationId": "tkshopSampleRequestPageV2Post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageSampleRequestRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«TkshopSampleRequestDetailVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/sample-request/pageWithPolicy": {"get": {"tags": ["TkshopSampleRequestController"], "summary": "分页查询索样记录", "operationId": "tkshopSampleRequestPageWithPolicyGet", "parameters": [{"name": "applyId", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "creatorId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "followerCntFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "followerCntTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "includeMedia", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "needCreatorDetail", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "needProductDetail", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "notStatus", "in": "query", "description": "排除的状态", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Cancelled", "Completed", "Expired", "InProgress", "ReadyToShip", "Reject", "Shipped", "ToReview"]}}, {"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "policyId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "query", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "region", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "shopId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "shopIds", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "sortField", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}, {"name": "status", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Cancelled", "Completed", "Expired", "InProgress", "ReadyToShip", "Reject", "Shipped", "ToReview"]}}, {"name": "statusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["Cancelled", "Completed", "Expired", "InProgress", "ReadyToShip", "Reject", "Shipped", "ToReview"]}}, {"name": "tagIds", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "tagLc", "in": "query", "description": "标签的逻辑条件，支持OR|AND", "required": false, "style": "form", "schema": {"type": "string", "enum": ["AND", "NOT", "OR"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«TkshopSampleRequestAuditVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/sample-request/policy": {"post": {"tags": ["TkshopSampleRequestController"], "summary": "添加索样策略", "operationId": "tkshopSampleRequestPolicyPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddSampleRequestPolicyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkshopSampleRequestPolicyVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/sample-request/policy/list": {"get": {"tags": ["TkshopSampleRequestController"], "summary": "获取所有索样策略", "operationId": "tkshopSampleRequestPolicyListGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TkshopSampleRequestPolicyVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/sample-request/policy/{id}": {"put": {"tags": ["TkshopSampleRequestController"], "summary": "修改索样策略", "operationId": "tkshopSampleRequestPolicyByIdPut", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddSampleRequestPolicyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkshopSampleRequestPolicyVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "delete": {"tags": ["TkshopSampleRequestController"], "summary": "删除索样策略", "operationId": "tkshopSampleRequestPolicyByIdDelete", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/sample-request/shopsToReview": {"get": {"tags": ["TkshopSampleRequestController"], "summary": "获取索样审批的店铺", "operationId": "tkshopSampleRequestShopsToReviewGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«SampleRequestShopVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/sample-request/sync": {"post": {"tags": ["TkshopSampleRequestController"], "summary": "同步索样记录", "operationId": "tkshopSampleRequestSyncPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncSampleRequestRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/sample-request/syncMedia": {"post": {"tags": ["TkshopSampleRequestController"], "summary": "同步索样记录的直播和视频", "operationId": "tkshopSampleRequestSyncMediaPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncSampleRequestMediaRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/sample-request/syncStatus": {"put": {"tags": ["TkshopSampleRequestController"], "summary": "同步索样记录状态", "operationId": "tkshopSampleRequestSyncStatusPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncSampleRequestStatusRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/searchProfile": {"post": {"tags": ["TkshopSearchProfileController"], "summary": "保存查询方案（新增或修改）", "operationId": "tkshopSearchProfilePost", "parameters": [{"name": "name", "in": "query", "description": "name", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "profileType", "in": "query", "description": "profileType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TkshopDeletingCreator", "TkshopFavoriteCreator", "TkshopGlobalCreator", "TkshopLive", "TkshopOrder", "TkshopProduct", "TkshopVideo"]}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkshopSearchProfileDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/searchProfile/list": {"get": {"tags": ["TkshopSearchProfileController"], "summary": "列出所有查询方案", "operationId": "tkshopSearchProfileListGet", "parameters": [{"name": "profileType", "in": "query", "description": "profileType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TkshopDeletingCreator", "TkshopFavoriteCreator", "TkshopGlobalCreator", "TkshopLive", "TkshopOrder", "TkshopProduct", "TkshopVideo"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TkshopSearchProfileDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/searchProfile/{id}": {"delete": {"tags": ["TkshopSearchProfileController"], "summary": "删除查询方案", "operationId": "tkshopSearchProfileByIdDelete", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/settings/creatorExpiredAutoTags": {"get": {"tags": ["TkshopSettingController"], "summary": "达人自动化标签配置", "operationId": "tkshopSettingsCreatorExpiredAutoTagsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkshopCreatorExpiredAutoTags»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "put": {"tags": ["TkshopSettingController"], "summary": "配置达人自动化标签", "operationId": "tkshopSettingsCreatorExpiredAutoTagsPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TkshopCreatorExpiredAutoTags"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/settings/getRpaBrowserPolicy": {"get": {"tags": ["TkshopSettingController"], "summary": "获取浏览器执行策略", "operationId": "tkshopSettingsGetRpaBrowserPolicyGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«GhRpaBrowserPolicy»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/settings/getTSDeviceBrowserConcurrent": {"get": {"tags": ["TkshopSettingController"], "summary": "获取用户登录设备的浏览器流程并发数（仅限tkshop触发的任务）", "operationId": "tkshopSettingsGetTSDeviceBrowserConcurrentGet", "parameters": [{"name": "deviceId", "in": "query", "description": "deviceId", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/settings/getTSSyncCreatorPolicy": {"get": {"tags": ["TkshopSettingController"], "summary": "获取同步达人基本信息的配置", "operationId": "tkshopSettingsGetTSSyncCreatorPolicyGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TSSyncCreatorPolicy»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/settings/setTSDeviceBrowserConcurrent": {"put": {"tags": ["TkshopSettingController"], "summary": "设置用户登录设备的浏览器流程并发数（仅限tkshop触发的任务）", "operationId": "tkshopSettingsSetTSDeviceBrowserConcurrentPut", "parameters": [{"name": "deviceId", "in": "query", "description": "deviceId", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "concurrent", "in": "query", "description": "concurrent", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/settings/updateRpaBrowserPolicy": {"put": {"tags": ["TkshopSettingController"], "summary": "设置浏览器执行策略", "operationId": "tkshopSettingsUpdateRpaBrowserPolicyPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhRpaBrowserPolicy"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/settings/updateTSSyncCreatorPolicy": {"put": {"tags": ["TkshopSettingController"], "summary": "更新同步达人基本信息的配置", "operationId": "tkshopSettingsUpdateTSSyncCreatorPolicyPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TSSyncCreatorPolicy"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/shop/getLastSyncTime": {"post": {"tags": ["TkshopShopController"], "summary": "获取店铺最后同步时间", "operationId": "tkshopShopGetLastSyncTimePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«Map«long,date-time»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/shop/loadTkshopInfo": {"post": {"tags": ["TkshopShopController"], "summary": "加载tkshop店铺信息", "operationId": "tkshopShopLoadTkshopInfoPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TkshopShopVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/shop/markSyncDone": {"get": {"tags": ["TkshopShopController"], "summary": "标记同步店铺结束", "operationId": "tkshopShopMarkSyncDoneGet", "parameters": [{"name": "shopId", "in": "query", "description": "shopId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/shop/page": {"post": {"tags": ["TkshopShopController"], "summary": "TKshop首页分页查询店铺", "operationId": "tkshopShopPagePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageTkshopRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«ShopHealthVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/shop/platforms": {"get": {"tags": ["TkshopShopController"], "summary": "当前团队所有tk店铺的平台对象", "operationId": "tkshopShopPlatformsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«ShopPlatformVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/shop/{id}/reportTkShopNo": {"put": {"tags": ["TkshopShopController"], "summary": "汇报TK店铺ID", "operationId": "tkshopShopByIdReportTkShopNoPut", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "coordinateId", "in": "query", "description": "coordinateId", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "shopNo", "in": "query", "description": "shopNo", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/shop/{shopId}/creatorInteractStat": {"get": {"tags": ["TkshopShopController"], "summary": "建联达人数据", "operationId": "tkshopShopByShopIdCreatorInteractStatGet", "parameters": [{"name": "shopId", "in": "path", "description": "shopId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "days", "in": "query", "description": "days", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CreatorInteractStatVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/shop/{shopId}/creatorStat": {"get": {"tags": ["TkshopShopController"], "summary": "达人统计", "operationId": "tkshopShopByShopIdCreatorStatGet", "parameters": [{"name": "shopId", "in": "path", "description": "shopId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ShopCreatorStatVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/shop/{shopId}/hasOrderCreator": {"get": {"tags": ["TkshopShopController"], "summary": "最近n天首次出单的达人", "operationId": "tkshopShopByShopIdHasOrderCreatorGet", "parameters": [{"name": "shopId", "in": "path", "description": "shopId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "days", "in": "query", "description": "days", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«TkshopCreatorDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/shop/{shopId}/healthDetail": {"get": {"tags": ["TkshopShopController"], "summary": "包含健康度的详情", "operationId": "tkshopShopByShopIdHealthDetailGet", "parameters": [{"name": "shopId", "in": "path", "description": "shopId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ShopHealthVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/shop/{shopId}/reportHealth": {"put": {"tags": ["TkshopShopController"], "summary": "汇报TK店铺健康状态", "operationId": "tkshopShopByShopIdReportHealthPut", "parameters": [{"name": "shopId", "in": "path", "description": "shopId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TkshopReportHealthRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/shop/{shopId}/shopLiveStat": {"get": {"tags": ["TkshopShopController"], "summary": "带货直播统计", "operationId": "tkshopShopByShopIdShopLiveStatGet", "parameters": [{"name": "shopId", "in": "path", "description": "shopId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ShopMediaStatVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/shop/{shopId}/shopSrStat": {"get": {"tags": ["TkshopShopController"], "summary": "索样记录", "operationId": "tkshopShopByShopIdShopSrStatGet", "parameters": [{"name": "shopId", "in": "path", "description": "shopId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ShopSrStatVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/shop/{shopId}/shopVideoStat": {"get": {"tags": ["TkshopShopController"], "summary": "带货视频统计", "operationId": "tkshopShopByShopIdShopVideoStatGet", "parameters": [{"name": "shopId", "in": "path", "description": "shopId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ShopMediaStatVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/calcBuyTkshop": {"post": {"tags": ["TkshopSystemController"], "summary": "计算购买Tkshop的价格", "operationId": "tkshopCalcBuyTkshopPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBuyTkshopRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CalcTkshopPriceResponse»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/calcUpgradeTkshop": {"post": {"tags": ["TkshopSystemController"], "summary": "计算购买Tkshop的价格", "operationId": "tkshopCalcUpgradeTkshopPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBuyTkshopRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CalcTkshopPriceResponse»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/createBuyTkshop": {"post": {"tags": ["TkshopSystemController"], "summary": "创建购买Tkshop订单", "operationId": "tkshopCreateBuyTkshopPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBuyTkshopRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CreateBuyTkPackOrderResponse»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/createByTrialCode": {"get": {"tags": ["TkshopSystemController"], "summary": "根据试用码创建套餐", "operationId": "tkshopCreateByTrialCodeGet", "parameters": [{"name": "code", "in": "query", "description": "code", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TeamDto»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/createRenewTkshop": {"post": {"tags": ["TkshopSystemController"], "summary": "创建续费Tkshop订单", "operationId": "tkshopCreateRenewTkshopPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBuyTkshopRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CreateOrderResponse»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/createUpgradeTkshop": {"post": {"tags": ["TkshopSystemController"], "summary": "创建升级Tkshop订单", "operationId": "tkshopCreateUpgradeTkshopPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBuyTkshopRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CreateOrderResponse»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/downgrade": {"put": {"tags": ["TkshopSystemController"], "summary": "将当前Tkshop团队降级为普通团队", "operationId": "tkshopDowngradePut", "parameters": [{"name": "confirm", "in": "query", "description": "confirm", "required": true, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/systemConfig": {"get": {"tags": ["TkshopSystemController"], "summary": "获取子系统配置", "operationId": "tkshopSystemConfigGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkshopConfig»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/systemStatus": {"get": {"tags": ["TkshopSystemController"], "summary": "查询当前团队状态", "operationId": "tkshopSystemStatusGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkshopSystemStatusVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/teamConfig": {"get": {"tags": ["TkshopSystemController"], "summary": "获取团队通用配置", "operationId": "tkshopTeamConfigGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TeamTkshopConfig»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "put": {"tags": ["TkshopSystemController"], "summary": "设置团队通用配置", "operationId": "tkshopTeamConfigPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TeamTkshopConfig"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/trialCode": {"get": {"tags": ["TkshopSystemController"], "summary": "查询试用码", "operationId": "tkshopTrialCodeGet", "parameters": [{"name": "code", "in": "query", "description": "code", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkshopTrialCodeDto»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/updateAllocateCreatorEnabled": {"put": {"tags": ["TkshopSystemController"], "summary": "开通/关闭达人分配功能", "operationId": "tkshopUpdateAllocateCreatorEnabledPut", "parameters": [{"name": "enabled", "in": "query", "description": "enabled", "required": true, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/taskDrawer": {"post": {"tags": ["TkshopTaskDrawerController"], "summary": "追加抽屉任务（返回跳过的记录）", "operationId": "tkshopTaskDrawerPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddTaskDrawerRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TkshopTaskDrawerDto»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/taskDrawer/batchDelete": {"post": {"tags": ["TkshopTaskDrawerController"], "summary": "批量删除抽屉任务", "operationId": "tkshopTaskDrawerBatchDeletePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/taskDrawer/{id}": {"delete": {"tags": ["TkshopTaskDrawerController"], "summary": "deleteTaskDrawer", "operationId": "tkshopTaskDrawerByIdDelete", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/taskDrawers": {"get": {"tags": ["TkshopTaskDrawerController"], "summary": "查询用户的任务抽屉", "operationId": "tkshopTaskDrawersGet", "parameters": [{"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«TkshopTaskDrawerVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/video/avatar": {"get": {"tags": ["TkshopVideoController"], "summary": "查询视频的封面URL", "operationId": "tkshopVideoAvatarGet", "parameters": [{"name": "creatorId", "in": "query", "description": "creatorId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "mediaId", "in": "query", "description": "mediaId", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/video/cancelSync": {"post": {"tags": ["TkshopVideoController"], "summary": "取消同步视频", "operationId": "tkshopVideoCancelSyncPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/video/creator/page": {"post": {"tags": ["TkshopVideoController"], "summary": "查询带视频的达人", "operationId": "tkshopVideoCreatorPagePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageVideoCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«TkshopCreatorProductVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/video/detail": {"get": {"tags": ["TkshopVideoController"], "summary": "返回视频的详情", "operationId": "tkshopVideoDetailGet", "parameters": [{"name": "creatorId", "in": "query", "description": "creatorId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "mediaId", "in": "query", "description": "mediaId", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkshopVideoVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/video/listBrief": {"post": {"tags": ["TkshopVideoController"], "summary": "全量查询视频简介", "operationId": "tkshopVideoListBriefPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageVideoRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«VideoBriefVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/video/markSync": {"post": {"tags": ["TkshopVideoController"], "summary": "标记同步视频", "operationId": "tkshopVideoMarkSyncPost", "parameters": [{"name": "force", "in": "query", "description": "force", "required": false, "style": "form", "schema": {"type": "boolean"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/video/page": {"post": {"tags": ["TkshopVideoController"], "summary": "分页查询视频", "operationId": "tkshopVideoPagePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageVideoRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«TkshopVideoVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/video/putOssUrl": {"get": {"tags": ["TkshopVideoController"], "summary": "获取视频预签名直传oss的URL", "operationId": "tkshopVideoPutOssUrlGet", "parameters": [{"name": "mediaId", "in": "query", "description": "mediaId", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "ext", "in": "query", "description": "ext", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/video/remark": {"put": {"tags": ["TkshopVideoController"], "summary": "更新备注", "operationId": "tkshopVideoRemarkPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRemarkRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/video/scrapTest2": {"get": {"tags": ["TkshopVideoController"], "summary": "scrapVideo2", "operationId": "tkshopVideoScrapTest2Get", "parameters": [{"name": "mediaId", "in": "query", "description": "mediaId", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/video/shops": {"get": {"tags": ["TkshopVideoController"], "summary": "查询带视频的店铺", "operationId": "tkshopVideoShopsGet", "parameters": [{"name": "query", "in": "query", "description": "query", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«long»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/video/syncAdCode": {"post": {"tags": ["TkshopVideoController"], "summary": "同步视频投流码", "operationId": "tkshopVideoSyncAdCodePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncVideoAdCodeRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/video/syncBatch": {"post": {"tags": ["TkshopVideoController"], "summary": "批量同步视频", "operationId": "tkshopVideoSyncBatchPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncCreatorVideoRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/video/url": {"get": {"tags": ["TkshopVideoController"], "summary": "获取tk视频的播放地址V2", "operationId": "tkshopVideoUrlGet", "parameters": [{"name": "mediaId", "in": "query", "description": "mediaId", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "refresh", "in": "query", "description": "refresh", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/video/{id}/adCode": {"get": {"tags": ["TkshopVideoController"], "summary": "手工更新视频的投流码", "operationId": "tkshopVideoByIdAdCodeGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "adCode", "in": "query", "description": "adCode", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/video/{id}/scrapAvatar": {"get": {"tags": ["TkshopVideoController"], "summary": "scrapAvatar", "operationId": "tkshopVideoByIdScrapAvatarGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/video/{id}/scrapTest1": {"get": {"tags": ["TkshopVideoController"], "summary": "scrapVideo", "operationId": "tkshopVideoByIdScrapTest1Get", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TKVideo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tkshop/video/{id}/statList": {"get": {"tags": ["TkshopVideoController"], "summary": "视频的热力趋势数据", "operationId": "tkshopVideoByIdStatListGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TkshopVideoStatDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}}, "components": {"schemas": {"AddFriendshipVo": {"title": "AddFriendshipVo", "type": "object", "properties": {"contact": {"type": "string", "description": "联系方式"}, "contactType": {"type": "string", "description": "类型", "enum": ["email", "f<PERSON><PERSON><PERSON><PERSON><PERSON>", "line", "phone", "viber", "whatsapp", "zalo"]}, "error": {"type": "string", "description": "添加时产生的异常"}, "mobileAccount": {"type": "string", "description": "手机账号"}, "mobileId": {"type": "integer", "description": "手机ID", "format": "int64"}, "status": {"type": "string", "description": "联系方式状态", "enum": ["Error", "NotFound", "Ready", "Unknown"]}, "targetId": {"type": "integer", "description": "达人或买家ID", "format": "int64"}}}, "AddSampleRequestPolicyRequest": {"title": "AddSampleRequestPolicyRequest", "type": "object", "properties": {"approved": {"type": "boolean"}, "conditionList": {"type": "array", "items": {"$ref": "#/components/schemas/SampleRequestConditionVo"}}, "logicalCondition": {"type": "string", "enum": ["AND", "NOT", "OR"]}, "policyDesc": {"type": "string"}, "policyName": {"type": "string"}}}, "AddShopToPlanLayoutRequest": {"title": "AddShopToPlanLayoutRequest", "type": "object", "properties": {"description": {"type": "string", "description": "描述，暂时还用不上"}, "layoutId": {"type": "integer", "format": "int64"}, "shopId": {"type": "integer", "format": "int64"}}}, "AddTaskDrawerItem": {"title": "AddTaskDrawerItem", "type": "object", "properties": {"accountId": {"type": "integer", "format": "int64"}, "creatorId": {"type": "integer", "format": "int64"}, "parameter": {"type": "string"}, "rpaType": {"type": "string", "enum": ["Browser", "Extension", "IOS", "Mobile"]}, "taskKey": {"type": "string"}, "taskName": {"type": "string"}, "taskType": {"type": "string", "enum": ["TS_IMChatByFilter", "TS_IMChatByHandle", "TS_SampleApprove", "TS_SendBuyerIMChat", "TS_SendLine", "TS_SendWhatsApp", "TS_SyncBuyerInfo", "TS_SyncCreator", "TS_SyncOrders", "TS_SyncSampleCreator", "TS_SyncShopInfo", "TS_SyncVideos", "TS_TargetPlanByFilter", "TS_TargetPlanByHandle", "TS_VideoADCode"]}}}, "AddTaskDrawerRequest": {"title": "AddTaskDrawerRequest", "type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/AddTaskDrawerItem"}}}}, "AddTkshopInteractionRequest": {"title": "AddTkshopInteractionRequest", "type": "object", "properties": {"description": {"type": "string", "description": "交互的描述"}, "extraInfo": {"type": "object", "description": "额外信息，转化成JSON总长度不要超过8000"}, "flowId": {"type": "integer", "description": "操作流程", "format": "int64"}, "handle": {"type": "string", "description": "达人handle，必填"}, "interactType": {"type": "string", "description": "交互类型，必填", "enum": ["add_friend", "collaborating", "has_order", "im_chat", "manual_remark", "sample_request", "send_email", "send_facebook", "send_line", "send_whatsapp", "send_zalo", "target_plan"]}, "jobId": {"type": "integer", "description": "任务ID", "format": "int64"}, "region": {"type": "string", "description": "达人所属区域"}, "shopId": {"type": "integer", "description": "店铺ID", "format": "int64"}, "targetId": {"type": "integer", "format": "int64"}, "taskId": {"type": "integer", "description": "流程任务ID", "format": "int64"}, "timestamp": {"type": "integer", "format": "int64"}}}, "AddTkshopInteractionsRequest": {"title": "AddTkshopInteractionsRequest", "type": "object", "properties": {"creators": {"type": "array", "items": {"$ref": "#/components/schemas/HandleRegionVo"}}, "description": {"type": "string", "description": "交互的描述"}, "extraInfo": {"type": "object", "description": "额外信息，转化成JSON总长度不要超过8000"}, "flowId": {"type": "integer", "description": "操作流程", "format": "int64"}, "interactType": {"type": "string", "description": "交互类型，必填", "enum": ["add_friend", "collaborating", "has_order", "im_chat", "manual_remark", "sample_request", "send_email", "send_facebook", "send_line", "send_whatsapp", "send_zalo", "target_plan"]}, "jobId": {"type": "integer", "description": "任务ID", "format": "int64"}, "shopId": {"type": "integer", "description": "店铺ID", "format": "int64"}, "targetId": {"type": "integer", "format": "int64"}, "taskId": {"type": "integer", "description": "流程任务ID", "format": "int64"}, "timestamp": {"type": "integer", "format": "int64"}}}, "AddTkshopManualRemarkRequest": {"title": "AddTkshopManualRemarkRequest", "type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "interactTime": {"type": "string", "format": "date-time"}, "remark": {"type": "string"}}}, "ApplyBestPracticeRequest": {"title": "ApplyBestPracticeRequest", "type": "object", "properties": {"follows": {"type": "array", "description": "后续的触发计划，一个跟着一个，但不需要指定parentId", "items": {"$ref": "#/components/schemas/CreateTkshopFollowingPlanRequest"}}, "header": {"description": "定时计划，不需要再冗余指定planGroupId", "$ref": "#/components/schemas/创建一个tkshop定时计划"}, "planGroupId": {"type": "integer", "description": "所属的分组", "format": "int64"}}}, "BankPayConfig": {"title": "BankPayConfig", "type": "object", "properties": {"accountName": {"type": "string"}, "accountNumber": {"type": "string"}, "bankName": {"type": "string"}}}, "BatchSyncBuyerInfoRequest": {"title": "BatchSyncBuyerInfoRequest", "type": "object", "properties": {"deviceId": {"type": "string", "description": "必须要指定在哪个客户端上执行"}, "shopBuyers": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/ShopBuyerBean"}}}}, "BatchSyncCreatorRequest": {"title": "BatchSyncCreatorRequest", "type": "object", "properties": {"deviceId": {"type": "string", "description": "必须要指定在哪个客户端上执行"}, "shopCreators": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/ShopCreatorBean"}, "description": "shopId -> [ghCreatorId, ...]"}}}, "BatchSyncSampleCreatorRequest": {"title": "BatchSyncSampleCreatorRequest", "type": "object", "properties": {"advanceSettings": {"type": "object"}, "deviceId": {"type": "string", "description": "必须要指定在哪个客户端上执行"}, "shopIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "BatchSyncVideoRequest": {"title": "BatchSyncVideoRequest", "type": "object", "properties": {"advanceSettings": {"type": "object"}, "deviceId": {"type": "string", "description": "必须要指定在哪个客户端上执行"}, "shopId": {"type": "integer", "format": "int64"}, "videoIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "BatchUpdateMessageConfigRequest": {"title": "BatchUpdateMessageConfigRequest", "type": "object", "properties": {"config": {"$ref": "#/components/schemas/MobileAccountMessageConfig"}, "mobileAccountIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "CalcTkshopPriceResponse": {"title": "CalcTkshopPriceResponse", "type": "object", "properties": {"discount": {"type": "number", "description": "折扣,[0-1]", "format": "double"}, "discountAmount": {"type": "number", "description": "打折减掉的金额(如果是打折的话)", "format": "bigdecimal"}, "goodsId": {"type": "integer", "format": "int64"}, "items": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/ItemPriceInfo"}, "description": "记录每个item的价格及折扣或赠送信息，<key=goodsId, value=PriceInfo>"}, "payablePrice": {"type": "number", "description": "订单应付价(减掉了打折等信息)", "format": "bigdecimal"}, "presentAmount": {"type": "number", "description": "赠送金额，目前只出现在购买花瓣", "format": "bigdecimal"}, "totalCost": {"type": "number", "description": "订单总成本", "format": "bigdecimal"}, "totalPrice": {"type": "number", "description": "订单总价(原价)", "format": "bigdecimal"}, "unitPrice": {"type": "number", "format": "bigdecimal"}}}, "CheckInvitationVo": {"title": "CheckInvitationVo", "type": "object", "properties": {"planId": {"type": "string"}, "updateTime": {"type": "integer", "format": "int64"}}}, "CommonIdsRequest": {"title": "CommonIdsRequest", "type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "ContactQueryVo": {"title": "ContactQueryVo", "type": "object", "properties": {"accountId": {"type": "integer", "format": "int64"}, "contact": {"type": "string"}, "contactType": {"type": "string"}, "empty": {"type": "boolean", "description": "制定项为空", "example": false}, "status": {"type": "string", "enum": ["Error", "NotFound", "Ready", "Unknown"]}}}, "CreateBuyTkPackOrderResponse": {"title": "CreateBuyTkPackOrderResponse", "type": "object", "properties": {"bankAccount": {"type": "string"}, "bankAccountName": {"type": "string"}, "bankName": {"type": "string"}, "bankRemark": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "deductedPrice": {"type": "number", "description": "扣减金额", "format": "bigdecimal"}, "orderId": {"type": "integer", "format": "int64"}, "payOutContent": {"type": "string", "description": "支付输出的内容"}, "payOutType": {"type": "string", "description": "支付输出的内容类型"}, "payStatus": {"type": "string", "description": "如果不需要现金支付，该订单状态会直接变成已支付", "enum": ["CANCELED", "Created", "Locked", "PAID", "PartialRefund", "REFUNDED", "WAIT_CONFIRM"]}, "payType": {"type": "string", "description": "支付方式", "enum": ["AliPay", "BalancePay", "BankPay", "WechatPay"]}, "realPrice": {"type": "number", "description": "需要现金支付的money", "format": "bigdecimal"}, "salesReduction": {"type": "number", "format": "bigdecimal"}, "serialNumber": {"type": "string"}, "team": {"description": "为订单创建的临时团队", "$ref": "#/components/schemas/TeamDto"}}}, "CreateBuyTkshopRequest": {"title": "CreateBuyTkshopRequest", "type": "object", "properties": {"agreement": {"type": "boolean", "description": "已经勾选阅读和同意使用协议，没什么用", "example": false}, "balanceAmount": {"type": "number", "description": "余额抵扣金额", "format": "bigdecimal"}, "discount": {"type": "number", "description": "折扣[0-1]", "format": "double"}, "distributionCode": {"type": "string", "description": "优惠码"}, "distributionInfo": {"$ref": "#/components/schemas/DistributionInfo"}, "duration": {"type": "integer", "description": "购买时长，根据 periodUnit的值 有可能是月，周或天", "format": "int32"}, "immediatePay": {"type": "boolean", "description": "是否立即支付（点稍候支付该属性传false）", "example": false}, "payType": {"type": "string", "enum": ["AliPay", "BalancePay", "BankPay", "WechatPay"]}, "periodUnit": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}, "shopCount": {"type": "integer", "format": "int32"}, "version": {"type": "string", "enum": ["TkshopEnterprise", "TkshopStandard"]}, "voucherAmount": {"type": "number", "description": "代金券抵扣金额，不得大于代金券余额", "format": "bigdecimal"}, "voucherId": {"type": "integer", "description": "要使用的代金券id", "format": "int64"}}}, "CreateOrderResponse": {"title": "CreateOrderResponse", "type": "object", "properties": {"bankAccount": {"type": "string"}, "bankAccountName": {"type": "string"}, "bankName": {"type": "string"}, "bankRemark": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "deductedPrice": {"type": "number", "description": "扣减金额", "format": "bigdecimal"}, "orderId": {"type": "integer", "format": "int64"}, "payOutContent": {"type": "string", "description": "支付输出的内容"}, "payOutType": {"type": "string", "description": "支付输出的内容类型"}, "payStatus": {"type": "string", "description": "如果不需要现金支付，该订单状态会直接变成已支付", "enum": ["CANCELED", "Created", "Locked", "PAID", "PartialRefund", "REFUNDED", "WAIT_CONFIRM"]}, "payType": {"type": "string", "description": "支付方式", "enum": ["AliPay", "BalancePay", "BankPay", "WechatPay"]}, "realPrice": {"type": "number", "description": "需要现金支付的money", "format": "bigdecimal"}, "salesReduction": {"type": "number", "format": "bigdecimal"}, "serialNumber": {"type": "string"}}}, "CreatePlanChainLayoutRequest": {"title": "CreatePlanChainLayoutRequest", "type": "object", "properties": {"description": {"type": "string"}, "deviceId": {"type": "string"}, "name": {"type": "string"}}}, "CreateTkshopFollowingPlanRequest": {"title": "CreateTkshopFollowingPlanRequest", "type": "object", "properties": {"bizScene": {"type": "string", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}, "creatorFilter": {"type": "string", "description": "达人筛选条件，json格式"}, "delayTime": {"type": "integer", "description": "如果是触发计划，触发的延时时间", "format": "int32"}, "enabled": {"type": "boolean"}, "jobType": {"type": "string", "enum": ["Acco<PERSON><PERSON><PERSON><PERSON>", "AccountHealthCheck", "AccountMaintenance", "AccountMessageCheck", "KOL_LiveRewards", "<PERSON><PERSON><PERSON><PERSON>", "SendInvite", "SendPm", "SendUserCard", "ShareVideo", "<PERSON><PERSON><PERSON>", "SyncContactToPhone", "SyncContacts", "SyncCreator", "SyncLiveStream", "SyncPm", "TS_AddContacts", "TS_AddFriends", "TS_DemandPayment", "TS_IMChatByFilter", "TS_IMChatByHandle", "TS_LoginCheck", "TS_SampleApprove", "TS_SendBuyerIMChat", "TS_SendEmail", "TS_SendFacebook", "TS_SendLine", "TS_SendWhatsApp", "TS_SendZalo", "TS_SyncBuyerInfo", "TS_SyncCreator", "TS_SyncOrders", "TS_SyncProducts", "TS_SyncSampleCreator", "TS_SyncShopInfo", "TS_SyncVideos", "TS_TargetPlanByFilter", "TS_TargetPlanByHandle", "TS_TargetPlanClear", "TS_VideoADCode"]}, "params": {"type": "string"}, "parentId": {"type": "integer", "description": "如果是触发计划，父计划的ID", "format": "int64"}, "triggerPolicy": {"type": "string", "enum": ["Always", "OnSuccess"]}}}, "CreatorDetailDocument": {"title": "CreatorDetailDocument", "type": "object", "properties": {"alias": {"type": "string"}, "avatar": {"type": "string"}, "bio": {"type": "string"}, "categories": {"type": "array", "items": {"type": "string"}}, "cid": {"type": "string", "description": "店铺端cid"}, "contactMap": {"type": "object", "additionalProperties": {"type": "string"}}, "contentType": {"type": "string", "enum": ["All", "LIVE", "Video"]}, "creatorId": {"type": "string", "description": "媒体端creator_id"}, "followerCnt": {"type": "integer", "format": "int32"}, "handle": {"type": "string"}, "region": {"type": "string"}, "statMap": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/CreatorStatVo"}}, "status": {"type": "string", "enum": ["Collaborating", "HasOrder", "NotContacted", "<PERSON><PERSON>"]}}}, "CreatorInteractStatVo": {"title": "CreatorInteractStatVo", "type": "object", "properties": {"collaboratingCount": {"type": "integer", "description": "合作中", "format": "int32"}, "imChatCount": {"type": "integer", "description": "站内消息数量", "format": "int32"}, "targetPlanCount": {"type": "integer", "description": "定向邀约数量", "format": "int32"}}}, "CreatorMediaVo": {"title": "CreatorMediaVo", "type": "object", "properties": {"endTimestamp": {"type": "integer", "format": "int64"}, "estCommission": {"type": "number", "format": "double"}, "gmv": {"type": "number", "format": "double"}, "itemsSold": {"type": "integer", "format": "int32"}, "mediaAvatar": {"type": "string"}, "mediaId": {"type": "string"}, "mediaName": {"type": "string"}, "mediaUrl": {"type": "string"}, "postTimestamp": {"type": "integer", "format": "int64"}, "startTimestamp": {"type": "integer", "format": "int64"}, "unit": {"type": "string"}}}, "CreatorProductVo": {"title": "CreatorProductVo", "type": "object", "properties": {"estCommission": {"type": "number", "format": "double"}, "gmv": {"type": "number", "format": "double"}, "itemsSold": {"type": "integer", "format": "int32"}, "productAvatar": {"type": "string"}, "productName": {"type": "string"}, "productNo": {"type": "string"}, "unit": {"type": "string"}}}, "CreatorShopDocument": {"title": "CreatorShopDocument", "type": "object", "properties": {"analysisCreatorId": {"type": "string", "description": "分析模块creator_id"}, "avatar": {"type": "string"}, "cid": {"type": "string", "description": "店铺端cid"}, "creatorId": {"type": "string", "description": "媒体端creator_id"}, "handle": {"type": "string"}, "lives": {"type": "array", "items": {"$ref": "#/components/schemas/CreatorMediaVo"}}, "products": {"type": "array", "items": {"$ref": "#/components/schemas/CreatorProductVo"}}, "region": {"type": "string"}, "videos": {"type": "array", "items": {"$ref": "#/components/schemas/CreatorMediaVo"}}}}, "CreatorStatRangeVo": {"title": "CreatorStatRangeVo", "type": "object", "properties": {"dimension": {"type": "string"}, "from": {"type": "number", "format": "double"}, "to": {"type": "number", "format": "double"}}}, "CreatorStatVo": {"title": "CreatorStatVo", "type": "object", "properties": {"unit": {"type": "string"}, "value": {"type": "number", "format": "double"}}}, "CreatorVideoDocument": {"title": "CreatorVideoDocument", "type": "object", "properties": {"commentCnt": {"type": "integer", "description": "评论数", "format": "int32"}, "creatorId": {"type": "string", "description": "媒体端creator_id"}, "duration": {"type": "integer", "description": "时长：秒", "format": "int32"}, "favoriteCnt": {"type": "integer", "description": "收藏数", "format": "int32"}, "handle": {"type": "string"}, "likeCnt": {"type": "integer", "description": "点赞数", "format": "int32"}, "mediaAvatar": {"type": "string", "description": "封面图片URL"}, "mediaId": {"type": "string", "description": "视频ID"}, "mediaName": {"type": "string", "description": "视频名称"}, "mediaUrl": {"type": "string", "description": "播放地址"}, "postTimestamp": {"type": "integer", "format": "int64"}, "region": {"type": "string"}, "retweetCnt": {"type": "integer", "description": "转发数", "format": "int32"}, "viewCnt": {"type": "integer", "description": "播放数", "format": "int32"}, "viewerCnt": {"type": "integer", "description": "观众数", "format": "int32"}}}, "DemandPaymentRequest": {"title": "DemandPaymentRequest", "type": "object", "properties": {"advanceSettings": {"type": "object"}, "deviceId": {"type": "string", "description": "必须要指定在哪个客户端上执行"}, "shopId": {"type": "integer", "format": "int64"}}}, "DiscountsVo": {"title": "DiscountsVo", "type": "object", "properties": {"amount": {"type": "integer", "description": "赠送数量或折扣百分比或阶梯折扣百分比", "format": "int32"}, "discountCode": {"type": "string", "description": "打折code"}, "discountType": {"type": "string", "description": "打折还是赠送", "enum": ["Discount", "LadderPrice", "Present"]}, "periodUnit": {"type": "string", "description": "周期或数量单位", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}, "remarks": {"type": "string", "description": "备注"}, "threshold": {"type": "integer", "description": "期数或数量", "format": "int32"}}}, "DistributionCodeDto": {"title": "DistributionCodeDto", "type": "object", "properties": {"amount": {"type": "number", "format": "bigdecimal"}, "code": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "description": {"type": "string"}, "discountId": {"type": "integer", "format": "int64"}, "distributionType": {"type": "string", "enum": ["Deduction", "Discount", "Official"]}, "distributor": {"type": "integer", "format": "int64"}, "goodsType": {"type": "string", "enum": ["Credit", "CreditPack", "ExclusiveIp", "FingerprintQuota", "IosDeveloperApprove", "Ip", "IpGo", "IpProxy", "MarketFlow", "None", "PluginPack", "PriceDifference", "ProxyTraffic", "RpaCaptcha", "RpaExecuteQuota", "RpaMobile", "RpaOpenAi", "RpaSendEmail", "RpaSendSms", "RpaSendWeChat", "Rpa_Voucher_Base", "Rpa_Voucher_Performance", "SharingIp", "ShopQuota", "ShopSecurityPolicy", "StorageQuota", "TeamMemberQuota", "Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TkPack", "TkPackTrail", "Tkshop", "TkshopEnterprise", "TkshopStandard", "Traffic", "TransitTraffic", "TransitTrafficV2", "UserExclusiveIp", "Voucher"]}, "id": {"type": "integer", "format": "int64"}, "limited": {"type": "boolean"}, "name": {"type": "string"}, "systemDefault": {"type": "boolean"}, "usageCount": {"type": "integer", "format": "int32"}, "usedCount": {"type": "integer", "format": "int32"}, "valid": {"type": "boolean"}, "validDays": {"type": "integer", "format": "int32"}}}, "DistributionInfo": {"title": "DistributionInfo", "type": "object", "properties": {"code": {"$ref": "#/components/schemas/DistributionCodeDto"}, "deductedPrice": {"type": "number", "format": "bigdecimal"}, "drawPrice": {"type": "number", "format": "bigdecimal"}}}, "DurationPriceVo": {"title": "DurationPriceVo", "type": "object", "properties": {"duration": {"type": "integer", "format": "int32"}, "rate": {"type": "number", "format": "double"}}}, "ExpressionSymbolVo": {"title": "ExpressionSymbolVo", "type": "object", "properties": {"desc": {"type": "string"}, "onlyNumber": {"type": "boolean", "description": "仅支持数字", "example": false}, "single": {"type": "boolean", "description": "不需要比较，单值", "example": false}, "symbol": {"type": "string", "enum": ["contains", "eq", "ge", "gt", "isEmpty", "le", "lt", "ne", "notContains", "notEmpty"]}}}, "FBUserProfile": {"title": "FBUserProfile", "type": "object", "properties": {"fbid": {"type": "string"}}}, "FilterBasicSyncedRequest": {"title": "FilterBasicSyncedRequest", "type": "object", "properties": {"days": {"type": "integer", "description": "若干天内", "format": "int32"}, "handles": {"type": "array", "description": "达人列表", "items": {"type": "string"}}}}, "FindTkshopCreatorRequest": {"title": "FindTkshopCreatorRequest", "type": "object", "properties": {"alias": {"type": "string"}, "basicSyncTimeBeforeDays": {"type": "integer", "format": "int32"}, "basicSyncTimeFrom": {"type": "string", "description": "基础信息更新时间from", "format": "date-time"}, "basicSyncTimeLastDays": {"type": "integer", "format": "int32"}, "basicSyncTimeTo": {"type": "string", "description": "基础信息更新时间to", "format": "date-time"}, "categoryIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "contactAllEmpty": {"type": "boolean", "description": "联系方式：全部为空", "example": false}, "contactNotAllEmpty": {"type": "boolean", "description": "联系方式：至少一项不为空", "example": false}, "contacts": {"type": "array", "items": {"$ref": "#/components/schemas/ContactQueryVo"}}, "containsTag": {"type": "boolean", "description": "包含指定标签。取false时，tagLc只能是OR", "example": false}, "contentType": {"type": "string", "enum": ["All", "LIVE", "Video"]}, "createTimeBeforeDays": {"type": "integer", "format": "int32"}, "createTimeFrom": {"type": "string", "format": "date-time"}, "createTimeLastDays": {"type": "integer", "format": "int32"}, "createTimeTo": {"type": "string", "format": "date-time"}, "deleting": {"type": "boolean"}, "filterByFavorite": {"type": "boolean"}, "followerCntFrom": {"type": "integer", "format": "int32"}, "followerCntTo": {"type": "integer", "format": "int32"}, "handle": {"type": "string"}, "hasCollaborating": {"type": "boolean", "description": "勾选已合作的店铺", "example": false}, "hasInteraction": {"type": "boolean", "description": "勾选交互过的店铺", "example": false}, "hasNewMsg": {"type": "boolean"}, "hasResponsibleUser": {"type": "boolean"}, "interactShopId": {"type": "integer", "description": "交互店铺ID", "format": "int64"}, "interactTypes": {"type": "array", "description": "包含以下沟通记录", "items": {"type": "string", "enum": ["add_friend", "collaborating", "has_order", "im_chat", "manual_remark", "sample_request", "send_email", "send_facebook", "send_line", "send_whatsapp", "send_zalo", "target_plan"]}}, "interactionText": {"type": "string", "description": "沟通记录包含：仅支持对手动记录、发送的即时消息内容进行检索"}, "lastSyncTimeBeforeDays": {"type": "integer", "format": "int32"}, "lastSyncTimeFrom": {"type": "string", "format": "date-time"}, "lastSyncTimeLastDays": {"type": "integer", "format": "int32"}, "lastSyncTimeTo": {"type": "string", "format": "date-time"}, "limit": {"type": "integer", "format": "int32"}, "liveStartTimeBeforeDays": {"type": "integer", "format": "int32"}, "liveStartTimeFrom": {"type": "string", "format": "date-time"}, "liveStartTimeLastDays": {"type": "integer", "format": "int32"}, "liveStartTimeTo": {"type": "string", "format": "date-time"}, "notStatus": {"type": "string", "enum": ["Collaborating", "HasOrder", "NotContacted", "<PERSON><PERSON>"]}, "productNo": {"type": "string", "description": "带货商品ID"}, "query": {"type": "string"}, "region": {"type": "string"}, "regions": {"type": "array", "items": {"type": "string"}}, "responsibleUserId": {"type": "integer", "format": "int64"}, "roiFrom": {"type": "number", "format": "double"}, "roiTo": {"type": "number", "format": "double"}, "shopId": {"type": "integer", "description": "合作店铺ID", "format": "int64"}, "statRanges": {"type": "array", "items": {"$ref": "#/components/schemas/CreatorStatRangeVo"}}, "statusList": {"type": "array", "items": {"type": "string", "enum": ["Collaborating", "HasOrder", "NotContacted", "<PERSON><PERSON>"]}}, "tagIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "tagKeys": {"type": "array", "description": "标签模版", "items": {"type": "string"}}, "tagLc": {"type": "string", "description": "标签的逻辑条件，支持OR|AND", "enum": ["AND", "NOT", "OR"]}, "textInteractTypes": {"type": "array", "description": "沟通文本代表的沟通类型", "items": {"type": "string", "enum": ["add_friend", "collaborating", "has_order", "im_chat", "manual_remark", "sample_request", "send_email", "send_facebook", "send_line", "send_whatsapp", "send_zalo", "target_plan"]}}, "totalCommissionFrom": {"type": "number", "format": "double"}, "totalCommissionTo": {"type": "number", "format": "double"}, "totalGmvFrom": {"type": "number", "format": "double"}, "totalGmvTo": {"type": "number", "format": "double"}, "totalItemsSoldFrom": {"type": "integer", "format": "int32"}, "totalItemsSoldTo": {"type": "integer", "format": "int32"}, "totalProductsFrom": {"type": "integer", "format": "int32"}, "totalProductsTo": {"type": "integer", "format": "int32"}, "videoPostTimeBeforeDays": {"type": "integer", "format": "int32"}, "videoPostTimeFrom": {"type": "string", "format": "date-time"}, "videoPostTimeLastDays": {"type": "integer", "format": "int32"}, "videoPostTimeTo": {"type": "string", "format": "date-time"}}}, "FindTkshopGlobalCreatorRequest": {"title": "FindTkshopGlobalCreatorRequest", "type": "object", "properties": {"alias": {"type": "string"}, "categoryIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "contactAllEmpty": {"type": "boolean", "description": "联系方式：全部为空", "example": false}, "contactNotAllEmpty": {"type": "boolean", "description": "联系方式：至少一项不为空", "example": false}, "contacts": {"type": "array", "items": {"$ref": "#/components/schemas/ContactQueryVo"}}, "createTimeFrom": {"type": "string", "format": "date-time"}, "createTimeTo": {"type": "string", "format": "date-time"}, "followerCntFrom": {"type": "integer", "format": "int32"}, "followerCntTo": {"type": "integer", "format": "int32"}, "handle": {"type": "string"}, "lastSyncTimeBeforeDays": {"type": "integer", "format": "int32"}, "lastSyncTimeFrom": {"type": "string", "format": "date-time"}, "lastSyncTimeLastDays": {"type": "integer", "format": "int32"}, "lastSyncTimeTo": {"type": "string", "format": "date-time"}, "limit": {"type": "integer", "format": "int32"}, "query": {"type": "string"}, "regions": {"type": "array", "items": {"type": "string"}}, "statRanges": {"type": "array", "items": {"$ref": "#/components/schemas/CreatorStatRangeVo"}}}}, "GhCreatorContactRequest": {"title": "GhCreatorContactRequest", "type": "object", "properties": {"contactType": {"type": "string", "enum": ["email", "f<PERSON><PERSON><PERSON><PERSON><PERSON>", "line", "phone", "viber", "whatsapp", "zalo"]}, "ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "GhCreatorHandleRequest": {"title": "GhCreatorHandleRequest", "type": "object", "properties": {"handles": {"type": "array", "items": {"type": "string"}}}}, "GhJobDto": {"title": "GhJobDto", "type": "object", "properties": {"bizScene": {"type": "string", "description": "什么类型的达人", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}, "clientId": {"type": "string"}, "createTime": {"type": "string", "description": "该条任务创建时间", "format": "date-time"}, "creatorId": {"type": "integer", "description": "创建者id", "format": "int64"}, "creatorName": {"type": "string", "description": "创建者昵称"}, "description": {"type": "string"}, "device": {"description": "执行设备", "$ref": "#/components/schemas/LoginDeviceDto"}, "deviceName": {"type": "string", "description": "执行设备的名称"}, "executeEndTime": {"type": "string", "description": "执行结束时间", "format": "date-time"}, "executeTime": {"type": "string", "description": "开始执行时间", "format": "date-time"}, "ghCreatorId": {"type": "integer", "description": "主播id", "format": "int64"}, "ghCreatorName": {"type": "string", "description": "主播昵称"}, "ghPlanId": {"type": "integer", "format": "int64"}, "ghPlatformType": {"type": "string", "description": "公会平台类型，tk、抖音、小红书", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}, "ghScheduleType": {"type": "string", "enum": ["GhBackstage", "Mobile", "Normal", "Official", "Union", "Unknown"]}, "id": {"type": "integer", "format": "int64"}, "jobType": {"type": "string", "enum": ["Acco<PERSON><PERSON><PERSON><PERSON>", "AccountHealthCheck", "AccountMaintenance", "AccountMessageCheck", "KOL_LiveRewards", "<PERSON><PERSON><PERSON><PERSON>", "SendInvite", "SendPm", "SendUserCard", "ShareVideo", "<PERSON><PERSON><PERSON>", "SyncContactToPhone", "SyncContacts", "SyncCreator", "SyncLiveStream", "SyncPm", "TS_AddContacts", "TS_AddFriends", "TS_DemandPayment", "TS_IMChatByFilter", "TS_IMChatByHandle", "TS_LoginCheck", "TS_SampleApprove", "TS_SendBuyerIMChat", "TS_SendEmail", "TS_SendFacebook", "TS_SendLine", "TS_SendWhatsApp", "TS_SendZalo", "TS_SyncBuyerInfo", "TS_SyncCreator", "TS_SyncOrders", "TS_SyncProducts", "TS_SyncSampleCreator", "TS_SyncShopInfo", "TS_SyncVideos", "TS_TargetPlanByFilter", "TS_TargetPlanByHandle", "TS_TargetPlanClear", "TS_VideoADCode"]}, "params": {"type": "string", "description": "任务参数，json格式"}, "planRunId": {"type": "string"}, "rpaTaskId": {"type": "integer", "format": "int64"}, "rpaType": {"type": "string", "enum": ["Browser", "Extension", "IOS", "Mobile"]}, "shopId": {"type": "integer", "description": "用来执行该job的分身", "format": "int64"}, "shopName": {"type": "string", "description": "用来执行该job的分身名"}, "status": {"type": "string", "description": "job状态", "enum": ["Cancelled", "Failed", "Pending", "Running", "Scheduled", "Succeed"]}, "subCounts": {"type": "integer", "description": "子任务个数，当任务为信息更新/私信更新时，根据该字段来显示 xxx个主播 ", "format": "int32"}, "teamId": {"type": "integer", "format": "int64"}}}, "GhRpaBrowserPolicy": {"title": "GhRpaBrowserPolicy", "type": "object", "properties": {"caseBrowserOpened": {"type": "string", "description": "当账号的浏览器已打开时的策略：stop | reopen | reuse"}, "closeBrowserOnEnd": {"type": "string", "description": "流程结束后是否关闭账号浏览器：close | keep | closeRpaBrowser"}, "showMouseTrack": {"type": "boolean", "description": "是否显示鼠标轨迹", "example": false}, "snapshot": {"type": "string", "enum": ["Node", "Not", "OnFail"]}}}, "HandleRegionVo": {"title": "HandleRegionVo", "type": "object", "properties": {"handle": {"type": "string"}, "region": {"type": "string"}}}, "HandlesRequest": {"title": "HandlesRequest", "type": "object", "properties": {"handles": {"type": "array", "description": "达人列表", "items": {"type": "string"}}}}, "HasInteractionVo": {"title": "HasInteractionVo", "type": "object", "properties": {"count": {"type": "integer", "format": "int32"}, "handle": {"type": "string"}}}, "IdHandleVo": {"title": "IdHandleVo", "type": "object", "properties": {"handle": {"type": "string"}, "id": {"type": "integer", "format": "int64"}}}, "ImportGlobalCreatorByIdsRequest": {"title": "ImportGlobalCreatorByIdsRequest", "type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "tags": {"type": "array", "items": {"type": "string"}}}}, "ImportTkshopGlobalCreatorRequest": {"title": "ImportTkshopGlobalCreatorRequest", "type": "object", "properties": {"alias": {"type": "string"}, "categoryIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "contactAllEmpty": {"type": "boolean", "description": "联系方式：全部为空", "example": false}, "contactNotAllEmpty": {"type": "boolean", "description": "联系方式：至少一项不为空", "example": false}, "contacts": {"type": "array", "items": {"$ref": "#/components/schemas/ContactQueryVo"}}, "createTimeFrom": {"type": "string", "format": "date-time"}, "createTimeTo": {"type": "string", "format": "date-time"}, "followerCntFrom": {"type": "integer", "format": "int32"}, "followerCntTo": {"type": "integer", "format": "int32"}, "handle": {"type": "string"}, "lastSyncTimeBeforeDays": {"type": "integer", "format": "int32"}, "lastSyncTimeFrom": {"type": "string", "format": "date-time"}, "lastSyncTimeLastDays": {"type": "integer", "format": "int32"}, "lastSyncTimeTo": {"type": "string", "format": "date-time"}, "limit": {"type": "integer", "format": "int32"}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "query": {"type": "string"}, "regions": {"type": "array", "items": {"type": "string"}}, "sortField": {"type": "string"}, "sortOrder": {"type": "string", "enum": ["asc", "desc"]}, "statRanges": {"type": "array", "items": {"$ref": "#/components/schemas/CreatorStatRangeVo"}}, "tags": {"type": "array", "items": {"type": "string"}}}}, "InvitationCreatorDocument": {"title": "InvitationCreatorDocument", "type": "object", "properties": {"creatorId": {"type": "string"}, "handle": {"type": "string"}, "region": {"type": "string"}}}, "InvitationDocument": {"title": "InvitationDocument", "type": "object", "properties": {"creatorCnt": {"type": "integer", "format": "int32"}, "creators": {"type": "array", "items": {"$ref": "#/components/schemas/InvitationCreatorDocument"}}, "endTime": {"type": "integer", "format": "int64"}, "message": {"type": "string"}, "name": {"type": "string"}, "planId": {"type": "string"}, "productCnt": {"type": "integer", "format": "int32"}, "products": {"type": "array", "items": {"$ref": "#/components/schemas/InvitationProductDocument"}}, "startTime": {"type": "integer", "format": "int64"}, "updateTime": {"type": "integer", "format": "int64"}}}, "InvitationProductDocument": {"title": "InvitationProductDocument", "type": "object", "properties": {"productAvatar": {"type": "string"}, "productName": {"type": "string"}, "productNo": {"type": "string"}}}, "IpPoolDto": {"title": "IpPoolDto", "type": "object", "properties": {"allocateStrategy": {"type": "string", "enum": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ByLoad", "ByOrder", "ByRandom"]}, "capacity": {"type": "integer", "format": "int32"}, "connectTransits": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "creator": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "domestic": {"type": "boolean"}, "enableWhitelist": {"type": "boolean"}, "exclusive": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "lastApiTime": {"type": "string", "format": "date-time"}, "lifetime": {"type": "integer", "format": "int32"}, "locationId": {"type": "integer", "format": "int64"}, "minApiInterval": {"type": "integer", "format": "int32"}, "minApiNum": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "produceFromTransit": {"type": "boolean"}, "produceStrategy": {"type": "string", "enum": ["ProduceOnEmpty", "ProduceOnHand", "ProduceOnRequest"]}, "produced": {"type": "integer", "format": "int32"}, "provider": {"type": "string"}, "releaseStrategy": {"type": "string", "enum": ["Discard", "Reuse"]}, "teamId": {"type": "integer", "format": "int64"}, "transitType": {"type": "string", "enum": ["Auto", "Direct", "Transit"]}, "transits": {"type": "string"}, "tunnelTypes": {"type": "string"}}}, "ItemPriceInfo": {"title": "ItemPriceInfo", "type": "object", "properties": {"costPrice": {"type": "number", "format": "bigdecimal"}, "currentValidEndTime": {"type": "string", "description": "当前过期时间", "format": "date-time"}, "discount": {"$ref": "#/components/schemas/DiscountsVo"}, "discountAmount": {"type": "number", "description": "打折减掉的金额，如果是打折的话", "format": "bigdecimal"}, "goodsId": {"type": "integer", "format": "int64"}, "payablePrice": {"type": "number", "description": "应付价格", "format": "bigdecimal"}, "presentAmount": {"type": "number", "description": "赠送数量，如果是赠送的话。目前只出现在购买花瓣", "format": "bigdecimal"}, "price": {"type": "number", "description": "item总价", "format": "bigdecimal"}, "validEndTime": {"type": "string", "description": "续费后到期时间", "format": "date-time"}}}, "LadderPriceRange": {"title": "LadderPriceRange", "type": "object", "properties": {"amount": {"type": "integer", "description": "阶梯折扣百分比（=原价*amount/100）", "format": "int32"}, "threshold": {"type": "integer", "description": "超过特定数量", "format": "int32"}}}, "LadderPriceVo": {"title": "LadderPriceVo", "type": "object", "properties": {"price": {"type": "number", "description": "阶梯报价", "format": "double"}, "quantity": {"type": "integer", "description": "特定的数量", "format": "int32"}}}, "LoginDeviceDto": {"title": "LoginDeviceDto", "type": "object", "properties": {"appId": {"type": "string"}, "appVersion": {"type": "string"}, "clientIp": {"type": "string"}, "clientLocation": {"type": "integer", "format": "int64"}, "cpus": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "deviceId": {"type": "string"}, "deviceType": {"type": "string", "enum": ["App", "Browser", "Extension", "HYRuntime", "RpaExecutor"]}, "domestic": {"type": "boolean"}, "hostName": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "ipDataId": {"type": "integer", "format": "int64"}, "lastActiveTime": {"type": "string", "format": "date-time"}, "lastCity": {"type": "string"}, "lastLogTime": {"type": "string", "format": "date-time"}, "lastRemoteIp": {"type": "string"}, "lastUserId": {"type": "integer", "format": "int64"}, "logUrl": {"type": "string"}, "mem": {"type": "integer", "format": "int64"}, "online": {"type": "boolean"}, "osName": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}, "userAgent": {"type": "string"}, "version": {"type": "string"}}}, "MarkProductLiveRequest": {"title": "MarkProductLiveRequest", "type": "object", "properties": {"productNos": {"type": "array", "description": "Live商品列表", "items": {"type": "string"}}, "shopId": {"type": "integer", "description": "分身ID", "format": "int64"}}}, "MobileAccountMessageConfig": {"title": "MobileAccountMessageConfig", "type": "object", "properties": {"addFriendsInterval": {"type": "integer", "description": "每批次添加好友的时间间隔，单位分钟", "format": "int32"}, "maxDayFriends": {"type": "integer", "description": "每日最多添加的好友数量", "format": "int32"}, "maxDayPm": {"type": "integer", "description": "每日最多发送的即时消息数量", "format": "int32"}, "maxTaskFriends": {"type": "integer", "description": "单账号每批次最多添加的好友数量", "format": "int32"}, "maxTaskPm": {"type": "integer", "description": "每批次最多发送的即时消息数量", "format": "int32"}, "sendPmInterval": {"type": "integer", "description": "每批次发送即时消息的时间间隔，单位分钟", "format": "int32"}}}, "OrderDetailVo": {"title": "OrderDetailVo", "type": "object", "properties": {"automatic": {"type": "boolean", "description": "是否为自动订单，例如自动续费订单", "example": false}, "balanceAmount": {"type": "number", "description": "余额抵扣金额", "format": "bigdecimal"}, "bankPayConfig": {"description": "如果是银行卡支付，则包含银行卡信息", "$ref": "#/components/schemas/BankPayConfig"}, "cashPayType": {"type": "string", "enum": ["AliPay", "BalancePay", "BankPay", "WechatPay"]}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "discountReason": {"type": "string"}, "earnedPartner": {"type": "integer", "format": "int64"}, "earnedPartnerDto": {"description": "钱款入账的代理商信息", "$ref": "#/components/schemas/PartnerDto"}, "id": {"type": "integer", "format": "int64"}, "lockTime": {"type": "string", "description": "订单锁定时间;订单锁定之后60分钟不支付会被取消掉", "format": "date-time"}, "nickname": {"type": "string"}, "orderType": {"type": "string", "enum": ["BuyGiftCard", "BuyIp", "BuyPluginPack", "BuyRpaVoucher", "BuyTkPack", "BuyTkshop", "BuyTraffic", "CashOut", "MakeupPriceDifference", "<PERSON><PERSON>", "OrderCancel", "PartnerBuyVoucher", "PartnerDraw", "PartnerRecharge", "PartnerRechargeCredit", "PartnerRenewRpaVoucher", "Present", "Recharge", "RechargeCredit", "Refund", "RenewIp", "RenewPluginPack", "RenewRpaVoucher", "RenewTkPack", "RenewTkshop", "UpgradeTkshop"]}, "parentOrderId": {"type": "integer", "description": "如果父订单不为空说明该订单被选中合并支付了", "format": "int64"}, "payStatus": {"type": "string", "enum": ["CANCELED", "Created", "Locked", "PAID", "PartialRefund", "REFUNDED", "WAIT_CONFIRM"]}, "payablePrice": {"type": "number", "description": "订单应付总额，比如说买一年打8折或者销售改价之后的价格", "format": "bigdecimal"}, "presentAmount": {"type": "number", "description": "充值赠送金额;只在充值订单有效", "format": "bigdecimal"}, "productionRemarks": {"type": "string", "description": "生产备注"}, "productionStatus": {"type": "string", "description": "生产状态", "enum": ["Finished", "NotStart", "ProduceError", "Producing", "ReFunded", "RefundError", "Refunding", "WaitReFund"]}, "realPrice": {"type": "number", "description": "实付金额，即现金支付金额;可开票金额以此为依据", "format": "bigdecimal"}, "salesReduction": {"type": "number", "description": "销售改价折现", "format": "bigdecimal"}, "serialNumber": {"type": "string"}, "totalPrice": {"type": "number", "description": "订单总额", "format": "bigdecimal"}, "voucherAmount": {"type": "number", "description": "代金券抵扣金额", "format": "bigdecimal"}, "voucherCardNumber": {"type": "string", "description": "代金券卡号"}}}, "OrderItemDto": {"title": "OrderItemDto", "type": "object", "properties": {"count": {"type": "integer", "format": "int32"}, "discountReason": {"type": "string"}, "extraInfo": {"type": "string"}, "goodsId": {"type": "integer", "format": "int64"}, "goodsPeriodUnit": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}, "goodsType": {"type": "string", "enum": ["Credit", "CreditPack", "ExclusiveIp", "FingerprintQuota", "IosDeveloperApprove", "Ip", "IpGo", "IpProxy", "MarketFlow", "None", "PluginPack", "PriceDifference", "ProxyTraffic", "RpaCaptcha", "RpaExecuteQuota", "RpaMobile", "RpaOpenAi", "RpaSendEmail", "RpaSendSms", "RpaSendWeChat", "Rpa_Voucher_Base", "Rpa_Voucher_Performance", "SharingIp", "ShopQuota", "ShopSecurityPolicy", "StorageQuota", "TeamMemberQuota", "Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TkPack", "TkPackTrail", "Tkshop", "TkshopEnterprise", "TkshopStandard", "Traffic", "TransitTraffic", "TransitTrafficV2", "UserExclusiveIp", "Voucher"]}, "id": {"type": "integer", "format": "int64"}, "itemPrice": {"type": "number", "format": "bigdecimal"}, "orderId": {"type": "integer", "format": "int64"}, "productionRemarks": {"type": "string"}, "productionStatus": {"type": "string", "enum": ["Finished", "NotStart", "ProduceError", "Producing", "ReFunded", "RefundError", "Refunding", "WaitReFund"]}, "resourceId": {"type": "integer", "format": "int64"}, "resourceName": {"type": "string"}, "resourceType": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}, "teamId": {"type": "integer", "format": "int64"}}}, "OrderProductStatVo": {"title": "OrderProductStatVo", "type": "object", "properties": {"amount": {"type": "number", "format": "bigdecimal"}, "currency": {"type": "string"}, "productAvatar": {"type": "string"}, "productName": {"type": "string"}, "productNo": {"type": "string"}, "quantity": {"type": "integer", "format": "int32"}, "shopId": {"type": "integer", "format": "int64"}, "unit": {"type": "string"}}}, "OrderStatusStatVo": {"title": "OrderStatusStatVo", "type": "object", "properties": {"amount": {"type": "number", "format": "bigdecimal"}, "count": {"type": "integer", "format": "int32"}, "currency": {"type": "string"}, "status": {"type": "string", "enum": ["cancellation", "completed", "fail_delivery", "pending", "shipped", "to_ship"]}, "unit": {"type": "string"}}}, "OrdersPmQueryVo": {"title": "OrdersPmQueryVo", "type": "object", "properties": {"abnormalSyncTimeDiff": {"type": "integer", "description": "订单同步时间和播放量更新时间相差大于（小时）", "format": "int32"}, "abnormalViewCnt": {"type": "integer", "description": "播放量小于", "format": "int32"}, "badRate": {"type": "number", "description": "较差区间的开始值", "format": "double"}, "goodRate": {"type": "number", "description": "优秀区间的开始值", "format": "double"}}}, "PageLiveRequest": {"title": "PageLiveRequest", "type": "object", "properties": {"creatorId": {"type": "integer", "format": "int64"}, "estCommissionFrom": {"type": "number", "format": "double"}, "estCommissionTo": {"type": "number", "format": "double"}, "gmvFrom": {"type": "number", "format": "double"}, "gmvTo": {"type": "number", "format": "double"}, "handle": {"type": "string", "description": "达人ID"}, "hasSampleRequest": {"type": "boolean", "description": "视频是否有索样记录", "example": false}, "itemsSoldFrom": {"type": "integer", "format": "int32"}, "itemsSoldTo": {"type": "integer", "format": "int32"}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "productNo": {"type": "string"}, "query": {"type": "string", "description": "标题和备注或MediaId或达人handle前缀"}, "searchShop": {"type": "boolean"}, "shopIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "sortField": {"type": "string"}, "sortOrder": {"type": "string", "enum": ["asc", "desc"]}, "srApplyId": {"type": "string", "description": "视频的索样记录ID是什么"}, "startTimeBeforeDays": {"type": "integer", "format": "int32"}, "startTimeFrom": {"type": "string", "format": "date-time"}, "startTimeLastDays": {"type": "integer", "format": "int32"}, "startTimeTo": {"type": "string", "format": "date-time"}}}, "PageResult«SampleRequestShopBriefVo»": {"title": "PageResult«SampleRequestShopBriefVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/SampleRequestShopBriefVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«ShopHealthVo»": {"title": "PageResult«ShopHealthVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/ShopHealthVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«TkshopBuyerDetailVo»": {"title": "PageResult«TkshopBuyerDetailVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopBuyerDetailVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«TkshopBuyerInteractionDto»": {"title": "PageResult«TkshopBuyerInteractionDto»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopBuyerInteractionDto"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«TkshopCreatorDetailVo»": {"title": "PageResult«TkshopCreatorDetailVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopCreatorDetailVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«TkshopCreatorDto»": {"title": "PageResult«TkshopCreatorDto»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopCreatorDto"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«TkshopCreatorLiveVo»": {"title": "PageResult«TkshopCreatorLiveVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopCreatorLiveVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«TkshopCreatorProductDto»": {"title": "PageResult«TkshopCreatorProductDto»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopCreatorProductDto"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«TkshopCreatorProductVo»": {"title": "PageResult«TkshopCreatorProductVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopCreatorProductVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«TkshopCreatorVideoVo»": {"title": "PageResult«TkshopCreatorVideoVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopCreatorVideoVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«TkshopGlobalCreatorDetailVo»": {"title": "PageResult«TkshopGlobalCreatorDetailVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopGlobalCreatorDetailVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«TkshopInteractionDto»": {"title": "PageResult«TkshopInteractionDto»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopInteractionDto"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«TkshopLiveVo»": {"title": "PageResult«TkshopLiveVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopLiveVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«TkshopOrderVo»": {"title": "PageResult«TkshopOrderVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopOrderVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«TkshopProductDto»": {"title": "PageResult«TkshopProductDto»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopProductDto"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«TkshopSampleRequestAuditVo»": {"title": "PageResult«TkshopSampleRequestAuditVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopSampleRequestAuditVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«TkshopSampleRequestDetailVo»": {"title": "PageResult«TkshopSampleRequestDetailVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopSampleRequestDetailVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«TkshopTaskDrawerVo»": {"title": "PageResult«TkshopTaskDrawerVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopTaskDrawerVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«TkshopVideoVo»": {"title": "PageResult«TkshopVideoVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopVideoVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageSampleRequestRequest": {"title": "PageSampleRequestRequest", "type": "object", "properties": {"applyId": {"type": "string"}, "creatorId": {"type": "integer", "format": "int64"}, "followerCntFrom": {"type": "integer", "format": "int32"}, "followerCntTo": {"type": "integer", "format": "int32"}, "includeMedia": {"type": "boolean"}, "needCreatorDetail": {"type": "boolean"}, "needProductDetail": {"type": "boolean"}, "notStatus": {"type": "string", "description": "排除的状态", "enum": ["Cancelled", "Completed", "Expired", "InProgress", "ReadyToShip", "Reject", "Shipped", "ToReview"]}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "query": {"type": "string"}, "region": {"type": "string"}, "shopId": {"type": "integer", "format": "int64"}, "shopIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "sortField": {"type": "string"}, "sortOrder": {"type": "string", "enum": ["asc", "desc"]}, "status": {"type": "string", "enum": ["Cancelled", "Completed", "Expired", "InProgress", "ReadyToShip", "Reject", "Shipped", "ToReview"]}, "statusList": {"type": "array", "items": {"type": "string", "enum": ["Cancelled", "Completed", "Expired", "InProgress", "ReadyToShip", "Reject", "Shipped", "ToReview"]}}, "tagIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "tagLc": {"type": "string", "description": "标签的逻辑条件，支持OR|AND", "enum": ["AND", "NOT", "OR"]}}}, "PageTkshopBuyerRequest": {"title": "PageTkshopBuyerRequest", "type": "object", "properties": {"commissionHandle": {"type": "string", "description": "带货达人ID"}, "contactAllEmpty": {"type": "boolean", "description": "联系方式：全部为空", "example": false}, "contactNotAllEmpty": {"type": "boolean", "description": "联系方式：至少一项不为空", "example": false}, "contacts": {"type": "array", "items": {"$ref": "#/components/schemas/ContactQueryVo"}}, "containsTag": {"type": "boolean", "description": "包含指定标签。取false时，tagLc只能是OR", "example": false}, "firstBuyTimeBeforeDays": {"type": "integer", "format": "int32"}, "firstBuyTimeFrom": {"type": "string", "format": "date-time"}, "firstBuyTimeLastDays": {"type": "integer", "format": "int32"}, "firstBuyTimeTo": {"type": "string", "format": "date-time"}, "interactTypes": {"type": "array", "description": "包含以下沟通记录", "items": {"type": "string", "enum": ["add_contact", "add_friend", "im_chat", "manual_remark", "send_email", "send_facebook", "send_line", "send_whatsapp", "send_zalo"]}}, "interactionText": {"type": "string", "description": "沟通记录包含：仅支持对手动记录、发送的即时消息内容进行检索"}, "lastBuyTimeBeforeDays": {"type": "integer", "format": "int32"}, "lastBuyTimeFrom": {"type": "string", "format": "date-time"}, "lastBuyTimeLastDays": {"type": "integer", "format": "int32"}, "lastBuyTimeTo": {"type": "string", "format": "date-time"}, "limit": {"type": "integer", "format": "int32"}, "orderAmountFrom": {"type": "number", "format": "bigdecimal"}, "orderAmountTo": {"type": "number", "format": "bigdecimal"}, "orderCountFrom": {"type": "integer", "format": "int32"}, "orderCountTo": {"type": "integer", "format": "int32"}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "productNo": {"type": "string", "description": "购买的产品ID"}, "query": {"type": "string"}, "regions": {"type": "array", "items": {"type": "string"}}, "shopId": {"type": "integer", "format": "int64"}, "sortField": {"type": "string"}, "sortOrder": {"type": "string", "enum": ["asc", "desc"]}, "tagIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "tagKeys": {"type": "array", "description": "标签模版", "items": {"type": "string"}}, "tagLc": {"type": "string", "description": "标签的逻辑条件，支持OR|AND", "enum": ["AND", "NOT", "OR"]}, "textInteractTypes": {"type": "array", "description": "沟通文本代表的沟通类型", "items": {"type": "string", "enum": ["add_contact", "add_friend", "im_chat", "manual_remark", "send_email", "send_facebook", "send_line", "send_whatsapp", "send_zalo"]}}}}, "PageTkshopCreatorRequest": {"title": "PageTkshopCreatorRequest", "type": "object", "properties": {"alias": {"type": "string"}, "basicSyncTimeBeforeDays": {"type": "integer", "format": "int32"}, "basicSyncTimeFrom": {"type": "string", "description": "基础信息更新时间from", "format": "date-time"}, "basicSyncTimeLastDays": {"type": "integer", "format": "int32"}, "basicSyncTimeTo": {"type": "string", "description": "基础信息更新时间to", "format": "date-time"}, "categoryIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "contactAllEmpty": {"type": "boolean", "description": "联系方式：全部为空", "example": false}, "contactNotAllEmpty": {"type": "boolean", "description": "联系方式：至少一项不为空", "example": false}, "contacts": {"type": "array", "items": {"$ref": "#/components/schemas/ContactQueryVo"}}, "containsTag": {"type": "boolean", "description": "包含指定标签。取false时，tagLc只能是OR", "example": false}, "contentType": {"type": "string", "enum": ["All", "LIVE", "Video"]}, "createTimeBeforeDays": {"type": "integer", "format": "int32"}, "createTimeFrom": {"type": "string", "format": "date-time"}, "createTimeLastDays": {"type": "integer", "format": "int32"}, "createTimeTo": {"type": "string", "format": "date-time"}, "deleting": {"type": "boolean"}, "filterByFavorite": {"type": "boolean"}, "followerCntFrom": {"type": "integer", "format": "int32"}, "followerCntTo": {"type": "integer", "format": "int32"}, "handle": {"type": "string"}, "hasCollaborating": {"type": "boolean", "description": "勾选已合作的店铺", "example": false}, "hasInteraction": {"type": "boolean", "description": "勾选交互过的店铺", "example": false}, "hasNewMsg": {"type": "boolean"}, "hasResponsibleUser": {"type": "boolean"}, "interactShopId": {"type": "integer", "description": "交互店铺ID", "format": "int64"}, "interactTypes": {"type": "array", "description": "包含以下沟通记录", "items": {"type": "string", "enum": ["add_friend", "collaborating", "has_order", "im_chat", "manual_remark", "sample_request", "send_email", "send_facebook", "send_line", "send_whatsapp", "send_zalo", "target_plan"]}}, "interactionText": {"type": "string", "description": "沟通记录包含：仅支持对手动记录、发送的即时消息内容进行检索"}, "lastSyncTimeBeforeDays": {"type": "integer", "format": "int32"}, "lastSyncTimeFrom": {"type": "string", "format": "date-time"}, "lastSyncTimeLastDays": {"type": "integer", "format": "int32"}, "lastSyncTimeTo": {"type": "string", "format": "date-time"}, "limit": {"type": "integer", "format": "int32"}, "liveStartTimeBeforeDays": {"type": "integer", "format": "int32"}, "liveStartTimeFrom": {"type": "string", "format": "date-time"}, "liveStartTimeLastDays": {"type": "integer", "format": "int32"}, "liveStartTimeTo": {"type": "string", "format": "date-time"}, "notStatus": {"type": "string", "enum": ["Collaborating", "HasOrder", "NotContacted", "<PERSON><PERSON>"]}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "productNo": {"type": "string", "description": "带货商品ID"}, "query": {"type": "string"}, "region": {"type": "string"}, "regions": {"type": "array", "items": {"type": "string"}}, "responsibleUserId": {"type": "integer", "format": "int64"}, "roiFrom": {"type": "number", "format": "double"}, "roiTo": {"type": "number", "format": "double"}, "shopId": {"type": "integer", "description": "合作店铺ID", "format": "int64"}, "sortField": {"type": "string"}, "sortOrder": {"type": "string", "enum": ["asc", "desc"]}, "statRanges": {"type": "array", "items": {"$ref": "#/components/schemas/CreatorStatRangeVo"}}, "statusList": {"type": "array", "items": {"type": "string", "enum": ["Collaborating", "HasOrder", "NotContacted", "<PERSON><PERSON>"]}}, "tagIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "tagKeys": {"type": "array", "description": "标签模版", "items": {"type": "string"}}, "tagLc": {"type": "string", "description": "标签的逻辑条件，支持OR|AND", "enum": ["AND", "NOT", "OR"]}, "textInteractTypes": {"type": "array", "description": "沟通文本代表的沟通类型", "items": {"type": "string", "enum": ["add_friend", "collaborating", "has_order", "im_chat", "manual_remark", "sample_request", "send_email", "send_facebook", "send_line", "send_whatsapp", "send_zalo", "target_plan"]}}, "totalCommissionFrom": {"type": "number", "format": "double"}, "totalCommissionTo": {"type": "number", "format": "double"}, "totalGmvFrom": {"type": "number", "format": "double"}, "totalGmvTo": {"type": "number", "format": "double"}, "totalItemsSoldFrom": {"type": "integer", "format": "int32"}, "totalItemsSoldTo": {"type": "integer", "format": "int32"}, "totalProductsFrom": {"type": "integer", "format": "int32"}, "totalProductsTo": {"type": "integer", "format": "int32"}, "videoPostTimeBeforeDays": {"type": "integer", "format": "int32"}, "videoPostTimeFrom": {"type": "string", "format": "date-time"}, "videoPostTimeLastDays": {"type": "integer", "format": "int32"}, "videoPostTimeTo": {"type": "string", "format": "date-time"}}}, "PageTkshopGlobalCreatorRequest": {"title": "PageTkshopGlobalCreatorRequest", "type": "object", "properties": {"alias": {"type": "string"}, "categoryIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "contactAllEmpty": {"type": "boolean", "description": "联系方式：全部为空", "example": false}, "contactNotAllEmpty": {"type": "boolean", "description": "联系方式：至少一项不为空", "example": false}, "contacts": {"type": "array", "items": {"$ref": "#/components/schemas/ContactQueryVo"}}, "createTimeFrom": {"type": "string", "format": "date-time"}, "createTimeTo": {"type": "string", "format": "date-time"}, "followerCntFrom": {"type": "integer", "format": "int32"}, "followerCntTo": {"type": "integer", "format": "int32"}, "handle": {"type": "string"}, "lastSyncTimeBeforeDays": {"type": "integer", "format": "int32"}, "lastSyncTimeFrom": {"type": "string", "format": "date-time"}, "lastSyncTimeLastDays": {"type": "integer", "format": "int32"}, "lastSyncTimeTo": {"type": "string", "format": "date-time"}, "limit": {"type": "integer", "format": "int32"}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "query": {"type": "string"}, "regions": {"type": "array", "items": {"type": "string"}}, "sortField": {"type": "string"}, "sortOrder": {"type": "string", "enum": ["asc", "desc"]}, "statRanges": {"type": "array", "items": {"$ref": "#/components/schemas/CreatorStatRangeVo"}}}}, "PageTkshopOrderRequest": {"title": "PageTkshopOrderRequest", "type": "object", "properties": {"amountFrom": {"type": "number", "format": "bigdecimal"}, "amountTo": {"type": "number", "format": "bigdecimal"}, "buyerHandle": {"type": "string", "description": "买家id"}, "buyerNote": {"type": "string", "description": "买家备注"}, "commissionHandle": {"type": "string", "description": "带货达人id"}, "contactAllEmpty": {"type": "boolean", "description": "买家联系方式：全部为空", "example": false}, "contactNotAllEmpty": {"type": "boolean", "description": "买家联系方式：至少一项不为空", "example": false}, "contacts": {"type": "array", "description": "买家联系方式信息", "items": {"$ref": "#/components/schemas/ContactQueryVo"}}, "cosFeeFrom": {"type": "number", "format": "bigdecimal"}, "cosFeeTo": {"type": "number", "format": "bigdecimal"}, "cosRatioFrom": {"type": "number", "format": "double"}, "cosRatioTo": {"type": "number", "format": "double"}, "createTimeBeforeDays": {"type": "integer", "format": "int32"}, "createTimeFrom": {"type": "string", "description": "创建时间开始", "format": "date-time"}, "createTimeLastDays": {"type": "integer", "format": "int32"}, "createTimeTo": {"type": "string", "description": "创建时间截止", "format": "date-time"}, "freeSample": {"type": "boolean", "description": "是否索样订单", "example": false}, "hasCommissionHandle": {"type": "boolean", "description": "是否有带货达人", "example": false}, "mediaId": {"type": "string", "description": "媒体id"}, "orderIds": {"type": "array", "description": "选中订单列表", "items": {"type": "integer", "format": "int64"}}, "orderNo": {"type": "string", "description": "商品ID"}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "productNo": {"type": "string"}, "promotionType": {"type": "string", "description": "带货来源", "enum": ["AffiliateProductPage", "ExternalTrafficProgram", "Livestream", "Showcase", "Video"]}, "quantityFrom": {"type": "integer", "format": "int32"}, "quantityTo": {"type": "integer", "format": "int32"}, "query": {"type": "string", "description": "订单ID/备注"}, "shopIds": {"type": "array", "description": "指定店铺", "items": {"type": "integer", "format": "int64"}}, "sortField": {"type": "string", "enum": ["amount", "cosFee", "createTime", "freeSample", "id", "quantity", "shopId", "status"]}, "sortOrder": {"type": "string", "enum": ["asc", "desc"]}, "status": {"type": "string", "description": "订单状态", "enum": ["cancellation", "completed", "fail_delivery", "pending", "shipped", "to_ship"]}, "statusList": {"type": "array", "description": "订单状态列表", "items": {"type": "string", "enum": ["cancellation", "completed", "fail_delivery", "pending", "shipped", "to_ship"]}}}}, "PageTkshopRequest": {"title": "PageTkshopRequest", "type": "object", "properties": {"pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "platformId": {"type": "integer", "description": "站点", "format": "int64"}, "query": {"type": "string", "description": "名称或备注"}, "shopType": {"type": "string", "description": "类型", "enum": ["Global", "Local", "None"]}, "sortField": {"type": "string", "enum": ["createTime", "healthRank", "healthScore", "healthStatus", "lastSyncTime", "name", "type"]}, "sortOrder": {"type": "string", "enum": ["asc", "desc"]}}}, "PageVideoCreatorRequest": {"title": "PageVideoCreatorRequest", "type": "object", "properties": {"pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "query": {"type": "string"}, "sortField": {"type": "string"}, "sortOrder": {"type": "string", "enum": ["asc", "desc"]}}}, "PageVideoRequest": {"title": "PageVideoRequest", "type": "object", "properties": {"adCode": {"type": "string"}, "autoSync": {"type": "boolean"}, "commentCntFrom": {"type": "integer", "format": "int32"}, "commentCntTo": {"type": "integer", "format": "int32"}, "creatorId": {"type": "integer", "format": "int64"}, "durationFrom": {"type": "integer", "format": "int32"}, "durationTo": {"type": "integer", "format": "int32"}, "estCommissionFrom": {"type": "number", "format": "double"}, "estCommissionTo": {"type": "number", "format": "double"}, "favoriteCntFrom": {"type": "integer", "format": "int32"}, "favoriteCntTo": {"type": "integer", "format": "int32"}, "gmvFrom": {"type": "number", "format": "double"}, "gmvTo": {"type": "number", "format": "double"}, "handle": {"type": "string", "description": "达人ID"}, "hasSampleRequest": {"type": "boolean", "description": "视频是否有索样记录", "example": false}, "itemsSoldFrom": {"type": "integer", "format": "int32"}, "itemsSoldTo": {"type": "integer", "format": "int32"}, "likeCntFrom": {"type": "integer", "format": "int32"}, "likeCntTo": {"type": "integer", "format": "int32"}, "limit": {"type": "integer", "format": "int32"}, "orderCountFrom": {"type": "integer", "format": "int32"}, "orderCountTo": {"type": "integer", "format": "int32"}, "ordersPmFrom": {"type": "number", "description": "千播转化率<=", "format": "double"}, "ordersPmQueryList": {"type": "array", "items": {"$ref": "#/components/schemas/QueryVo"}}, "ordersPmTo": {"type": "number", "description": "千播转化率>=", "format": "double"}, "ordersPmTypes": {"type": "array", "items": {"type": "string", "enum": ["abnormal", "bad", "good", "normal"]}}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "postTimeBeforeDays": {"type": "integer", "format": "int32"}, "postTimeFrom": {"type": "string", "format": "date-time"}, "postTimeLastDays": {"type": "integer", "format": "int32"}, "postTimeTo": {"type": "string", "format": "date-time"}, "productNo": {"type": "string"}, "query": {"type": "string", "description": "标题和备注或MediaId或达人handle前缀"}, "retweetCntFrom": {"type": "integer", "format": "int32"}, "retweetCntTo": {"type": "integer", "format": "int32"}, "shopIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "sortField": {"type": "string"}, "sortOrder": {"type": "string", "enum": ["asc", "desc"]}, "srApplyId": {"type": "string", "description": "视频的索样记录ID是什么"}, "syncTimeDiff": {"type": "integer", "format": "int32"}, "viewCntFrom": {"type": "integer", "format": "int32"}, "viewCntTo": {"type": "integer", "format": "int32"}, "viewerCntFrom": {"type": "integer", "format": "int32"}, "viewerCntTo": {"type": "integer", "format": "int32"}}}, "PartnerDto": {"title": "PartnerDto", "type": "object", "properties": {"bankAccount": {"type": "string"}, "bankName": {"type": "string"}, "bankNo": {"type": "string"}, "contactName": {"type": "string"}, "contactPhone": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "fullName": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "managerId": {"type": "integer", "format": "int64"}, "oemSupport": {"type": "boolean"}, "openapiSupport": {"type": "boolean"}, "organizedTeamAccountQuota": {"type": "integer", "format": "int32"}, "organizedTeamUserQuota": {"type": "integer", "format": "int32"}, "password": {"type": "string"}, "role": {"type": "string", "enum": ["Broker", "Organizer"]}, "shortName": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "BLOCK", "DELETED", "INACTIVE"]}, "teamId": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64"}}}, "PlanGroupChain": {"title": "PlanGroupChain", "type": "object", "properties": {"plans": {"type": "array", "description": "一个跟着一个，第一个一定是自动计划，后续的一定是触发计划", "items": {"$ref": "#/components/schemas/TkshopPlanVo"}}}}, "ProductNoRequest": {"title": "ProductNoRequest", "type": "object", "properties": {"productNos": {"type": "array", "items": {"type": "string"}}}}, "QueryVo": {"title": "QueryVo", "type": "object", "properties": {"symbol": {"type": "string"}, "value": {"type": "number", "format": "double"}}}, "RegionBatchVo": {"title": "RegionBatchVo", "type": "object", "properties": {"creators": {"type": "array", "items": {"$ref": "#/components/schemas/IdHandleVo"}}, "region": {"type": "string"}}}, "ReportGhJobRequest": {"title": "ReportGhJobRequest", "type": "object", "properties": {"jobId": {"type": "integer", "format": "int64"}, "message": {"type": "string"}, "rpaTaskId": {"type": "integer", "format": "int64"}, "status": {"type": "string", "description": "汇报的时候允许指定状态。如果未指定将根据 success 来设置为成功或失败。 Succeed | Failed | Cancelled ", "enum": ["Cancelled", "Failed", "Pending", "Running", "Scheduled", "Succeed"]}, "success": {"type": "boolean"}, "unbindCreatorShop": {"type": "boolean", "description": "是否解绑达人和分身，目前只有在公会后台邀约建联时有意义", "example": false}}}, "RpaFlowGroupVo": {"title": "RpaFlowGroupVo", "type": "object", "properties": {"description": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "sortNumber": {"type": "integer", "format": "int32"}, "teamId": {"type": "integer", "format": "int64"}}}, "RpaFlowVo": {"title": "RpaFlowVo", "type": "object", "properties": {"allowPushUpdate": {"type": "boolean"}, "allowRead": {"type": "boolean", "description": "是否可读。针对分享流程和市场流程", "example": false}, "bizCode": {"type": "string"}, "configId": {"type": "string"}, "console": {"type": "boolean"}, "createTime": {"type": "string", "format": "date-time"}, "createType": {"type": "string", "enum": ["FileCopy", "Manual", "Market", "MarketCopy", "Shared", "TkPack", "Tkshop"]}, "creatorId": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "dirty": {"type": "boolean"}, "expireTime": {"type": "string", "description": "过期时间，仅针对引用市场流程", "format": "date-time"}, "expired": {"type": "boolean", "description": "是否已过期，仅针对引用市场流程，根据 expireTime 计算得出来的", "example": false}, "extra": {"type": "object"}, "flowShareCode": {"type": "string"}, "groups": {"type": "array", "items": {"$ref": "#/components/schemas/RpaFlowGroupVo"}}, "id": {"type": "integer", "format": "int64"}, "marketId": {"type": "integer", "description": "对应的市场模板ID", "format": "int64"}, "marketLatestVersion": {"type": "string", "description": "如果是市场流程，显示市场流程的最新版本"}, "name": {"type": "string"}, "nameBrief": {"type": "string"}, "numberVersion": {"type": "integer", "description": "数字版本号，会从1开始累加", "format": "int32"}, "platforms": {"type": "array", "items": {"$ref": "#/components/schemas/RpaPlatformVo"}}, "publishTime": {"type": "string", "format": "date-time"}, "rpaType": {"type": "string", "description": "流程类型，分手机和browser", "enum": ["Browser", "Extension", "IOS", "Mobile"]}, "sessionInner": {"type": "boolean"}, "shareFromTeamId": {"type": "integer", "format": "int64"}, "shareFromTeamName": {"type": "string"}, "shareLatestVersion": {"type": "string", "description": "如果是分享过来的流程，显示被分享的流程最新的版本"}, "sharedFlowId": {"type": "integer", "description": "不为空表示是他人分享的流程", "format": "int64"}, "shopId": {"type": "integer", "format": "int64"}, "sortNo": {"type": "integer", "format": "int32"}, "status": {"type": "string", "enum": ["Draft", "Published"]}, "supportConcurrent": {"type": "boolean"}, "teamId": {"type": "integer", "description": "团队ID;", "format": "int64"}, "teamName": {"type": "string"}, "tkFlowId": {"type": "integer", "format": "int64"}, "updateTime": {"type": "string", "format": "date-time"}, "version": {"type": "string"}}}, "RpaPlatformVo": {"title": "RpaPlatformVo", "type": "object", "properties": {"flowId": {"type": "integer", "format": "int64"}, "platformName": {"type": "string"}}}, "SampleRequestConditionVo": {"title": "SampleRequestConditionVo", "type": "object", "properties": {"filedType": {"type": "string", "enum": ["CreatorColumn", "CreatorContact", "CreatorProp", "CreatorStat", "ProductColumn"]}, "key": {"type": "string"}, "symbol": {"type": "string", "enum": ["contains", "eq", "ge", "gt", "isEmpty", "le", "lt", "ne", "notContains", "notEmpty"]}, "value": {"type": "object"}}}, "SampleRequestDocument": {"title": "SampleRequestDocument", "type": "object", "properties": {"applyList": {"type": "array", "items": {"$ref": "#/components/schemas/SampleRequestVo"}}, "avatar": {"type": "string"}, "creatorId": {"type": "string"}, "followerCnt": {"type": "integer", "format": "int32"}, "handle": {"type": "string"}, "region": {"type": "string"}, "statMap": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/CreatorStatVo"}}}}, "SampleRequestMediaDocument": {"title": "SampleRequestMediaDocument", "type": "object", "properties": {"applyId": {"type": "string"}, "medias": {"type": "array", "items": {"$ref": "#/components/schemas/SampleRequestMediaItem"}}}}, "SampleRequestMediaItem": {"title": "SampleRequestMediaItem", "type": "object", "properties": {"commentNum": {"type": "integer", "format": "int32"}, "createTimestamp": {"type": "integer", "format": "int64"}, "finishTimestamp": {"type": "integer", "format": "int64"}, "likeNum": {"type": "integer", "format": "int32"}, "mediaAvatar": {"type": "string"}, "mediaId": {"type": "string"}, "mediaName": {"type": "string"}, "mediaUrl": {"type": "string"}, "promotionType": {"type": "string", "enum": ["AffiliateProductPage", "ExternalTrafficProgram", "Livestream", "Showcase", "Video"]}, "viewNum": {"type": "integer", "format": "int32"}}}, "SampleRequestShopBriefVo": {"title": "SampleRequestShopBriefVo", "type": "object", "properties": {"count": {"type": "integer", "format": "int32"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "shop": {"$ref": "#/components/schemas/ShopBriefVo"}}}, "SampleRequestShopVo": {"title": "SampleRequestShopVo", "type": "object", "properties": {"count": {"type": "integer", "format": "int32"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "shopId": {"type": "integer", "format": "int64"}}}, "SampleRequestStatusDocument": {"title": "SampleRequestStatusDocument", "type": "object", "properties": {"creatorId": {"type": "string"}, "handle": {"type": "string"}, "statusMap": {"type": "object", "additionalProperties": {"type": "string"}}}}, "SampleRequestVo": {"title": "SampleRequestVo", "type": "object", "properties": {"applyId": {"type": "string"}, "commissionRate": {"type": "number", "format": "double"}, "createTime": {"type": "integer", "description": "创建时间", "format": "int64"}, "expiredIn": {"type": "integer", "description": "过期剩余毫秒", "format": "int32"}, "groupId": {"type": "string"}, "productName": {"type": "string"}, "productNo": {"type": "string"}, "skuDesc": {"type": "string"}, "skuId": {"type": "string"}, "skuImage": {"type": "string"}, "status": {"type": "string", "enum": ["Cancelled", "Completed", "Expired", "InProgress", "ReadyToShip", "Reject", "Shipped", "ToReview"]}}}, "SendAddContactsRequest": {"title": "SendAddContactsRequest", "type": "object", "properties": {"advanceSettings": {"type": "object", "description": "邀约高级设置"}, "bizScene": {"type": "string", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}, "creatorIds": {"type": "array", "description": "拟加好友的达人列表", "items": {"type": "integer", "format": "int64"}}, "mobileId": {"type": "integer", "description": "所使用的手机id", "format": "int64"}}}, "SendAddFriendsRequest": {"title": "SendAddFriendsRequest", "type": "object", "properties": {"advanceSettings": {"type": "object", "description": "邀约高级设置"}, "bizScene": {"type": "string", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}, "contactType": {"type": "string", "description": "通过什么字段查找用于添加联系人的电话号码", "enum": ["email", "f<PERSON><PERSON><PERSON><PERSON><PERSON>", "line", "phone", "viber", "whatsapp", "zalo"]}, "creatorIds": {"type": "array", "description": "拟加好友的达人列表", "items": {"type": "integer", "format": "int64"}}, "mobileAccountId": {"type": "integer", "description": "所使用的手机账号的id。根据相应账号的平台也决定了最终的流程类型", "format": "int64"}}}, "SendBuyerIMChatRequest": {"title": "SendBuyerIMChatRequest", "type": "object", "properties": {"advanceSettings": {"type": "object"}, "content": {"type": "string", "description": "自定义输入内容，如果不为空，wordIds会被忽略"}, "deviceId": {"type": "string", "description": "必须要指定在哪个客户端上执行"}, "ghCreatorIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "shopId": {"type": "integer", "format": "int64"}, "videos": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "description": "<ghCreatorId, [相应的video数组]>"}, "wordsIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "SendEmailRequest": {"title": "SendEmailRequest", "type": "object", "properties": {"creatorIds": {"type": "array", "description": "要给哪些达人发送邮件", "items": {"type": "integer", "format": "int64"}}, "deviceId": {"type": "string"}, "documentIds": {"type": "array", "description": "电子邮件模板", "items": {"type": "integer", "format": "int64"}}, "emailId": {"type": "integer", "description": "拟使用的邮箱账号", "format": "int64"}}}, "SendIMChatByFilterRequest": {"title": "SendIMChatByFilterRequest", "type": "object", "properties": {"advanceSettings": {"type": "object"}, "content": {"type": "string", "description": "自定义输入内容，如果不为空，wordIds会被忽略"}, "deviceId": {"type": "string", "description": "必须要指定在哪个客户端上执行"}, "shopId": {"type": "integer", "format": "int64"}, "wordsIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "SendIMChatByHandleRequest": {"title": "SendIMChatByHandleRequest", "type": "object", "properties": {"advanceSettings": {"type": "object"}, "content": {"type": "string", "description": "自定义输入内容，如果不为空，wordIds会被忽略"}, "deviceId": {"type": "string", "description": "必须要指定在哪个客户端上执行"}, "ghCreatorId": {"type": "integer", "format": "int64"}, "shopId": {"type": "integer", "format": "int64"}, "videos": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "description": "<ghCreatorId, [相应的video数组]>"}, "wordsIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "SendInstantMessageRequest": {"title": "SendInstantMessageRequest", "type": "object", "properties": {"advanceSettings": {"type": "object", "description": "邀约高级设置"}, "bizScene": {"type": "string", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}, "content": {"type": "string", "description": "自定义输入内容，如果不为空，wordIds会被忽略"}, "creatorIds": {"type": "array", "description": "拟发送的达人列表", "items": {"type": "integer", "format": "int64"}}, "mobileAccountId": {"type": "integer", "description": "所使用的手机账号的id。根据相应账号的平台也决定了最终的流程类型", "format": "int64"}, "wordsIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "SendLoginCheckRequest": {"title": "SendLoginCheckRequest", "type": "object", "properties": {"advanceSettings": {"type": "object"}, "deviceId": {"type": "string", "description": "必须要指定在哪个客户端上执行"}, "shopId": {"type": "integer", "format": "int64"}}}, "SendTargetPlanByFilterRequest": {"title": "SendTargetPlanByFilterRequest", "type": "object", "properties": {"advanceSettings": {"type": "object"}, "deviceId": {"type": "string", "description": "必须要指定在哪个客户端上执行"}, "shopId": {"type": "integer", "format": "int64"}, "wordsIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "SendTargetPlanByHandleRequest": {"title": "SendTargetPlanByHandleRequest", "type": "object", "properties": {"advanceSettings": {"type": "object"}, "deviceId": {"type": "string", "description": "必须要指定在哪个客户端上执行"}, "shopId": {"type": "integer", "format": "int64"}, "wordsIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "SendVideoADCodeRequest": {"title": "SendVideoADCodeRequest", "type": "object", "properties": {"advanceSettings": {"type": "object"}, "deviceId": {"type": "string", "description": "必须要指定在哪个客户端上执行"}, "ghCreatorIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "shopId": {"type": "integer", "format": "int64"}}}, "ShopBriefVo": {"title": "ShopBriefVo", "type": "object", "properties": {"account": {"type": "string", "description": "账户账号"}, "allowExtension": {"type": "boolean", "description": "是否允许用户自行安装插件", "example": false}, "allowMonitor": {"type": "boolean", "description": "会话是否允许监视", "example": false}, "allowSkip": {"type": "boolean", "description": "是否允许跳过敏感操作", "example": false}, "autoFill": {"type": "boolean", "description": "是否自动代填", "example": false}, "bookmarkBar": {"type": "string"}, "coordinateId": {"type": "string"}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time"}, "creatorId": {"type": "integer", "description": "创建者", "format": "int64"}, "deleteTime": {"type": "string", "format": "date-time"}, "deleting": {"type": "boolean"}, "description": {"type": "string", "description": "描述"}, "domainPolicy": {"type": "string", "enum": ["Blacklist", "None", "Whitelist"]}, "exclusive": {"type": "boolean", "description": "是否独占访问", "example": false}, "extension": {"type": "string", "enum": ["both", "extension", "huayang"]}, "extraProp": {"type": "string"}, "fingerprintId": {"type": "integer", "description": "绑定的指纹Id", "format": "int64"}, "fingerprintTemplateId": {"type": "integer", "description": "绑定的指纹模板Id（只有无状态账号才允许绑定指纹模板）", "format": "int64"}, "frontUrl": {"type": "string"}, "googleTranslateSpeed": {"type": "boolean"}, "homePage": {"type": "string"}, "homePageSites": {"type": "string"}, "id": {"type": "integer", "description": "id", "format": "int64"}, "imageForbiddenSize": {"type": "integer", "format": "int64"}, "intranetEnabled": {"type": "boolean"}, "ipSwitchCheckInterval": {"type": "integer", "format": "int32"}, "ipSwitchStrategy": {"type": "string", "enum": ["Abort", "<PERSON><PERSON>", "Off"]}, "lastAccessTime": {"type": "string", "format": "date-time"}, "lastAccessUser": {"type": "integer", "format": "int64"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "loginStatus": {"type": "string", "enum": ["Offline", "Online", "Unknown"]}, "markCode": {"type": "string", "description": "任务栏分身标记文字"}, "markCodeBg": {"type": "integer", "description": "任务栏分身标记背景颜色", "format": "int32"}, "monitorPerception": {"type": "boolean"}, "name": {"type": "string", "description": "账户名称"}, "nameBookmarkEnabled": {"type": "boolean"}, "operateStatus": {"type": "string", "enum": ["shared", "sharing", "sole", "transferring"]}, "operatingCategory": {"type": "string", "description": "经营品类", "enum": ["医药保健", "图书文具", "宠物用品", "家具建材", "家电电器", "工业用品", "户外运动", "手机数码", "手表眼镜", "护肤美妆", "母婴玩具", "汽车配件", "生活家居", "电商其他", "电脑平板", "艺术珠宝", "花园聚会", "计生情趣", "软件程序", "鞋服箱包", "音乐影视", "食品生鲜", "鲜花绿植"]}, "parentShopId": {"type": "integer", "format": "int64"}, "password": {"type": "string", "description": "密码"}, "platform": {"description": "平台信息", "$ref": "#/components/schemas/ShopPlatformVo"}, "platformId": {"type": "integer", "description": "平台ID", "format": "int64"}, "privateAddress": {"type": "string"}, "privateTitle": {"type": "string"}, "recordPerception": {"type": "boolean"}, "recordPolicy": {"type": "string", "enum": ["<PERSON><PERSON>", "Disabled", "Forced"]}, "requireIp": {"type": "boolean"}, "resourcePolicy": {"type": "integer", "format": "int32"}, "securityPolicyEnabled": {"type": "boolean"}, "securityPolicyUpdateTime": {"type": "string", "format": "date-time"}, "sharePolicyId": {"type": "string"}, "shopDataSize": {"type": "integer", "format": "int64"}, "shopNo": {"type": "string"}, "stateless": {"type": "boolean"}, "statelessChangeFp": {"type": "boolean"}, "statelessSyncPolicy": {"type": "integer", "format": "int32"}, "syncPolicy": {"type": "integer", "format": "int32"}, "teamId": {"type": "integer", "description": "团队ID", "format": "int64"}, "trafficAlertStrategy": {"type": "string", "enum": ["Abort", "Off"]}, "trafficAlertThreshold": {"type": "integer", "format": "int32"}, "trafficSaving": {"type": "boolean"}, "type": {"type": "string", "enum": ["Global", "Local", "None"]}, "webSecurity": {"type": "boolean"}}}, "ShopBuyerBean": {"title": "Shop<PERSON>uyerBean", "type": "object", "properties": {"advanceSettings": {"type": "object"}, "ghCreatorIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "ShopChannelVo": {"title": "ShopChannelVo", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "dynamicStrategy": {"type": "string", "enum": ["Off", "<PERSON><PERSON><PERSON>", "SwitchOnSession"]}, "id": {"type": "integer", "format": "int64"}, "ip": {"description": "通道的IP", "$ref": "#/components/schemas/TeamIpVo"}, "ipId": {"type": "integer", "format": "int64"}, "ipPool": {"description": "通道的IP池", "$ref": "#/components/schemas/IpPoolDto"}, "ippId": {"type": "integer", "format": "int64"}, "locationId": {"type": "integer", "format": "int64"}, "locationLevel": {"type": "string", "enum": ["City", "Continent", "Country", "District", "None", "Province", "Unknown"]}, "officialChannelId": {"type": "integer", "format": "int64"}, "primary": {"type": "boolean"}, "shopId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}}}, "ShopCreatorBean": {"title": "ShopCreatorBean", "type": "object", "properties": {"advanceSettings": {"type": "object"}, "ghCreatorIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "ShopCreatorStatVo": {"title": "ShopCreatorStatVo", "type": "object", "properties": {"collaboratingCount": {"type": "integer", "description": "合作达人", "format": "int32"}, "hasOrderCount": {"type": "integer", "description": "出单达人", "format": "int32"}, "hasOrderRate": {"type": "number", "description": "出单率:出单数/(出单数+合作数）", "format": "double"}}}, "ShopDto": {"title": "ShopDto", "type": "object", "properties": {"account": {"type": "string", "description": "账户账号"}, "allowExtension": {"type": "boolean", "description": "是否允许用户自行安装插件", "example": false}, "allowMonitor": {"type": "boolean", "description": "会话是否允许监视", "example": false}, "allowSkip": {"type": "boolean", "description": "是否允许跳过敏感操作", "example": false}, "autoFill": {"type": "boolean", "description": "是否自动代填", "example": false}, "bookmarkBar": {"type": "string"}, "coordinateId": {"type": "string"}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time"}, "creatorId": {"type": "integer", "description": "创建者", "format": "int64"}, "deleteTime": {"type": "string", "format": "date-time"}, "deleting": {"type": "boolean"}, "description": {"type": "string", "description": "描述"}, "domainPolicy": {"type": "string", "enum": ["Blacklist", "None", "Whitelist"]}, "exclusive": {"type": "boolean", "description": "是否独占访问", "example": false}, "extension": {"type": "string", "enum": ["both", "extension", "huayang"]}, "extraProp": {"type": "string"}, "fingerprintId": {"type": "integer", "description": "绑定的指纹Id", "format": "int64"}, "fingerprintTemplateId": {"type": "integer", "description": "绑定的指纹模板Id（只有无状态账号才允许绑定指纹模板）", "format": "int64"}, "frontUrl": {"type": "string"}, "googleTranslateSpeed": {"type": "boolean"}, "homePage": {"type": "string"}, "homePageSites": {"type": "string"}, "id": {"type": "integer", "description": "id", "format": "int64"}, "imageForbiddenSize": {"type": "integer", "format": "int64"}, "intranetEnabled": {"type": "boolean"}, "ipSwitchCheckInterval": {"type": "integer", "format": "int32"}, "ipSwitchStrategy": {"type": "string", "enum": ["Abort", "<PERSON><PERSON>", "Off"]}, "lastAccessTime": {"type": "string", "format": "date-time"}, "lastAccessUser": {"type": "integer", "format": "int64"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "loginStatus": {"type": "string", "enum": ["Offline", "Online", "Unknown"]}, "markCode": {"type": "string", "description": "任务栏分身标记文字"}, "markCodeBg": {"type": "integer", "description": "任务栏分身标记背景颜色", "format": "int32"}, "monitorPerception": {"type": "boolean"}, "name": {"type": "string", "description": "账户名称"}, "nameBookmarkEnabled": {"type": "boolean"}, "operateStatus": {"type": "string", "enum": ["shared", "sharing", "sole", "transferring"]}, "operatingCategory": {"type": "string", "description": "经营品类", "enum": ["医药保健", "图书文具", "宠物用品", "家具建材", "家电电器", "工业用品", "户外运动", "手机数码", "手表眼镜", "护肤美妆", "母婴玩具", "汽车配件", "生活家居", "电商其他", "电脑平板", "艺术珠宝", "花园聚会", "计生情趣", "软件程序", "鞋服箱包", "音乐影视", "食品生鲜", "鲜花绿植"]}, "parentShopId": {"type": "integer", "format": "int64"}, "password": {"type": "string", "description": "密码"}, "platformId": {"type": "integer", "description": "平台ID", "format": "int64"}, "privateAddress": {"type": "string"}, "privateTitle": {"type": "string"}, "recordPerception": {"type": "boolean"}, "recordPolicy": {"type": "string", "enum": ["<PERSON><PERSON>", "Disabled", "Forced"]}, "requireIp": {"type": "boolean"}, "resourcePolicy": {"type": "integer", "format": "int32"}, "securityPolicyEnabled": {"type": "boolean"}, "securityPolicyUpdateTime": {"type": "string", "format": "date-time"}, "sharePolicyId": {"type": "string"}, "shopDataSize": {"type": "integer", "format": "int64"}, "shopNo": {"type": "string"}, "stateless": {"type": "boolean"}, "statelessChangeFp": {"type": "boolean"}, "statelessSyncPolicy": {"type": "integer", "format": "int32"}, "syncPolicy": {"type": "integer", "format": "int32"}, "teamId": {"type": "integer", "description": "团队ID", "format": "int64"}, "trafficAlertStrategy": {"type": "string", "enum": ["Abort", "Off"]}, "trafficAlertThreshold": {"type": "integer", "format": "int32"}, "trafficSaving": {"type": "boolean"}, "type": {"type": "string", "enum": ["Global", "Local", "None"]}, "webSecurity": {"type": "boolean"}}}, "ShopHealthVo": {"title": "ShopHealthVo", "type": "object", "properties": {"account": {"type": "string", "description": "账户账号"}, "allowExtension": {"type": "boolean", "description": "是否允许用户自行安装插件", "example": false}, "allowMonitor": {"type": "boolean", "description": "会话是否允许监视", "example": false}, "allowSkip": {"type": "boolean", "description": "是否允许跳过敏感操作", "example": false}, "autoFill": {"type": "boolean", "description": "是否自动代填", "example": false}, "bookmarkBar": {"type": "string"}, "channels": {"type": "array", "description": "通道列表", "items": {"$ref": "#/components/schemas/ShopChannelVo"}}, "coordinateId": {"type": "string"}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time"}, "creatorId": {"type": "integer", "description": "创建者", "format": "int64"}, "deleteTime": {"type": "string", "format": "date-time"}, "deleting": {"type": "boolean"}, "description": {"type": "string", "description": "描述"}, "domainPolicy": {"type": "string", "enum": ["Blacklist", "None", "Whitelist"]}, "exclusive": {"type": "boolean", "description": "是否独占访问", "example": false}, "extension": {"type": "string", "enum": ["both", "extension", "huayang"]}, "extraProp": {"type": "string"}, "fingerprintId": {"type": "integer", "description": "绑定的指纹Id", "format": "int64"}, "fingerprintTemplateId": {"type": "integer", "description": "绑定的指纹模板Id（只有无状态账号才允许绑定指纹模板）", "format": "int64"}, "frontUrl": {"type": "string"}, "googleTranslateSpeed": {"type": "boolean"}, "healthVo": {"$ref": "#/components/schemas/TkshopShopVo"}, "homePage": {"type": "string"}, "homePageSites": {"type": "string"}, "id": {"type": "integer", "description": "id", "format": "int64"}, "imageForbiddenSize": {"type": "integer", "format": "int64"}, "intranetEnabled": {"type": "boolean"}, "ipSwitchCheckInterval": {"type": "integer", "format": "int32"}, "ipSwitchStrategy": {"type": "string", "enum": ["Abort", "<PERSON><PERSON>", "Off"]}, "lanProxy": {"description": "本地代理配置", "$ref": "#/components/schemas/ShopLanProxyDto"}, "lastAccessTime": {"type": "string", "format": "date-time"}, "lastAccessUser": {"type": "integer", "format": "int64"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "loginStatus": {"type": "string", "enum": ["Offline", "Online", "Unknown"]}, "markCode": {"type": "string", "description": "任务栏分身标记文字"}, "markCodeBg": {"type": "integer", "description": "任务栏分身标记背景颜色", "format": "int32"}, "monitorPerception": {"type": "boolean"}, "name": {"type": "string", "description": "账户名称"}, "nameBookmarkEnabled": {"type": "boolean"}, "operateStatus": {"type": "string", "enum": ["shared", "sharing", "sole", "transferring"]}, "operatingCategory": {"type": "string", "description": "经营品类", "enum": ["医药保健", "图书文具", "宠物用品", "家具建材", "家电电器", "工业用品", "户外运动", "手机数码", "手表眼镜", "护肤美妆", "母婴玩具", "汽车配件", "生活家居", "电商其他", "电脑平板", "艺术珠宝", "花园聚会", "计生情趣", "软件程序", "鞋服箱包", "音乐影视", "食品生鲜", "鲜花绿植"]}, "parentShopId": {"type": "integer", "format": "int64"}, "password": {"type": "string", "description": "密码"}, "platform": {"description": "平台信息", "$ref": "#/components/schemas/ShopPlatformVo"}, "platformId": {"type": "integer", "description": "平台ID", "format": "int64"}, "privateAddress": {"type": "string"}, "privateTitle": {"type": "string"}, "recordPerception": {"type": "boolean"}, "recordPolicy": {"type": "string", "enum": ["<PERSON><PERSON>", "Disabled", "Forced"]}, "requireIp": {"type": "boolean"}, "resourcePolicy": {"type": "integer", "format": "int32"}, "securityPolicyEnabled": {"type": "boolean"}, "securityPolicyUpdateTime": {"type": "string", "format": "date-time"}, "sharePolicyId": {"type": "string"}, "shopDataSize": {"type": "integer", "format": "int64"}, "shopNo": {"type": "string"}, "stateless": {"type": "boolean"}, "statelessChangeFp": {"type": "boolean"}, "statelessSyncPolicy": {"type": "integer", "format": "int32"}, "syncPolicy": {"type": "integer", "format": "int32"}, "teamId": {"type": "integer", "description": "团队ID", "format": "int64"}, "trafficAlertStrategy": {"type": "string", "enum": ["Abort", "Off"]}, "trafficAlertThreshold": {"type": "integer", "format": "int32"}, "trafficSaving": {"type": "boolean"}, "type": {"type": "string", "enum": ["Global", "Local", "None"]}, "webSecurity": {"type": "boolean"}}}, "ShopLanProxyDto": {"title": "ShopLanProxyDto", "type": "object", "properties": {"enabled": {"type": "boolean"}, "host": {"type": "string"}, "hostDomestic": {"type": "boolean"}, "hostLocationId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "latitude": {"type": "number", "format": "double"}, "locale": {"type": "string"}, "locationId": {"type": "integer", "format": "int64"}, "longitude": {"type": "number", "format": "double"}, "networkType": {"type": "string", "enum": ["UseDirect", "UseProxy", "UseSystem"]}, "password": {"type": "string"}, "port": {"type": "integer", "format": "int32"}, "probeOnSession": {"type": "boolean"}, "proxyType": {"type": "string"}, "remoteIp": {"type": "string"}, "sshKey": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}, "timezone": {"type": "string"}, "updateTime": {"type": "string", "format": "date-time"}, "username": {"type": "string"}}}, "ShopMediaStatVo": {"title": "ShopMediaStatVo", "type": "object", "properties": {"hasOrderCount": {"type": "integer", "description": "出单达人", "format": "int32"}, "hasOrderRate": {"type": "number", "description": "出单率:出单数/(出单数+合作数）", "format": "double"}, "total": {"type": "integer", "description": "合作达人", "format": "int32"}}}, "ShopPlatformVo": {"title": "ShopPlatformVo", "type": "object", "properties": {"area": {"type": "string", "enum": ["Argentina", "Australia", "Austria", "Belarus", "Belgium", "Bolivia", "Brazil", "Canada", "Chile", "China", "Colombia", "Costa_Rica", "Dominican", "Ecuador", "Egypt", "France", "Germany", "Global", "Guatemala", "Honduras", "HongKong", "India", "Indonesia", "Ireland", "Israel", "Italy", "Japan", "Kazakhstan", "Korea", "Malaysia", "Mexico", "Netherlands", "Nicaragua", "Panama", "Paraguay", "Peru", "Philippines", "Poland", "Portuguese", "Puerto_Rico", "Russia", "Salvador", "Saudi_Arabia", "Singapore", "Spain", "Sweden", "Switzerland", "Taiwan", "Thailand", "Turkey", "United_Arab_Emirates", "United_Kingdom", "United_States", "Uruguay", "Venezuela", "Vietnam"]}, "category": {"type": "string", "enum": ["IM", "Mail", "Other", "Payment", "Shop", "SocialMedia"]}, "frontUrl": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "loginUrl": {"type": "string"}, "name": {"type": "string"}, "typeName": {"type": "string"}}}, "ShopSrStatVo": {"title": "ShopSrStatVo", "type": "object", "properties": {"completedCount": {"type": "integer", "description": "已完成", "format": "int32"}, "fulfillmentRate": {"type": "number", "description": "履约率：completedCount/total", "format": "double"}, "total": {"type": "integer", "description": "索样总数（不包含待审批+已拒绝已取消等）", "format": "int32"}}}, "ShopWithPlatformVo": {"title": "ShopWithPlatformVo", "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "operatingCategory": {"type": "string", "enum": ["医药保健", "图书文具", "宠物用品", "家具建材", "家电电器", "工业用品", "户外运动", "手机数码", "手表眼镜", "护肤美妆", "母婴玩具", "汽车配件", "生活家居", "电商其他", "电脑平板", "艺术珠宝", "花园聚会", "计生情趣", "软件程序", "鞋服箱包", "音乐影视", "食品生鲜", "鲜花绿植"]}, "parentShopId": {"type": "integer", "format": "int64"}, "platformArea": {"type": "string", "enum": ["Argentina", "Australia", "Austria", "Belarus", "Belgium", "Bolivia", "Brazil", "Canada", "Chile", "China", "Colombia", "Costa_Rica", "Dominican", "Ecuador", "Egypt", "France", "Germany", "Global", "Guatemala", "Honduras", "HongKong", "India", "Indonesia", "Ireland", "Israel", "Italy", "Japan", "Kazakhstan", "Korea", "Malaysia", "Mexico", "Netherlands", "Nicaragua", "Panama", "Paraguay", "Peru", "Philippines", "Poland", "Portuguese", "Puerto_Rico", "Russia", "Salvador", "Saudi_Arabia", "Singapore", "Spain", "Sweden", "Switzerland", "Taiwan", "Thailand", "Turkey", "United_Arab_Emirates", "United_Kingdom", "United_States", "Uruguay", "Venezuela", "Vietnam"]}, "platformId": {"type": "integer", "format": "int64"}, "platformName": {"type": "string"}, "recordPolicy": {"type": "string", "enum": ["<PERSON><PERSON>", "Disabled", "Forced"]}, "securityPolicyEnabled": {"type": "boolean"}, "teamId": {"type": "integer", "format": "int64"}, "type": {"type": "string", "enum": ["Global", "Local", "None"]}}}, "SyncAffiliateNoDataRequest": {"title": "SyncAffiliateNoDataRequest", "type": "object", "properties": {"orderNos": {"type": "array", "items": {"type": "string"}}, "shopId": {"type": "integer", "format": "int64"}}}, "SyncAffiliateOrderRequest": {"title": "SyncAffiliateOrderRequest", "type": "object", "properties": {"orders": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopAffiliateOrderDocument"}}, "region": {"type": "string"}, "shopId": {"type": "integer", "format": "int64"}}}, "SyncBuyerInfoRequest": {"title": "SyncBuyerInfoRequest", "type": "object", "properties": {"advanceSettings": {"type": "object"}, "deviceId": {"type": "string", "description": "必须要指定在哪个客户端上执行"}, "ghCreatorIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "shopId": {"type": "integer", "format": "int64"}}}, "SyncBuyerRequest": {"title": "SyncBuyerRequest", "type": "object", "properties": {"buyers": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopBuyerDocument"}}}}, "SyncCreatorDetailRequest": {"title": "SyncCreatorDetailRequest", "type": "object", "properties": {"creators": {"type": "array", "items": {"$ref": "#/components/schemas/CreatorDetailDocument"}}, "sourceId": {"type": "integer", "format": "int64"}, "tags": {"type": "array", "items": {"type": "string"}}}}, "SyncCreatorShopRequest": {"title": "SyncCreatorShopRequest", "type": "object", "properties": {"creators": {"type": "array", "items": {"$ref": "#/components/schemas/CreatorShopDocument"}}, "shopId": {"type": "integer", "description": "当前分身ID", "format": "int64"}, "sourceId": {"type": "integer", "format": "int64"}}}, "SyncCreatorVideoRequest": {"title": "SyncCreatorVideoRequest", "type": "object", "properties": {"videos": {"type": "array", "items": {"$ref": "#/components/schemas/CreatorVideoDocument"}}}}, "SyncOrdersRequest": {"title": "SyncOrdersRequest", "type": "object", "properties": {"advanceSettings": {"type": "object"}, "deviceId": {"type": "string", "description": "必须要指定在哪个客户端上执行"}, "shopIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "SyncProductRequest": {"title": "SyncProductRequest", "type": "object", "properties": {"products": {"type": "array", "description": "产品列表", "items": {"$ref": "#/components/schemas/TkProductDocument"}}, "shopId": {"type": "integer", "description": "分身ID", "format": "int64"}}}, "SyncProductsRequest": {"title": "SyncProductsRequest", "type": "object", "properties": {"advanceSettings": {"type": "object"}, "deviceId": {"type": "string", "description": "必须要指定在哪个客户端上执行"}, "shopId": {"type": "integer", "format": "int64"}}}, "SyncSampleCreatorRequest": {"title": "SyncSampleCreatorRequest", "type": "object", "properties": {"advanceSettings": {"type": "object"}, "deviceId": {"type": "string", "description": "必须要指定在哪个客户端上执行"}, "shopId": {"type": "integer", "format": "int64"}}}, "SyncSampleRequestMediaRequest": {"title": "SyncSampleRequestMediaRequest", "type": "object", "properties": {"requests": {"type": "array", "items": {"$ref": "#/components/schemas/SampleRequestMediaDocument"}}, "shopId": {"type": "integer", "format": "int64"}}}, "SyncSampleRequestRequest": {"title": "SyncSampleRequestRequest", "type": "object", "properties": {"requests": {"type": "array", "items": {"$ref": "#/components/schemas/SampleRequestDocument"}}, "shopId": {"type": "integer", "format": "int64"}}}, "SyncSampleRequestStatusRequest": {"title": "SyncSampleRequestStatusRequest", "type": "object", "properties": {"requests": {"type": "array", "items": {"$ref": "#/components/schemas/SampleRequestStatusDocument"}}, "shopId": {"type": "integer", "format": "int64"}}}, "SyncShopInfoRequest": {"title": "SyncShopInfoRequest", "type": "object", "properties": {"advanceSettings": {"type": "object"}, "deviceId": {"type": "string", "description": "必须要指定在哪个客户端上执行"}, "shopId": {"type": "integer", "format": "int64"}, "shopIds": {"type": "array", "description": "请使用 shopId ，当shopId不为空时，shopIds会被忽略，当shopId为空时，为了向前兼容，shopIds只读取第一个（我们现在也没有shopIds传多个的场景）", "items": {"type": "integer", "format": "int64"}}}}, "SyncVideoAdCodeRequest": {"title": "SyncVideoAdCodeRequest", "type": "object", "properties": {"adCode": {"type": "string", "description": "投流码"}, "alias": {"type": "string", "description": "达人昵称"}, "avatar": {"type": "string", "description": "达人头像"}, "bio": {"type": "string", "description": "达人bio"}, "handle": {"type": "string", "description": "达人handle"}, "mediaAvatar": {"type": "string", "description": "封面图片URL"}, "mediaId": {"type": "string", "description": "视频ID"}, "mediaName": {"type": "string", "description": "视频名称"}, "mediaUrl": {"type": "string", "description": "播放地址"}, "postTimestamp": {"type": "integer", "format": "int64"}, "products": {"type": "array", "items": {"$ref": "#/components/schemas/VideoAdCodeProductVo"}}, "region": {"type": "string", "description": "区域"}, "shopId": {"type": "integer", "description": "花漾店铺ID", "format": "int64"}}}, "TKVideo": {"title": "TKVideo", "type": "object", "properties": {"author": {"$ref": "#/components/schemas/TKVideoAuthor"}, "cover": {"$ref": "#/components/schemas/TKVideoCover"}, "createTime": {"type": "string", "format": "date-time"}, "data_size": {"type": "integer", "format": "int64"}, "desc": {"type": "string"}, "height": {"type": "integer", "format": "int32"}, "statistics": {"$ref": "#/components/schemas/TKVideoStatistics"}, "url_list": {"type": "array", "items": {"type": "string"}}, "width": {"type": "integer", "format": "int32"}}}, "TKVideoAuthor": {"title": "TKVideoAuthor", "type": "object", "properties": {"handle": {"type": "string"}, "nickname": {"type": "string"}, "uid": {"type": "string"}, "unique_id": {"type": "string"}}}, "TKVideoCover": {"title": "TKVideoCover", "type": "object", "properties": {"height": {"type": "integer", "format": "int32"}, "url_list": {"type": "array", "items": {"type": "string"}}, "width": {"type": "integer", "format": "int32"}}}, "TKVideoStatistics": {"title": "TKVideoStatistics", "type": "object", "properties": {"aweme_id": {"type": "string"}, "collect_count": {"type": "integer", "format": "int32"}, "comment_count": {"type": "integer", "format": "int32"}, "digg_count": {"type": "integer", "format": "int32"}, "download_count": {"type": "integer", "format": "int32"}, "forward_count": {"type": "integer", "format": "int32"}, "lose_comment_count": {"type": "integer", "format": "int32"}, "lose_count": {"type": "integer", "format": "int32"}, "play_count": {"type": "integer", "format": "int32"}, "repost_count": {"type": "integer", "format": "int32"}, "share_count": {"type": "integer", "format": "int32"}, "whatsapp_share_count": {"type": "integer", "format": "int32"}}}, "TSSyncCreatorPolicy": {"title": "TSSyncCreatorPolicy", "type": "object", "properties": {"maxShopTaskSyncCreator": {"type": "integer", "description": "达人基础信息更新每批更新数量", "format": "int32"}, "shopTaskSyncCreatorInterval": {"type": "integer", "description": "达人基础信息更新每批次间隔", "format": "int32"}}}, "TagDto": {"title": "TagDto", "type": "object", "properties": {"bizCode": {"type": "string"}, "color": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "expireTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "resourceType": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}, "system": {"type": "boolean"}, "tag": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}}}, "TargetPlanClearRequest": {"title": "TargetPlanClearRequest", "type": "object", "properties": {"advanceSettings": {"type": "object"}, "deviceId": {"type": "string", "description": "必须要指定在哪个客户端上执行"}, "shopId": {"type": "integer", "format": "int64"}}}, "TeamDto": {"title": "TeamDto", "type": "object", "properties": {"avatar": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "deleteTime": {"type": "string", "format": "date-time"}, "domesticCloudEnabled": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "invalidTime": {"type": "string", "format": "date-time"}, "inviteCode": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "overseaCloudEnabled": {"type": "boolean"}, "paid": {"type": "boolean"}, "partnerId": {"type": "integer", "format": "int64"}, "payTime": {"type": "string", "format": "date-time"}, "repurchaseTime": {"type": "string", "format": "date-time"}, "repurchased": {"type": "boolean"}, "status": {"type": "string", "enum": ["Blocked", "Deleted", "Pending", "Ready"]}, "teamType": {"type": "string", "enum": ["crs", "gh", "krShop", "normal", "partner", "plugin", "tk", "tkshop"]}, "tenantId": {"type": "integer", "format": "int64"}, "testing": {"type": "boolean"}, "validateTime": {"type": "string", "format": "date-time"}, "validated": {"type": "boolean"}, "verified": {"type": "boolean"}}}, "TeamIpVo": {"title": "TeamIpVo", "type": "object", "properties": {"autoRenew": {"type": "boolean"}, "cloudProvider": {"type": "string"}, "cloudRegion": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "directDownTraffic": {"type": "integer", "format": "int64"}, "directUpTraffic": {"type": "integer", "format": "int64"}, "domestic": {"type": "boolean"}, "downTraffic": {"type": "integer", "format": "int64"}, "dynamic": {"type": "boolean"}, "eipId": {"type": "integer", "format": "int64"}, "enableWhitelist": {"type": "boolean"}, "expireStatus": {"type": "string", "description": "过期状态", "enum": ["Expired", "Expiring", "Normal"]}, "forbiddenLongLatitude": {"type": "boolean"}, "gatewayId": {"type": "integer", "format": "int64"}, "goodsId": {"type": "integer", "format": "int64"}, "goodsType": {"type": "string", "enum": ["Credit", "CreditPack", "ExclusiveIp", "FingerprintQuota", "IosDeveloperApprove", "Ip", "IpGo", "IpProxy", "MarketFlow", "None", "PluginPack", "PriceDifference", "ProxyTraffic", "RpaCaptcha", "RpaExecuteQuota", "RpaMobile", "RpaOpenAi", "RpaSendEmail", "RpaSendSms", "RpaSendWeChat", "Rpa_Voucher_Base", "Rpa_Voucher_Performance", "SharingIp", "ShopQuota", "ShopSecurityPolicy", "StorageQuota", "TeamMemberQuota", "Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TkPack", "TkPackTrail", "Tkshop", "TkshopEnterprise", "TkshopStandard", "Traffic", "TransitTraffic", "TransitTrafficV2", "UserExclusiveIp", "Voucher"]}, "id": {"type": "integer", "format": "int64"}, "importType": {"type": "string", "enum": ["Platform", "User"]}, "invalidTime": {"type": "string", "format": "date-time"}, "ip": {"type": "string"}, "lastProbeTime": {"type": "string", "format": "date-time"}, "latitude": {"type": "number", "format": "double"}, "locale": {"type": "string"}, "locationId": {"type": "integer", "format": "int64"}, "longitude": {"type": "number", "format": "double"}, "name": {"type": "string"}, "networkType": {"type": "string", "enum": ["cloudIdc", "mobile", "proxyIdc", "residential", "unknown", "unknownIdc"]}, "operateStatus": {"type": "string", "enum": ["shared", "sharing", "sole", "transferring"]}, "originalTeam": {"type": "integer", "format": "int64"}, "periodUnit": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}, "pipeType": {"type": "string", "enum": ["None", "Proxy", "Tunnel", "TunnelFailToProxy"]}, "preferTransit": {"type": "integer", "format": "int64"}, "probeError": {"type": "string"}, "providerName": {"type": "string", "description": "供应商名称"}, "realIp": {"type": "string"}, "refreshUrl": {"type": "string"}, "remoteLogin": {"type": "boolean"}, "renewPrice": {"type": "number", "format": "bigdecimal"}, "source": {"type": "string"}, "speedLimit": {"type": "integer", "format": "int32"}, "status": {"type": "string", "enum": ["Available", "Pending", "Unavailable"]}, "sticky": {"type": "boolean"}, "teamId": {"type": "integer", "format": "int64"}, "testingTime": {"type": "integer", "format": "int32"}, "timezone": {"type": "string"}, "traffic": {"type": "number", "format": "double"}, "trafficCurrency": {"type": "string", "enum": ["CREDIT", "RMB", "USD"]}, "trafficPrice": {"type": "number", "format": "bigdecimal"}, "trafficUnlimited": {"type": "boolean"}, "transitType": {"type": "string", "enum": ["Auto", "Direct", "Transit"]}, "tunnelTypes": {"type": "string"}, "upTraffic": {"type": "integer", "format": "int64"}, "valid": {"type": "boolean"}, "validEndDate": {"type": "string", "format": "date-time"}, "vpsId": {"type": "integer", "format": "int64"}}}, "TeamQuotaVo": {"title": "TeamQuotaVo", "type": "object", "properties": {"freeQuota": {"type": "number", "description": "团队免费配额，null表示使用官方默认，-1表示无限", "format": "double"}, "ladderPrices": {"type": "array", "description": "阶梯价格", "items": {"$ref": "#/components/schemas/LadderPriceRange"}}, "price": {"type": "number", "description": "官方单价（花瓣/天），0表示免费使用", "format": "double"}, "quota": {"type": "number", "description": "（最大）配额(-1表示不限制）", "format": "double"}, "quotaDesc": {"type": "string", "description": "配额描述"}, "quotaName": {"type": "string", "description": "配额名称", "enum": ["ShopQuota", "ShopSecurityPolicy", "StorageQuota", "TeamMemberQuota", "Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}}}, "TeamTkshopConfig": {"title": "TeamTkshopConfig", "type": "object", "properties": {"expireRemind": {"type": "boolean", "description": "是否过期提醒", "example": false}, "ordersPmQueryVo": {"description": "千次播放转换率参数", "$ref": "#/components/schemas/OrdersPmQueryVo"}, "remainBeforeDays": {"type": "integer", "description": "提前n天提醒", "format": "int32"}}}, "TkProductDocument": {"title": "TkProductDocument", "type": "object", "properties": {"name": {"type": "string", "description": "产品名称"}, "no": {"type": "string", "description": "产品ID"}, "planEnabled": {"type": "boolean"}, "planRemark": {"type": "string"}, "price": {"type": "number", "description": "单价", "format": "bigdecimal"}, "priceUnit": {"type": "string", "description": "价格单位"}, "quantity": {"type": "integer", "description": "库存", "format": "int32"}, "status": {"type": "string", "description": "状态", "enum": ["Deactivated", "Deleted", "Draft", "Live", "Reviewing", "Suspended"]}, "thumbUrl": {"type": "string", "description": "产品图片"}, "updateTime": {"type": "integer", "description": "更新时间戳", "format": "int64"}}}, "TkshopAddFriendshipRequest": {"title": "TkshopAddFriendshipRequest", "type": "object", "properties": {"friendships": {"type": "array", "items": {"$ref": "#/components/schemas/AddFriendshipVo"}}}}, "TkshopAffiliateOrderDocument": {"title": "TkshopAffiliateOrderDocument", "type": "object", "properties": {"completeTime": {"type": "integer", "format": "int64"}, "createTimestamp": {"type": "integer", "description": "这个订单的创建时间", "format": "int64"}, "currency": {"type": "string", "description": "使用的是哪种货币，What your buyer paid那里可以看到"}, "deliveryTime": {"type": "integer", "format": "int64"}, "items": {"type": "array", "description": "订单条目", "items": {"$ref": "#/components/schemas/TkshopAffiliateOrderItemDocument"}}, "orderNo": {"type": "string"}, "settlementTime": {"type": "integer", "format": "int64"}}}, "TkshopAffiliateOrderItemDocument": {"title": "TkshopAffiliateOrderItemDocument", "type": "object", "properties": {"adsCosFee": {"type": "number", "format": "double"}, "adsCosRatio": {"type": "number", "format": "double"}, "cosFee": {"type": "number", "format": "double"}, "cosRatio": {"type": "number", "format": "double"}, "creatorNickname": {"type": "string"}, "creatorUsername": {"type": "string", "description": "提成达人handle"}, "mediaId": {"type": "string", "description": "带货视频ID"}, "productAvatar": {"type": "string", "description": "产品图片"}, "productName": {"type": "string"}, "productNo": {"type": "string"}, "promotionType": {"type": "string", "enum": ["AffiliateProductPage", "ExternalTrafficProgram", "Livestream", "Showcase", "Video"]}, "quantity": {"type": "integer", "description": "买了几个", "format": "int32"}, "skuName": {"type": "string"}, "unit": {"type": "string", "description": "价格单位"}, "unitPrice": {"type": "number", "description": "产品单价", "format": "bigdecimal"}}}, "TkshopAutoTagRequest": {"title": "TkshopAutoTagRequest", "type": "object", "properties": {"handles": {"type": "array", "items": {"type": "string"}}, "tag": {"type": "string"}, "tagKey": {"type": "string"}}}, "TkshopBuyerDetailVo": {"title": "TkshopBuyerDetailVo", "type": "object", "properties": {"alias": {"type": "string"}, "avatar": {"type": "string"}, "contactMap": {"type": "object", "additionalProperties": {"type": "string"}}, "contactUrl": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "string"}, "firstBuyTime": {"type": "string", "format": "date-time"}, "friendshipList": {"type": "array", "description": "好友关系", "items": {"$ref": "#/components/schemas/TkshopContactFriendshipVo"}}, "handle": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "lastBuyTime": {"type": "string", "format": "date-time"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "orderAmount": {"type": "number", "format": "bigdecimal"}, "orderCount": {"type": "integer", "format": "int32"}, "region": {"type": "string"}, "remark": {"type": "string"}, "responsibleUserId": {"type": "integer", "format": "int64"}, "shopIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "status": {"type": "string", "enum": ["NotContacted", "<PERSON><PERSON>"]}, "tags": {"type": "array", "items": {"$ref": "#/components/schemas/TagDto"}}, "teamId": {"type": "integer", "format": "int64"}, "unit": {"type": "string"}}}, "TkshopBuyerDocument": {"title": "TkshopBuyerDocument", "type": "object", "properties": {"avatar": {"type": "string", "description": "用户的头像地址"}, "contactUrl": {"type": "string", "description": "给买家发消息的链接"}, "creatorId": {"type": "string"}, "handle": {"type": "string"}, "phone": {"type": "string", "description": "手机号"}, "region": {"type": "string"}}}, "TkshopBuyerDto": {"title": "TkshopBuyerDto", "type": "object", "properties": {"alias": {"type": "string"}, "avatar": {"type": "string"}, "contactUrl": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "string"}, "firstBuyTime": {"type": "string", "format": "date-time"}, "handle": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "lastBuyTime": {"type": "string", "format": "date-time"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "orderAmount": {"type": "number", "format": "bigdecimal"}, "orderCount": {"type": "integer", "format": "int32"}, "region": {"type": "string"}, "remark": {"type": "string"}, "responsibleUserId": {"type": "integer", "format": "int64"}, "status": {"type": "string", "enum": ["NotContacted", "<PERSON><PERSON>"]}, "teamId": {"type": "integer", "format": "int64"}, "unit": {"type": "string"}}}, "TkshopBuyerInteractionDto": {"title": "TkshopBuyerInteractionDto", "type": "object", "properties": {"buyerId": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "extraInfo": {"type": "string"}, "flowId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "interactTime": {"type": "string", "format": "date-time"}, "interactType": {"type": "string", "enum": ["add_contact", "add_friend", "im_chat", "manual_remark", "send_email", "send_facebook", "send_line", "send_whatsapp", "send_zalo"]}, "interactionId": {"type": "string"}, "jobId": {"type": "integer", "format": "int64"}, "shopId": {"type": "integer", "format": "int64"}, "shopName": {"type": "string"}, "status": {"type": "string"}, "targetId": {"type": "integer", "format": "int64"}, "taskId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}, "updateTime": {"type": "string", "format": "date-time"}, "userId": {"type": "integer", "format": "int64"}}}, "TkshopCategoryDto": {"title": "TkshopCategoryDto", "type": "object", "properties": {"category": {"type": "string"}, "id": {"type": "integer", "format": "int64"}}}, "TkshopCheckInvitationCreatorRequest": {"title": "TkshopCheckInvitationCreatorRequest", "type": "object", "properties": {"creatorIds": {"type": "array", "items": {"type": "string"}}, "productNos": {"type": "array", "items": {"type": "string"}}, "shopId": {"type": "string"}}}, "TkshopCheckInvitationRequest": {"title": "TkshopCheckInvitationRequest", "type": "object", "properties": {"invitations": {"type": "array", "items": {"$ref": "#/components/schemas/CheckInvitationVo"}}, "shopId": {"type": "integer", "format": "int64"}}}, "TkshopConfig": {"title": "TkshopConfig", "type": "object", "properties": {"enterprise": {"$ref": "#/components/schemas/TkshopVersionConfig"}, "operationTeamId": {"type": "integer", "format": "int64"}, "standard": {"$ref": "#/components/schemas/TkshopVersionConfig"}, "trackVideoDays": {"type": "integer", "format": "int32"}}}, "TkshopContactFriendshipVo": {"title": "TkshopContactFriendshipVo", "type": "object", "properties": {"accountId": {"type": "integer", "format": "int64"}, "contactType": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "error": {"type": "string"}, "mobileId": {"type": "integer", "format": "int64"}, "mobileName": {"type": "string"}, "status": {"type": "string", "enum": ["Error", "NotFound", "Ready", "Unknown"]}, "username": {"type": "string"}}}, "TkshopContactVo": {"title": "TkshopContactVo", "type": "object", "properties": {"contactMap": {"type": "object", "additionalProperties": {"type": "string"}}, "handle": {"type": "string"}}}, "TkshopCreatorAchievementResult": {"title": "TkshopCreatorAchievementResult", "type": "object", "properties": {"lives": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopCreatorLiveDto"}}, "products": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopCreatorProductDto"}}, "shop": {"$ref": "#/components/schemas/TkshopCreatorShopDto"}, "videos": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopCreatorVideoDto"}}}}, "TkshopCreatorAchievementStat": {"title": "TkshopCreatorAchievementStat", "type": "object", "properties": {"commission": {"type": "number", "format": "double"}, "gmv": {"type": "number", "format": "double"}, "itemsSold": {"type": "integer", "format": "int32"}, "lives": {"type": "integer", "format": "int32"}, "orderCount": {"type": "integer", "format": "int32"}, "products": {"type": "integer", "format": "int32"}, "shopCount": {"type": "integer", "format": "int32"}, "status": {"type": "string", "enum": ["Collaborating", "HasOrder", "NotContacted", "<PERSON><PERSON>"]}, "unit": {"type": "string"}, "videos": {"type": "integer", "format": "int32"}}}, "TkshopCreatorAllocateRequest": {"title": "TkshopCreatorAllocateRequest", "type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "responsibleUserId": {"type": "integer", "format": "int64"}}}, "TkshopCreatorContactDetailVo": {"title": "TkshopCreatorContactDetailVo", "type": "object", "properties": {"address": {"type": "string"}, "alias": {"type": "string"}, "analysisCreatorId": {"type": "string"}, "avatar": {"type": "string"}, "basicSyncTime": {"type": "string", "format": "date-time"}, "bio": {"type": "string"}, "categories": {"type": "string"}, "cid": {"type": "string"}, "contactMap": {"type": "object", "additionalProperties": {"type": "string"}}, "contentType": {"type": "string", "enum": ["All", "LIVE", "Video"]}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "string"}, "deleteRemark": {"type": "string"}, "deleteTime": {"type": "string", "format": "date-time"}, "deleting": {"type": "boolean"}, "followerCnt": {"type": "integer", "format": "int32"}, "friendshipList": {"type": "array", "description": "好友关系", "items": {"$ref": "#/components/schemas/TkshopContactFriendshipVo"}}, "gmv": {"type": "number", "format": "double"}, "handle": {"type": "string"}, "hasNewMsg": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "itemsSold": {"type": "integer", "format": "int32"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "orderCount": {"type": "integer", "format": "int32"}, "region": {"type": "string"}, "remark": {"type": "string"}, "responsibleUserId": {"type": "integer", "format": "int64"}, "roi": {"type": "number", "format": "double"}, "source": {"type": "integer", "format": "int64"}, "status": {"type": "string", "enum": ["Collaborating", "HasOrder", "NotContacted", "<PERSON><PERSON>"]}, "teamId": {"type": "integer", "format": "int64"}, "totalCommission": {"type": "number", "format": "double"}, "totalGmv": {"type": "number", "format": "double"}, "totalItemsSold": {"type": "integer", "format": "int32"}, "totalProducts": {"type": "integer", "format": "int32"}, "unit": {"type": "string"}}}, "TkshopCreatorDetailVo": {"title": "TkshopCreatorDetailVo", "type": "object", "properties": {"achievedShopIds": {"type": "array", "description": "合作店铺", "items": {"type": "integer", "format": "int64"}}, "address": {"type": "string"}, "alias": {"type": "string"}, "analysisCreatorId": {"type": "string"}, "avatar": {"type": "string"}, "basicSyncTime": {"type": "string", "format": "date-time"}, "bio": {"type": "string"}, "categories": {"type": "string"}, "categoryList": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopCategoryDto"}}, "cid": {"type": "string"}, "contactMap": {"type": "object", "additionalProperties": {"type": "string"}}, "contentType": {"type": "string", "enum": ["All", "LIVE", "Video"]}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "string"}, "creatorRoi": {"description": "达人的ROI", "$ref": "#/components/schemas/TkshopCreatorRoiDto"}, "deleteRemark": {"type": "string"}, "deleteTime": {"type": "string", "format": "date-time"}, "deleting": {"type": "boolean"}, "followerCnt": {"type": "integer", "format": "int32"}, "friendshipList": {"type": "array", "description": "好友关系", "items": {"$ref": "#/components/schemas/TkshopContactFriendshipVo"}}, "gmv": {"type": "number", "format": "double"}, "handle": {"type": "string"}, "hasNewMsg": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "itemsSold": {"type": "integer", "format": "int32"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "orderCount": {"type": "integer", "format": "int32"}, "region": {"type": "string"}, "remark": {"type": "string"}, "responsibleUser": {"description": "认领人", "$ref": "#/components/schemas/UserDto"}, "responsibleUserId": {"type": "integer", "format": "int64"}, "roi": {"type": "number", "format": "double"}, "source": {"type": "integer", "format": "int64"}, "statList": {"type": "array", "description": "统计数据", "items": {"$ref": "#/components/schemas/TkshopCreatorStatVo"}}, "status": {"type": "string", "enum": ["Collaborating", "HasOrder", "NotContacted", "<PERSON><PERSON>"]}, "tags": {"type": "array", "description": "标签", "items": {"$ref": "#/components/schemas/TagDto"}}, "teamId": {"type": "integer", "format": "int64"}, "totalCommission": {"type": "number", "format": "double"}, "totalGmv": {"type": "number", "format": "double"}, "totalItemsSold": {"type": "integer", "format": "int32"}, "totalProducts": {"type": "integer", "format": "int32"}, "unit": {"type": "string"}}}, "TkshopCreatorDto": {"title": "TkshopCreatorDto", "type": "object", "properties": {"address": {"type": "string"}, "alias": {"type": "string"}, "analysisCreatorId": {"type": "string"}, "avatar": {"type": "string"}, "basicSyncTime": {"type": "string", "format": "date-time"}, "bio": {"type": "string"}, "categories": {"type": "string"}, "cid": {"type": "string"}, "contentType": {"type": "string", "enum": ["All", "LIVE", "Video"]}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "string"}, "deleteRemark": {"type": "string"}, "deleteTime": {"type": "string", "format": "date-time"}, "deleting": {"type": "boolean"}, "followerCnt": {"type": "integer", "format": "int32"}, "gmv": {"type": "number", "format": "double"}, "handle": {"type": "string"}, "hasNewMsg": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "itemsSold": {"type": "integer", "format": "int32"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "orderCount": {"type": "integer", "format": "int32"}, "region": {"type": "string"}, "remark": {"type": "string"}, "responsibleUserId": {"type": "integer", "format": "int64"}, "roi": {"type": "number", "format": "double"}, "source": {"type": "integer", "format": "int64"}, "status": {"type": "string", "enum": ["Collaborating", "HasOrder", "NotContacted", "<PERSON><PERSON>"]}, "teamId": {"type": "integer", "format": "int64"}, "totalCommission": {"type": "number", "format": "double"}, "totalGmv": {"type": "number", "format": "double"}, "totalItemsSold": {"type": "integer", "format": "int32"}, "totalProducts": {"type": "integer", "format": "int32"}, "unit": {"type": "string"}}}, "TkshopCreatorExpiredAutoTags": {"title": "TkshopCreatorExpiredAutoTags", "type": "object", "properties": {"add_friend": {"type": "string"}, "expiredDays": {"type": "integer", "format": "int32"}, "im_chat": {"type": "string"}, "sample_approve_pass": {"type": "string"}, "sample_approve_reject": {"type": "string"}, "send_email": {"type": "string"}, "send_msg": {"type": "string"}, "sync_creator": {"type": "string"}, "target_plan": {"type": "string"}}}, "TkshopCreatorFiledVo": {"title": "TkshopCreatorFiledVo", "type": "object", "properties": {"desc": {"type": "string", "description": "字段描述（可能为null）"}, "filedType": {"type": "string", "enum": ["CreatorColumn", "CreatorContact", "CreatorProp", "CreatorStat", "ProductColumn"]}, "force": {"type": "boolean", "description": "方案必须包含", "example": false}, "key": {"type": "string", "description": "字段Key"}, "label": {"type": "string", "description": "字段名称"}, "path": {"type": "string"}, "policySupported": {"type": "boolean"}, "sortable": {"type": "boolean"}, "valueType": {"type": "string", "enum": ["number", "string"]}}}, "TkshopCreatorLiveDto": {"title": "TkshopCreatorLiveDto", "type": "object", "properties": {"creatorId": {"type": "integer", "format": "int64"}, "endTime": {"type": "string", "format": "date-time"}, "estCommission": {"type": "number", "format": "double"}, "gmv": {"type": "number", "format": "double"}, "id": {"type": "integer", "format": "int64"}, "itemsSold": {"type": "integer", "format": "int32"}, "mediaAvatar": {"type": "string"}, "mediaId": {"type": "string"}, "mediaName": {"type": "string"}, "mediaUrl": {"type": "string"}, "shopId": {"type": "integer", "format": "int64"}, "startTime": {"type": "string", "format": "date-time"}, "teamId": {"type": "integer", "format": "int64"}, "unit": {"type": "string"}}}, "TkshopCreatorLiveVo": {"title": "TkshopCreatorLiveVo", "type": "object", "properties": {"creatorId": {"type": "integer", "format": "int64"}, "endTime": {"type": "string", "format": "date-time"}, "estCommission": {"type": "number", "format": "double"}, "gmv": {"type": "number", "format": "double"}, "id": {"type": "integer", "format": "int64"}, "itemsSold": {"type": "integer", "format": "int32"}, "live": {"$ref": "#/components/schemas/TkshopLiveDto"}, "mediaAvatar": {"type": "string"}, "mediaId": {"type": "string"}, "mediaName": {"type": "string"}, "mediaUrl": {"type": "string"}, "products": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopLiveProductDto"}}, "shopId": {"type": "integer", "format": "int64"}, "startTime": {"type": "string", "format": "date-time"}, "teamId": {"type": "integer", "format": "int64"}, "unit": {"type": "string"}}}, "TkshopCreatorProductDto": {"title": "TkshopCreatorProductDto", "type": "object", "properties": {"creatorId": {"type": "integer", "format": "int64"}, "estCommission": {"type": "number", "format": "double"}, "gmv": {"type": "number", "format": "double"}, "id": {"type": "integer", "format": "int64"}, "itemsSold": {"type": "integer", "format": "int32"}, "productAvatar": {"type": "string"}, "productName": {"type": "string"}, "productNo": {"type": "string"}, "shopId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}, "unit": {"type": "string"}}}, "TkshopCreatorProductVo": {"title": "TkshopCreatorProductVo", "type": "object", "properties": {"address": {"type": "string"}, "alias": {"type": "string"}, "analysisCreatorId": {"type": "string"}, "avatar": {"type": "string"}, "basicSyncTime": {"type": "string", "format": "date-time"}, "bio": {"type": "string"}, "categories": {"type": "string"}, "cid": {"type": "string"}, "contentType": {"type": "string", "enum": ["All", "LIVE", "Video"]}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "string"}, "deleteRemark": {"type": "string"}, "deleteTime": {"type": "string", "format": "date-time"}, "deleting": {"type": "boolean"}, "followerCnt": {"type": "integer", "format": "int32"}, "gmv": {"type": "number", "format": "double"}, "handle": {"type": "string"}, "hasNewMsg": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "itemsSold": {"type": "integer", "format": "int32"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "orderCount": {"type": "integer", "format": "int32"}, "products": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopVideoProductDto"}}, "region": {"type": "string"}, "remark": {"type": "string"}, "responsibleUserId": {"type": "integer", "format": "int64"}, "roi": {"type": "number", "format": "double"}, "source": {"type": "integer", "format": "int64"}, "status": {"type": "string", "enum": ["Collaborating", "HasOrder", "NotContacted", "<PERSON><PERSON>"]}, "teamId": {"type": "integer", "format": "int64"}, "totalCommission": {"type": "number", "format": "double"}, "totalGmv": {"type": "number", "format": "double"}, "totalItemsSold": {"type": "integer", "format": "int32"}, "totalProducts": {"type": "integer", "format": "int32"}, "unit": {"type": "string"}}}, "TkshopCreatorRoiDto": {"title": "TkshopCreatorRoiDto", "type": "object", "properties": {"adsCommissionCost": {"type": "number", "format": "bigdecimal"}, "adsCost": {"type": "number", "format": "bigdecimal"}, "commissionCost": {"type": "number", "format": "bigdecimal"}, "creatorId": {"type": "integer", "format": "int64"}, "gmv": {"type": "number", "format": "bigdecimal"}, "id": {"type": "integer", "format": "int64"}, "inputAmount": {"type": "number", "format": "bigdecimal"}, "itemSold": {"type": "integer", "format": "int32"}, "lives": {"type": "integer", "format": "int32"}, "outputAmount": {"type": "number", "format": "bigdecimal"}, "products": {"type": "integer", "format": "int32"}, "roi": {"type": "number", "format": "double"}, "sampleRequest": {"type": "integer", "format": "int32"}, "sampleRequestCost": {"type": "number", "format": "bigdecimal"}, "shops": {"type": "integer", "format": "int32"}, "teamId": {"type": "integer", "format": "int64"}, "unit": {"type": "string"}, "updateTime": {"type": "string", "format": "date-time"}, "videos": {"type": "integer", "format": "int32"}}}, "TkshopCreatorShopDto": {"title": "TkshopCreatorShopDto", "type": "object", "properties": {"commission": {"type": "number", "format": "bigdecimal"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "gmv": {"type": "number", "format": "bigdecimal"}, "id": {"type": "integer", "format": "int64"}, "itemsSold": {"type": "integer", "format": "int32"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "lives": {"type": "integer", "format": "int32"}, "orderCount": {"type": "integer", "format": "int32"}, "products": {"type": "integer", "format": "int32"}, "roi": {"type": "number", "format": "double"}, "shopId": {"type": "integer", "format": "int64"}, "status": {"type": "string", "enum": ["Collaborating", "HasOrder", "NotContacted", "<PERSON><PERSON>"]}, "teamId": {"type": "integer", "format": "int64"}, "unit": {"type": "string"}, "videos": {"type": "integer", "format": "int32"}}}, "TkshopCreatorSrDetailVo": {"title": "TkshopCreatorSrDetailVo", "type": "object", "properties": {"achievedShopIds": {"type": "array", "description": "合作店铺", "items": {"type": "integer", "format": "int64"}}, "address": {"type": "string"}, "alias": {"type": "string"}, "analysisCreatorId": {"type": "string"}, "avatar": {"type": "string"}, "basicSyncTime": {"type": "string", "format": "date-time"}, "bio": {"type": "string"}, "categories": {"type": "string"}, "categoryList": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopCategoryDto"}}, "cid": {"type": "string"}, "contactMap": {"type": "object", "additionalProperties": {"type": "string"}}, "contentType": {"type": "string", "enum": ["All", "LIVE", "Video"]}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "string"}, "creatorRoi": {"description": "达人的ROI", "$ref": "#/components/schemas/TkshopCreatorRoiDto"}, "deleteRemark": {"type": "string"}, "deleteTime": {"type": "string", "format": "date-time"}, "deleting": {"type": "boolean"}, "favorite": {"type": "boolean", "description": "是否关注", "example": false}, "followerCnt": {"type": "integer", "format": "int32"}, "friendshipList": {"type": "array", "description": "好友关系", "items": {"$ref": "#/components/schemas/TkshopContactFriendshipVo"}}, "gmv": {"type": "number", "format": "double"}, "handle": {"type": "string"}, "hasNewMsg": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "itemsSold": {"type": "integer", "format": "int32"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "orderCount": {"type": "integer", "format": "int32"}, "region": {"type": "string"}, "remark": {"type": "string"}, "responsibleUser": {"description": "认领人", "$ref": "#/components/schemas/UserDto"}, "responsibleUserId": {"type": "integer", "format": "int64"}, "roi": {"type": "number", "format": "double"}, "source": {"type": "integer", "format": "int64"}, "statList": {"type": "array", "description": "统计数据", "items": {"$ref": "#/components/schemas/TkshopCreatorStatVo"}}, "status": {"type": "string", "enum": ["Collaborating", "HasOrder", "NotContacted", "<PERSON><PERSON>"]}, "tags": {"type": "array", "description": "标签", "items": {"$ref": "#/components/schemas/TagDto"}}, "teamId": {"type": "integer", "format": "int64"}, "totalCommission": {"type": "number", "format": "double"}, "totalGmv": {"type": "number", "format": "double"}, "totalItemsSold": {"type": "integer", "format": "int32"}, "totalProducts": {"type": "integer", "format": "int32"}, "unit": {"type": "string"}}}, "TkshopCreatorStatVo": {"title": "TkshopCreatorStatVo", "type": "object", "properties": {"dimension": {"type": "string"}, "unit": {"type": "string"}, "value": {"type": "number", "format": "double"}}}, "TkshopCreatorVideoDto": {"title": "TkshopCreatorVideoDto", "type": "object", "properties": {"creatorId": {"type": "integer", "format": "int64"}, "estCommission": {"type": "number", "format": "double"}, "gmv": {"type": "number", "format": "double"}, "id": {"type": "integer", "format": "int64"}, "itemsSold": {"type": "integer", "format": "int32"}, "mediaAvatar": {"type": "string"}, "mediaId": {"type": "string"}, "mediaName": {"type": "string"}, "mediaUrl": {"type": "string"}, "postTime": {"type": "string", "format": "date-time"}, "shopId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}, "unit": {"type": "string"}}}, "TkshopCreatorVideoVo": {"title": "TkshopCreatorVideoVo", "type": "object", "properties": {"creatorId": {"type": "integer", "format": "int64"}, "estCommission": {"type": "number", "format": "double"}, "gmv": {"type": "number", "format": "double"}, "id": {"type": "integer", "format": "int64"}, "itemsSold": {"type": "integer", "format": "int32"}, "mediaAvatar": {"type": "string"}, "mediaId": {"type": "string"}, "mediaName": {"type": "string"}, "mediaUrl": {"type": "string"}, "postTime": {"type": "string", "format": "date-time"}, "products": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopVideoProductDto"}}, "shopId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}, "unit": {"type": "string"}, "video": {"$ref": "#/components/schemas/TkshopVideoDto"}}}, "TkshopGlobalCreatorDetailVo": {"title": "TkshopGlobalCreatorDetailVo", "type": "object", "properties": {"address": {"type": "string"}, "alias": {"type": "string"}, "avatar": {"type": "string"}, "basicSyncTime": {"type": "string", "format": "date-time"}, "bio": {"type": "string"}, "categories": {"type": "string"}, "categoryList": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopCategoryDto"}}, "cid": {"type": "string"}, "contactMap": {"type": "object", "additionalProperties": {"type": "string"}, "description": "联系信息"}, "contentType": {"type": "string", "enum": ["All", "LIVE", "Video"]}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "string"}, "followerCnt": {"type": "integer", "format": "int32"}, "gmv": {"type": "number", "format": "double"}, "handle": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "itemsSold": {"type": "integer", "format": "int32"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "region": {"type": "string"}, "remark": {"type": "string"}, "source": {"type": "integer", "format": "int32"}, "statList": {"type": "array", "description": "统计数据", "items": {"$ref": "#/components/schemas/TkshopCreatorStatVo"}}}}, "TkshopHasInteractionRequest": {"title": "TkshopHasInteractionRequest", "type": "object", "properties": {"handlers": {"type": "array", "items": {"type": "string"}}, "interactTimeFrom": {"type": "integer", "format": "int64"}, "interactTimeTo": {"type": "integer", "format": "int64"}, "interactType": {"type": "string", "enum": ["add_friend", "collaborating", "has_order", "im_chat", "manual_remark", "sample_request", "send_email", "send_facebook", "send_line", "send_whatsapp", "send_zalo", "target_plan"]}, "shopId": {"type": "integer", "format": "int64"}}}, "TkshopHealthMetricDto": {"title": "TkshopHealthMetricDto", "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string", "enum": ["ses_fulfillment", "ses_metric_FDR_PC", "ses_metric_complaint_rate", "ses_metric_im_response_rate", "ses_metric_neg_review_rate", "ses_metric_quality_rr_rate", "ses_product", "ses_service", "ses_ses_metric_seller_fault_cancel"]}, "rank": {"type": "integer", "format": "int32"}, "rawScore": {"type": "string"}, "score": {"type": "number", "format": "double"}, "shopId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}, "type": {"type": "string"}}}, "TkshopHealthMetricVo": {"title": "TkshopHealthMetricVo", "type": "object", "properties": {"name": {"type": "string"}, "rank": {"type": "integer", "format": "int32"}, "rawScore": {"type": "string"}, "score": {"type": "number", "format": "double"}, "type": {"type": "string"}}}, "TkshopInteractionDto": {"title": "TkshopInteractionDto", "type": "object", "properties": {"creatorId": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "extraInfo": {"type": "string"}, "flowId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "interactTime": {"type": "string", "format": "date-time"}, "interactType": {"type": "string", "enum": ["add_friend", "collaborating", "has_order", "im_chat", "manual_remark", "sample_request", "send_email", "send_facebook", "send_line", "send_whatsapp", "send_zalo", "target_plan"]}, "interactionId": {"type": "string"}, "jobId": {"type": "integer", "format": "int64"}, "shopId": {"type": "integer", "format": "int64"}, "shopName": {"type": "string"}, "status": {"type": "string"}, "targetId": {"type": "integer", "format": "int64"}, "taskId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}, "updateTime": {"type": "string", "format": "date-time"}, "userId": {"type": "integer", "format": "int64"}}}, "TkshopLiveDto": {"title": "TkshopLiveDto", "type": "object", "properties": {"commentCnt": {"type": "integer", "format": "int32"}, "creatorId": {"type": "integer", "format": "int64"}, "endTime": {"type": "string", "format": "date-time"}, "estCommission": {"type": "number", "format": "double"}, "gmv": {"type": "number", "format": "double"}, "id": {"type": "integer", "format": "int64"}, "itemsSold": {"type": "integer", "format": "int32"}, "likeCnt": {"type": "integer", "format": "int32"}, "mediaAvatar": {"type": "string"}, "mediaId": {"type": "string"}, "mediaName": {"type": "string"}, "mediaUrl": {"type": "string"}, "orderCount": {"type": "integer", "format": "int32"}, "remark": {"type": "string"}, "startTime": {"type": "string", "format": "date-time"}, "teamId": {"type": "integer", "format": "int64"}, "unit": {"type": "string"}, "viewCnt": {"type": "integer", "format": "int32"}}}, "TkshopLiveProductDto": {"title": "TkshopLiveProductDto", "type": "object", "properties": {"creatorId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "mediaId": {"type": "string"}, "productAvatar": {"type": "string"}, "productName": {"type": "string"}, "productNo": {"type": "string"}, "shopId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}}}, "TkshopLiveVo": {"title": "TkshopLiveVo", "type": "object", "properties": {"commentCnt": {"type": "integer", "format": "int32"}, "creator": {"$ref": "#/components/schemas/TkshopCreatorDto"}, "creatorId": {"type": "integer", "format": "int64"}, "endTime": {"type": "string", "format": "date-time"}, "estCommission": {"type": "number", "format": "double"}, "gmv": {"type": "number", "format": "double"}, "id": {"type": "integer", "format": "int64"}, "itemsSold": {"type": "integer", "format": "int32"}, "likeCnt": {"type": "integer", "format": "int32"}, "mediaAvatar": {"type": "string"}, "mediaId": {"type": "string"}, "mediaName": {"type": "string"}, "mediaUrl": {"type": "string"}, "orderCount": {"type": "integer", "format": "int32"}, "products": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopLiveProductDto"}}, "remark": {"type": "string"}, "sampleRequestApplyIds": {"type": "array", "items": {"type": "string"}}, "shopIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "startTime": {"type": "string", "format": "date-time"}, "teamId": {"type": "integer", "format": "int64"}, "unit": {"type": "string"}, "viewCnt": {"type": "integer", "format": "int32"}}}, "TkshopOrderCountVo": {"title": "TkshopOrderCountVo", "type": "object", "properties": {"otherCount": {"type": "integer", "description": "自然流量订单数量", "format": "int32"}, "promotionCount": {"type": "integer", "description": "带货订单数量", "format": "int32"}, "sampleCount": {"type": "integer", "description": "索样订单数量", "format": "int32"}, "shopCount": {"type": "integer", "description": "店铺数", "format": "int32"}, "total": {"type": "integer", "description": "总量", "format": "int32"}}}, "TkshopOrderDocument": {"title": "TkshopOrderDocument", "type": "object", "properties": {"amount": {"type": "number", "description": "该订单付款总额", "format": "bigdecimal"}, "buyer": {"description": "买家信息", "$ref": "#/components/schemas/TkshopBuyerDocument"}, "buyerNote": {"type": "string", "description": "买家备注"}, "createTimestamp": {"type": "integer", "description": "这个订单的创建时间", "format": "int64"}, "currency": {"type": "string", "description": "使用的是哪种货币，What your buyer paid那里可以看到"}, "freeSample": {"type": "boolean", "description": "是否索样订单", "example": false}, "items": {"type": "array", "description": "订单条目", "items": {"$ref": "#/components/schemas/TkshopOrderItemDocument"}}, "location": {"type": "string", "description": "对应订单页面的Location字段"}, "orderNo": {"type": "string"}, "phone": {"type": "string"}, "shipAddress": {"type": "string"}, "status": {"type": "string", "description": "订单状态", "enum": ["cancellation", "completed", "fail_delivery", "pending", "shipped", "to_ship"]}}}, "TkshopOrderHasPhoneVo": {"title": "TkshopOrderHasPhoneVo", "type": "object", "properties": {"hasPhone": {"type": "boolean"}, "orderNo": {"type": "string"}, "phone": {"type": "string"}}}, "TkshopOrderItemDocument": {"title": "TkshopOrderItemDocument", "type": "object", "properties": {"commissionHandle": {"type": "string", "description": "提成达人handle"}, "productAvatar": {"type": "string", "description": "产品图片"}, "productName": {"type": "string"}, "productNo": {"type": "string"}, "quantity": {"type": "integer", "description": "买了几个", "format": "int32"}, "skuDesc": {"type": "string"}, "skuId": {"type": "string"}, "skuName": {"type": "string"}, "unit": {"type": "string", "description": "价格单位"}, "unitPrice": {"type": "number", "description": "产品单价", "format": "bigdecimal"}}}, "TkshopOrderItemVo": {"title": "TkshopOrderItemVo", "type": "object", "properties": {"adsCosFee": {"type": "number", "format": "bigdecimal"}, "adsCosRatio": {"type": "number", "format": "double"}, "affiliateSyncTime": {"type": "string", "format": "date-time"}, "amount": {"type": "number", "format": "bigdecimal"}, "commissionCreatorId": {"type": "integer", "format": "int64"}, "commissionHandle": {"type": "string"}, "cosFee": {"type": "number", "format": "bigdecimal"}, "cosRatio": {"type": "number", "format": "double"}, "id": {"type": "integer", "format": "int64"}, "mediaId": {"type": "string"}, "mediaName": {"type": "string"}, "orderId": {"type": "integer", "format": "int64"}, "productAvatar": {"type": "string"}, "productName": {"type": "string"}, "productNo": {"type": "string"}, "promotionType": {"type": "string", "enum": ["AffiliateProductPage", "ExternalTrafficProgram", "Livestream", "Showcase", "Video"]}, "quantity": {"type": "integer", "format": "int32"}, "skuDesc": {"type": "string"}, "skuId": {"type": "string"}, "skuName": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}, "unit": {"type": "string"}, "unitPrice": {"type": "number", "format": "bigdecimal"}}}, "TkshopOrderStatVo": {"title": "TkshopOrderStatVo", "type": "object", "properties": {"orderCount": {"type": "integer", "format": "int32"}, "totalAmount": {"type": "number", "format": "bigdecimal"}, "totalCosFee": {"type": "number", "format": "bigdecimal"}, "totalQuantity": {"type": "integer", "format": "int32"}}}, "TkshopOrderVo": {"title": "TkshopOrderVo", "type": "object", "properties": {"amount": {"type": "number", "format": "bigdecimal"}, "buyer": {"$ref": "#/components/schemas/TkshopBuyerDetailVo"}, "buyerHandle": {"type": "string"}, "buyerId": {"type": "integer", "format": "int64"}, "buyerNote": {"type": "string"}, "completeTime": {"type": "string", "format": "date-time"}, "contactUrl": {"type": "string"}, "cosFee": {"type": "number", "format": "bigdecimal"}, "createTime": {"type": "string", "format": "date-time"}, "currency": {"type": "string"}, "deliveryTime": {"type": "string", "format": "date-time"}, "freeSample": {"type": "boolean"}, "hasCommissionHandle": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopOrderItemVo"}}, "lastSyncTime": {"type": "string", "format": "date-time"}, "location": {"type": "string"}, "orderNo": {"type": "string"}, "phone": {"type": "string"}, "quantity": {"type": "integer", "format": "int32"}, "settlementTime": {"type": "string", "format": "date-time"}, "shipAddress": {"type": "string"}, "shop": {"$ref": "#/components/schemas/ShopWithPlatformVo"}, "shopId": {"type": "integer", "format": "int64"}, "status": {"type": "string", "enum": ["cancellation", "completed", "fail_delivery", "pending", "shipped", "to_ship"]}, "teamId": {"type": "integer", "format": "int64"}}}, "TkshopPlanChainGroupVo": {"title": "TkshopPlanChainGroupVo", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "layoutId": {"type": "integer", "format": "int64"}, "shop": {"description": "shopId对应的分身，如果为空说明分身可能被删除了", "$ref": "#/components/schemas/ShopBriefVo"}, "shopAuthorized": {"type": "boolean", "description": "计划的店铺对当前用户是不授权了（仅当查询当前用户自己的计划时才会有返回值）", "example": false}, "shopId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}}}, "TkshopPlanChainLayoutVo": {"title": "TkshopPlanChainLayoutVo", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "device": {"description": "如果对应的设备已经删除了该字段为空", "$ref": "#/components/schemas/LoginDeviceDto"}, "deviceId": {"type": "string"}, "enabled": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "planCount": {"type": "integer", "description": "计划个数", "format": "int32"}, "shops": {"type": "array", "description": "店铺列表", "items": {"$ref": "#/components/schemas/ShopBriefVo"}}, "teamId": {"type": "integer", "format": "int64"}}}, "TkshopPlanVo": {"title": "TkshopPlanVo", "type": "object", "properties": {"bizScene": {"type": "string", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}, "createTime": {"type": "string", "format": "date-time"}, "creatorFilter": {"type": "string", "description": "达人筛选条件，json格式"}, "cronExpression": {"type": "string", "description": "重复日期，只包含星期信息的表达式(具体参考自动流程计划)"}, "delayTime": {"type": "integer", "description": "如果是触发计划，触发的延时时间", "format": "int32"}, "enabled": {"type": "boolean"}, "expressions": {"type": "array", "description": "如果是随机执行，用-隔开开始时间和结束时间[start, start-end, start, ...]", "items": {"type": "string"}}, "id": {"type": "integer", "format": "int64"}, "jobType": {"type": "string", "enum": ["Acco<PERSON><PERSON><PERSON><PERSON>", "AccountHealthCheck", "AccountMaintenance", "AccountMessageCheck", "KOL_LiveRewards", "<PERSON><PERSON><PERSON><PERSON>", "SendInvite", "SendPm", "SendUserCard", "ShareVideo", "<PERSON><PERSON><PERSON>", "SyncContactToPhone", "SyncContacts", "SyncCreator", "SyncLiveStream", "SyncPm", "TS_AddContacts", "TS_AddFriends", "TS_DemandPayment", "TS_IMChatByFilter", "TS_IMChatByHandle", "TS_LoginCheck", "TS_SampleApprove", "TS_SendBuyerIMChat", "TS_SendEmail", "TS_SendFacebook", "TS_SendLine", "TS_SendWhatsApp", "TS_SendZalo", "TS_SyncBuyerInfo", "TS_SyncCreator", "TS_SyncOrders", "TS_SyncProducts", "TS_SyncSampleCreator", "TS_SyncShopInfo", "TS_SyncVideos", "TS_TargetPlanByFilter", "TS_TargetPlanByHandle", "TS_TargetPlanClear", "TS_VideoADCode"]}, "name": {"type": "string"}, "nextFireTime": {"type": "string", "description": "下次执行时间", "format": "date-time"}, "params": {"type": "string"}, "parentId": {"type": "integer", "description": "如果是触发计划，父计划的ID", "format": "int64"}, "planGroupId": {"type": "integer", "format": "int64"}, "planType": {"type": "string", "enum": ["Team", "User"]}, "teamId": {"type": "integer", "format": "int64"}, "triggerPolicy": {"type": "string", "description": "触发条件 触发策略", "enum": ["Always", "OnSuccess"]}, "triggerType": {"type": "string", "description": "计划的触发类型", "enum": ["Following", "Timing"]}, "userId": {"type": "integer", "format": "int64"}}}, "TkshopProductDto": {"title": "TkshopProductDto", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "itemsSold": {"type": "integer", "format": "int32"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "planEnabled": {"type": "boolean"}, "planRemark": {"type": "string"}, "price": {"type": "number", "format": "bigdecimal"}, "priceUnit": {"type": "string"}, "productAvatar": {"type": "string"}, "productName": {"type": "string"}, "productNo": {"type": "string"}, "quantity": {"type": "integer", "format": "int32"}, "regions": {"type": "string"}, "remark": {"type": "string"}, "shopId": {"type": "integer", "format": "int64"}, "status": {"type": "string", "enum": ["Deactivated", "Deleted", "Draft", "Live", "Reviewing", "Suspended"]}, "teamId": {"type": "integer", "format": "int64"}, "updateTime": {"type": "string", "format": "date-time"}}}, "TkshopReportHealthRequest": {"title": "TkshopReportHealthRequest", "type": "object", "properties": {"metrics": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopHealthMetricVo"}}, "rank": {"type": "integer", "format": "int32"}, "score": {"type": "number", "format": "double"}, "status": {"type": "string", "enum": ["NotRestricted", "Restricted", "ShopClose"]}}}, "TkshopSampleRequestAuditVo": {"title": "TkshopSampleRequestAuditVo", "type": "object", "properties": {"applyId": {"type": "string"}, "applyTime": {"type": "string", "format": "date-time"}, "approved": {"type": "boolean", "description": "同意或拒绝或不匹配（=null）", "example": false}, "avatar": {"type": "string"}, "commissionRate": {"type": "number", "format": "double"}, "contactMap": {"type": "object", "additionalProperties": {"type": "string"}}, "creator": {"$ref": "#/components/schemas/TkshopCreatorSrDetailVo"}, "creatorId": {"type": "string"}, "expiredTime": {"type": "string", "format": "date-time"}, "groupId": {"type": "string"}, "handle": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "lives": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopLiveDto"}}, "message": {"type": "string", "description": "原因"}, "pendingApproved": {"type": "boolean", "description": "任务抽屉是否已经处理", "example": false}, "pendingDrawer": {"$ref": "#/components/schemas/TkshopTaskDrawerDto"}, "pendingDrawerId": {"type": "integer", "format": "int64"}, "product": {"$ref": "#/components/schemas/TkshopProductDto"}, "productName": {"type": "string"}, "productNo": {"type": "string"}, "reviewTime": {"type": "string", "format": "date-time"}, "shopId": {"type": "integer", "format": "int64"}, "skuDesc": {"type": "string"}, "skuId": {"type": "string"}, "skuImage": {"type": "string"}, "status": {"type": "string", "enum": ["Cancelled", "Completed", "Expired", "InProgress", "ReadyToShip", "Reject", "Shipped", "ToReview"]}, "tcId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}, "videos": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopVideoDto"}}}}, "TkshopSampleRequestDetailVo": {"title": "TkshopSampleRequestDetailVo", "type": "object", "properties": {"applyId": {"type": "string"}, "applyTime": {"type": "string", "format": "date-time"}, "avatar": {"type": "string"}, "commissionRate": {"type": "number", "format": "double"}, "contactMap": {"type": "object", "additionalProperties": {"type": "string"}}, "creator": {"$ref": "#/components/schemas/TkshopCreatorSrDetailVo"}, "creatorId": {"type": "string"}, "expiredTime": {"type": "string", "format": "date-time"}, "groupId": {"type": "string"}, "handle": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "lives": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopLiveDto"}}, "pendingApproved": {"type": "boolean", "description": "任务抽屉是否已经处理", "example": false}, "pendingDrawer": {"$ref": "#/components/schemas/TkshopTaskDrawerDto"}, "pendingDrawerId": {"type": "integer", "format": "int64"}, "product": {"$ref": "#/components/schemas/TkshopProductDto"}, "productName": {"type": "string"}, "productNo": {"type": "string"}, "reviewTime": {"type": "string", "format": "date-time"}, "shopId": {"type": "integer", "format": "int64"}, "skuDesc": {"type": "string"}, "skuId": {"type": "string"}, "skuImage": {"type": "string"}, "status": {"type": "string", "enum": ["Cancelled", "Completed", "Expired", "InProgress", "ReadyToShip", "Reject", "Shipped", "ToReview"]}, "tcId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}, "videos": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopVideoDto"}}}}, "TkshopSampleRequestPolicyVo": {"title": "TkshopSampleRequestPolicyVo", "type": "object", "properties": {"approved": {"type": "boolean"}, "conditionList": {"type": "array", "items": {"$ref": "#/components/schemas/SampleRequestConditionVo"}}, "conditions": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "logicalCondition": {"type": "string", "enum": ["AND", "NOT", "OR"]}, "policyDesc": {"type": "string"}, "policyName": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}}}, "TkshopSearchProfileDto": {"title": "TkshopSearchProfileDto", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "params": {"type": "string"}, "profileType": {"type": "string", "enum": ["<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TkshopDeletingCreator", "TkshopFavoriteCreator", "TkshopGlobalCreator", "TkshopLive", "TkshopOrder", "TkshopProduct", "TkshopVideo"]}, "teamId": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64"}}}, "TkshopShopBuyerInfo": {"title": "TkshopShopBuyerInfo", "type": "object", "properties": {"buyerIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "shopId": {"type": "integer", "format": "int64"}}}, "TkshopShopBuyerResult": {"title": "TkshopShopBuyerResult", "type": "object", "properties": {"shopBuyersList": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopShopBuyerInfo"}}}}, "TkshopShopVo": {"title": "TkshopShopVo", "type": "object", "properties": {"firstOrderSyncTime": {"type": "string", "format": "date-time"}, "healthRank": {"type": "integer", "format": "int32"}, "healthScore": {"type": "number", "format": "double"}, "healthStatus": {"type": "string", "enum": ["NotRestricted", "Restricted", "ShopClose"]}, "id": {"type": "integer", "format": "int64"}, "lastOrderSyncTime": {"type": "string", "format": "date-time"}, "metrics": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopHealthMetricDto"}}, "teamId": {"type": "integer", "format": "int64"}}}, "TkshopSyncInvitationRequest": {"title": "TkshopSyncInvitationRequest", "type": "object", "properties": {"invitations": {"type": "array", "items": {"$ref": "#/components/schemas/InvitationDocument"}}, "shopId": {"type": "integer", "format": "int64"}}}, "TkshopSyncOrderRequest": {"title": "TkshopSyncOrderRequest", "type": "object", "properties": {"orders": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopOrderDocument"}}, "region": {"type": "string"}, "shopId": {"type": "integer", "format": "int64"}}}, "TkshopSystemStatusVo": {"title": "TkshopSystemStatusVo", "type": "object", "properties": {"flows": {"type": "array", "description": "RPA流程", "items": {"$ref": "#/components/schemas/RpaFlowVo"}}, "order": {"description": "购买套餐的订单", "$ref": "#/components/schemas/OrderDetailVo"}, "orderItem": {"description": "购买套餐的订单条目", "$ref": "#/components/schemas/OrderItemDto"}, "shopCount": {"type": "integer", "description": "已用配额", "format": "int32"}, "shops": {"type": "array", "description": "创建的账号", "items": {"$ref": "#/components/schemas/ShopDto"}}, "team": {"description": "创建的团队信息", "$ref": "#/components/schemas/TeamDto"}, "teamQuotas": {"type": "array", "description": "团队配额信息", "items": {"$ref": "#/components/schemas/TeamQuotaVo"}}, "tkshopTeam": {"description": "过期时间相关", "$ref": "#/components/schemas/TkshopTeamDto"}, "userCount": {"type": "integer", "description": "成员数目", "format": "int32"}}}, "TkshopTaskDrawerDto": {"title": "TkshopTaskDrawerDto", "type": "object", "properties": {"accountId": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "deviceId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "parameter": {"type": "string"}, "rpaType": {"type": "string", "enum": ["Browser", "Extension", "IOS", "Mobile"]}, "taskKey": {"type": "string"}, "taskName": {"type": "string"}, "taskType": {"type": "string", "enum": ["TS_IMChatByFilter", "TS_IMChatByHandle", "TS_SampleApprove", "TS_SendBuyerIMChat", "TS_SendLine", "TS_SendWhatsApp", "TS_SyncBuyerInfo", "TS_SyncCreator", "TS_SyncOrders", "TS_SyncSampleCreator", "TS_SyncShopInfo", "TS_SyncVideos", "TS_TargetPlanByFilter", "TS_TargetPlanByHandle", "TS_VideoADCode"]}, "teamId": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64"}}}, "TkshopTaskDrawerVo": {"title": "TkshopTaskDrawerVo", "type": "object", "properties": {"accountId": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "deviceId": {"type": "integer", "format": "int64"}, "extensionType": {"type": "string", "description": "关联分身的插件类型", "enum": ["both", "extension", "huayang"]}, "id": {"type": "integer", "format": "int64"}, "parameter": {"type": "string"}, "rpaType": {"type": "string", "enum": ["Browser", "Extension", "IOS", "Mobile"]}, "taskKey": {"type": "string"}, "taskName": {"type": "string"}, "taskType": {"type": "string", "enum": ["TS_IMChatByFilter", "TS_IMChatByHandle", "TS_SampleApprove", "TS_SendBuyerIMChat", "TS_SendLine", "TS_SendWhatsApp", "TS_SyncBuyerInfo", "TS_SyncCreator", "TS_SyncOrders", "TS_SyncSampleCreator", "TS_SyncShopInfo", "TS_SyncVideos", "TS_TargetPlanByFilter", "TS_TargetPlanByHandle", "TS_VideoADCode"]}, "teamId": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64"}}}, "TkshopTeamDto": {"title": "TkshopTeamDto", "type": "object", "properties": {"allocateCreatorEnabled": {"type": "boolean"}, "autoRenew": {"type": "boolean"}, "buyerChatQuota": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "customPrice": {"type": "boolean"}, "duration": {"type": "integer", "format": "int32"}, "extraParams": {"type": "string"}, "goodsId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "imChatQuota": {"type": "integer", "format": "int32"}, "invalidateTime": {"type": "string", "format": "date-time"}, "listPrice": {"type": "number", "format": "bigdecimal"}, "mobileQuota": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "periodUnit": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}, "realPrice": {"type": "number", "format": "bigdecimal"}, "renewPending": {"type": "boolean"}, "renewPrice": {"type": "number", "format": "bigdecimal"}, "shopQuota": {"type": "integer", "format": "int32"}, "syncVideoQuota": {"type": "integer", "format": "int32"}, "targetPlanQuota": {"type": "integer", "format": "int32"}, "userQuota": {"type": "integer", "format": "int32"}, "valid": {"type": "boolean"}, "validEndDate": {"type": "string", "format": "date-time"}, "version": {"type": "string", "enum": ["TkshopEnterprise", "TkshopStandard"]}}}, "TkshopTrialCodeDto": {"title": "TkshopTrialCodeDto", "type": "object", "properties": {"code": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "partnerId": {"type": "integer", "format": "int64"}, "remark": {"type": "string"}, "shopQuota": {"type": "integer", "format": "int32"}, "userQuota": {"type": "integer", "format": "int32"}, "valid": {"type": "boolean"}, "validSeconds": {"type": "integer", "format": "int32"}, "version": {"type": "string", "enum": ["TkshopEnterprise", "TkshopStandard"]}}}, "TkshopVersionConfig": {"title": "TkshopVersionConfig", "type": "object", "properties": {"basePrice": {"type": "number", "format": "double"}, "baseQuota": {"type": "object", "additionalProperties": {"type": "number"}}, "buyerChatQuota": {"type": "integer", "description": "批量私信买家数量", "format": "int32"}, "creditBase": {"type": "integer", "description": "基准赠送花瓣", "format": "int32"}, "creditPerShop": {"type": "integer", "description": "每个分身赠送花瓣", "format": "int32"}, "durationPrices": {"type": "array", "items": {"$ref": "#/components/schemas/DurationPriceVo"}}, "enableTransit": {"type": "boolean", "description": "自有IP允许使用接入点", "example": false}, "globalCreatorImportQuota": {"type": "integer", "description": "每日从公海达人库导入到团队的数量", "format": "int32"}, "imChatQuota": {"type": "integer", "description": "批量私信达人数量", "format": "int32"}, "ladderPrices": {"type": "array", "items": {"$ref": "#/components/schemas/LadderPriceVo"}}, "syncVideoQuota": {"type": "integer", "description": "关注带货视频的数量", "format": "int32"}, "targetPlanQuota": {"type": "integer", "description": "批量邀约达人数量", "format": "int32"}}}, "TkshopVideoDto": {"title": "TkshopVideoDto", "type": "object", "properties": {"adCode": {"type": "string"}, "autoSync": {"type": "boolean"}, "commentCnt": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "duration": {"type": "integer", "format": "int32"}, "estCommission": {"type": "number", "format": "double"}, "favoriteCnt": {"type": "integer", "format": "int32"}, "gmv": {"type": "number", "format": "double"}, "id": {"type": "integer", "format": "int64"}, "itemsSold": {"type": "integer", "format": "int32"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "likeCnt": {"type": "integer", "format": "int32"}, "markSyncTime": {"type": "string", "format": "date-time"}, "mediaAvatar": {"type": "string"}, "mediaId": {"type": "string"}, "mediaName": {"type": "string"}, "mediaUrl": {"type": "string"}, "orderCount": {"type": "integer", "format": "int32"}, "ordersPm": {"type": "number", "format": "bigdecimal"}, "postTime": {"type": "string", "format": "date-time"}, "remark": {"type": "string"}, "retweetCnt": {"type": "integer", "format": "int32"}, "teamId": {"type": "integer", "format": "int64"}, "unit": {"type": "string"}, "viewCnt": {"type": "integer", "format": "int32"}, "viewerCnt": {"type": "integer", "format": "int32"}}}, "TkshopVideoProductDto": {"title": "TkshopVideoProductDto", "type": "object", "properties": {"creatorId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "mediaId": {"type": "string"}, "productAvatar": {"type": "string"}, "productName": {"type": "string"}, "productNo": {"type": "string"}, "shopId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}}}, "TkshopVideoStatDto": {"title": "TkshopVideoStatDto", "type": "object", "properties": {"commentCnt": {"type": "integer", "format": "int32"}, "day": {"type": "string", "format": "date-time"}, "favoriteCnt": {"type": "integer", "format": "int32"}, "id": {"type": "integer", "format": "int64"}, "likeCnt": {"type": "integer", "format": "int32"}, "mediaId": {"type": "string"}, "retweetCnt": {"type": "integer", "format": "int32"}, "viewCnt": {"type": "integer", "format": "int32"}}}, "TkshopVideoVo": {"title": "TkshopVideoVo", "type": "object", "properties": {"adCode": {"type": "string"}, "autoSync": {"type": "boolean"}, "commentCnt": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "creator": {"$ref": "#/components/schemas/TkshopCreatorDto"}, "creatorId": {"type": "integer", "format": "int64"}, "duration": {"type": "integer", "format": "int32"}, "estCommission": {"type": "number", "format": "double"}, "favoriteCnt": {"type": "integer", "format": "int32"}, "gmv": {"type": "number", "format": "double"}, "id": {"type": "integer", "format": "int64"}, "itemsSold": {"type": "integer", "format": "int32"}, "lastOrderSyncTime": {"type": "string", "format": "date-time"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "likeCnt": {"type": "integer", "format": "int32"}, "markSyncTime": {"type": "string", "format": "date-time"}, "mediaAvatar": {"type": "string"}, "mediaId": {"type": "string"}, "mediaName": {"type": "string"}, "mediaUrl": {"type": "string"}, "orderCount": {"type": "integer", "format": "int32"}, "ordersPm": {"type": "number", "format": "bigdecimal"}, "ordersPmType": {"type": "string", "enum": ["abnormal", "bad", "good", "normal"]}, "postTime": {"type": "string", "format": "date-time"}, "products": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopVideoProductDto"}}, "remark": {"type": "string"}, "retweetCnt": {"type": "integer", "format": "int32"}, "sampleRequestApplyIds": {"type": "array", "items": {"type": "string"}}, "shopIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "teamId": {"type": "integer", "format": "int64"}, "unit": {"type": "string"}, "viewCnt": {"type": "integer", "format": "int32"}, "viewerCnt": {"type": "integer", "format": "int32"}}}, "UpdateContactRequest": {"title": "UpdateContactRequest", "type": "object", "properties": {"contactMap": {"type": "object", "additionalProperties": {"type": "string"}}}}, "UpdateLayoutDeviceRequest": {"title": "UpdateLayoutDeviceRequest", "type": "object", "properties": {"deviceId": {"type": "string"}, "layoutId": {"type": "integer", "format": "int64"}}}, "UpdateOneContactRequest": {"title": "UpdateOneContactRequest", "type": "object", "properties": {"contact": {"type": "string", "description": "联系方式，传空时表示未注册"}, "contactType": {"type": "string", "description": "联系方式类型", "enum": ["email", "f<PERSON><PERSON><PERSON><PERSON><PERSON>", "line", "phone", "viber", "whatsapp", "zalo"]}, "error": {"type": "string"}, "mobileAccountId": {"type": "integer", "description": "手机账号ID", "format": "int64"}, "mobileId": {"type": "integer", "description": "手机ID", "format": "int64"}, "status": {"type": "string", "description": "好友关系状态", "enum": ["Error", "NotFound", "Ready", "Unknown"]}}}, "UpdatePlanRequest": {"title": "UpdatePlanRequest", "type": "object", "properties": {"creatorFilter": {"type": "string", "description": "达人筛选条件，json格式"}, "cronExpression": {"type": "string", "description": "重复日期，只包含星期信息的表达式(具体参考自动流程计划)"}, "delayTime": {"type": "integer", "description": "如果是触发计划，触发的延时时间", "format": "int32"}, "expressions": {"type": "array", "description": "如果是随机执行，用-隔开开始时间和结束时间[start, start-end, start, ...]", "items": {"type": "string"}}, "id": {"type": "integer", "format": "int64"}, "params": {"type": "string"}, "triggerPolicy": {"type": "string", "description": "如果是触发计划，触发条件/触发策略", "enum": ["Always", "OnSuccess"]}}}, "UpdateRegionRequest": {"title": "UpdateRegionRequest", "type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "region": {"type": "string"}}}, "UpdateRemarkRequest": {"title": "UpdateRemarkRequest", "type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "remark": {"type": "string"}}}, "UpdateStatusRequest": {"title": "UpdateStatusRequest", "type": "object", "properties": {"handles": {"type": "array", "description": "达人列表", "items": {"type": "string"}}, "status": {"type": "string", "enum": ["Collaborating", "HasOrder", "NotContacted", "<PERSON><PERSON>"]}}}, "UserDto": {"title": "UserDto", "type": "object", "properties": {"avatar": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "gender": {"type": "string", "enum": ["FEMALE", "MALE", "UNSPECIFIC"]}, "id": {"type": "integer", "format": "int64"}, "nickname": {"type": "string"}, "partnerId": {"type": "integer", "format": "int64"}, "residentCity": {"type": "string"}, "signature": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "BLOCK", "DELETED", "INACTIVE"]}, "tenant": {"type": "integer", "format": "int64"}, "userType": {"type": "string", "enum": ["NORMAL", "PARTNER", "SHADOW"]}}}, "VideoAdCodeProductVo": {"title": "VideoAdCodeProductVo", "type": "object", "properties": {"productName": {"type": "string", "description": "商品名称"}, "productNo": {"type": "string", "description": "商品ID"}, "shopNo": {"type": "string", "description": "TK店铺ID"}}}, "VideoBriefVo": {"title": "VideoBriefVo", "type": "object", "properties": {"creatorId": {"type": "integer", "format": "int64"}, "handle": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "mediaId": {"type": "string"}}}, "WebResult": {"title": "WebResult", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«CalcTkshopPriceResponse»": {"title": "WebResult«CalcTkshopPriceResponse»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/CalcTkshopPriceResponse"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«CreateBuyTkPackOrderResponse»": {"title": "WebResult«CreateBuyTkPackOrderResponse»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/CreateBuyTkPackOrderResponse"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«CreateOrderResponse»": {"title": "WebResult«CreateOrderResponse»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/CreateOrderResponse"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«CreatorInteractStatVo»": {"title": "WebResult«CreatorInteractStatVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/CreatorInteractStatVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«FBUserProfile»": {"title": "WebResult«FBUserProfile»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/FBUserProfile"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«GhJobDto»": {"title": "WebResult«GhJobDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/GhJobDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«GhRpaBrowserPolicy»": {"title": "WebResult«GhRpaBrowserPolicy»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/GhRpaBrowserPolicy"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«ExpressionSymbolVo»»": {"title": "WebResult«List«ExpressionSymbolVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ExpressionSymbolVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«HasInteractionVo»»": {"title": "WebResult«List«HasInteractionVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/HasInteractionVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«OrderProductStatVo»»": {"title": "WebResult«List«OrderProductStatVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/OrderProductStatVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«OrderStatusStatVo»»": {"title": "WebResult«List«OrderStatusStatVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/OrderStatusStatVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«PlanGroupChain»»": {"title": "WebResult«List«PlanGroupChain»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/PlanGroupChain"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«RegionBatchVo»»": {"title": "WebResult«List«RegionBatchVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/RegionBatchVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«SampleRequestShopVo»»": {"title": "WebResult«List«SampleRequestShopVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/SampleRequestShopVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«ShopBriefVo»»": {"title": "WebResult«List«ShopBriefVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ShopBriefVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«ShopPlatformVo»»": {"title": "WebResult«List«ShopPlatformVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ShopPlatformVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TkshopCategoryDto»»": {"title": "WebResult«List«TkshopCategoryDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopCategoryDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TkshopContactVo»»": {"title": "WebResult«List«TkshopContactVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopContactVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TkshopCreatorContactDetailVo»»": {"title": "WebResult«List«TkshopCreatorContactDetailVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopCreatorContactDetailVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TkshopCreatorFiledVo»»": {"title": "WebResult«List«TkshopCreatorFiledVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopCreatorFiledVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TkshopOrderHasPhoneVo»»": {"title": "WebResult«List«TkshopOrderHasPhoneVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopOrderHasPhoneVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TkshopOrderVo»»": {"title": "WebResult«List«TkshopOrderVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopOrderVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TkshopPlanChainGroupVo»»": {"title": "WebResult«List«TkshopPlanChainGroupVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopPlanChainGroupVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TkshopPlanChainLayoutVo»»": {"title": "WebResult«List«TkshopPlanChainLayoutVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopPlanChainLayoutVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TkshopSampleRequestPolicyVo»»": {"title": "WebResult«List«TkshopSampleRequestPolicyVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopSampleRequestPolicyVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TkshopSearchProfileDto»»": {"title": "WebResult«List«TkshopSearchProfileDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopSearchProfileDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TkshopShopVo»»": {"title": "WebResult«List«TkshopShopVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopShopVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TkshopTaskDrawerDto»»": {"title": "WebResult«List«TkshopTaskDrawerDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopTaskDrawerDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TkshopVideoStatDto»»": {"title": "WebResult«List«TkshopVideoStatDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TkshopVideoStatDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«VideoBriefVo»»": {"title": "WebResult«List«VideoBriefVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/VideoBriefVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«long»»": {"title": "WebResult«List«long»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«string»»": {"title": "WebResult«List«string»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"type": "string"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«Map«long,date-time»»": {"title": "WebResult«Map«long,date-time»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object", "additionalProperties": {"type": "string", "format": "date-time"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«Map«long,string»»": {"title": "WebResult«Map«long,string»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object", "additionalProperties": {"type": "string"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«Map«string,string»»": {"title": "WebResult«Map«string,string»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object", "additionalProperties": {"type": "string"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«MobileAccountMessageConfig»": {"title": "WebResult«MobileAccountMessageConfig»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/MobileAccountMessageConfig"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«SampleRequestShopBriefVo»»": {"title": "WebResult«PageResult«SampleRequestShopBriefVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«SampleRequestShopBriefVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«ShopHealthVo»»": {"title": "WebResult«PageResult«ShopHealthVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«ShopHealthVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«TkshopBuyerDetailVo»»": {"title": "WebResult«PageResult«TkshopBuyerDetailVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«TkshopBuyerDetailVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«TkshopBuyerInteractionDto»»": {"title": "WebResult«PageResult«TkshopBuyerInteractionDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«TkshopBuyerInteractionDto»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«TkshopCreatorDetailVo»»": {"title": "WebResult«PageResult«TkshopCreatorDetailVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«TkshopCreatorDetailVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«TkshopCreatorDto»»": {"title": "WebResult«PageResult«TkshopCreatorDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«TkshopCreatorDto»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«TkshopCreatorLiveVo»»": {"title": "WebResult«PageResult«TkshopCreatorLiveVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«TkshopCreatorLiveVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«TkshopCreatorProductDto»»": {"title": "WebResult«PageResult«TkshopCreatorProductDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«TkshopCreatorProductDto»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«TkshopCreatorProductVo»»": {"title": "WebResult«PageResult«TkshopCreatorProductVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«TkshopCreatorProductVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«TkshopCreatorVideoVo»»": {"title": "WebResult«PageResult«TkshopCreatorVideoVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«TkshopCreatorVideoVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«TkshopGlobalCreatorDetailVo»»": {"title": "WebResult«PageResult«TkshopGlobalCreatorDetailVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«TkshopGlobalCreatorDetailVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«TkshopInteractionDto»»": {"title": "WebResult«PageResult«TkshopInteractionDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«TkshopInteractionDto»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«TkshopLiveVo»»": {"title": "WebResult«PageResult«TkshopLiveVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«TkshopLiveVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«TkshopOrderVo»»": {"title": "WebResult«PageResult«TkshopOrderVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«TkshopOrderVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«TkshopProductDto»»": {"title": "WebResult«PageResult«TkshopProductDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«TkshopProductDto»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«TkshopSampleRequestAuditVo»»": {"title": "WebResult«PageResult«TkshopSampleRequestAuditVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«TkshopSampleRequestAuditVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«TkshopSampleRequestDetailVo»»": {"title": "WebResult«PageResult«TkshopSampleRequestDetailVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«TkshopSampleRequestDetailVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«TkshopTaskDrawerVo»»": {"title": "WebResult«PageResult«TkshopTaskDrawerVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«TkshopTaskDrawerVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«TkshopVideoVo»»": {"title": "WebResult«PageResult«TkshopVideoVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«TkshopVideoVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«ShopCreatorStatVo»": {"title": "WebResult«ShopCreatorStatVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ShopCreatorStatVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«ShopHealthVo»": {"title": "WebResult«ShopHealthVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ShopHealthVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«ShopMediaStatVo»": {"title": "WebResult«ShopMediaStatVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ShopMediaStatVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«ShopSrStatVo»": {"title": "WebResult«ShopSrStatVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ShopSrStatVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TKVideo»": {"title": "WebResult«TKVideo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TKVideo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TSSyncCreatorPolicy»": {"title": "WebResult«TSSyncCreatorPolicy»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TSSyncCreatorPolicy"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TeamDto»": {"title": "WebResult«TeamDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TeamDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TeamTkshopConfig»": {"title": "WebResult«TeamTkshopConfig»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TeamTkshopConfig"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkshopBuyerDetailVo»": {"title": "WebResult«TkshopBuyerDetailVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkshopBuyerDetailVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkshopBuyerDto»": {"title": "WebResult«TkshopBuyerDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkshopBuyerDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkshopBuyerInteractionDto»": {"title": "WebResult«TkshopBuyerInteractionDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkshopBuyerInteractionDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkshopConfig»": {"title": "WebResult«TkshopConfig»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkshopConfig"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkshopCreatorAchievementResult»": {"title": "WebResult«TkshopCreatorAchievementResult»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkshopCreatorAchievementResult"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkshopCreatorAchievementStat»": {"title": "WebResult«TkshopCreatorAchievementStat»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkshopCreatorAchievementStat"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkshopCreatorDetailVo»": {"title": "WebResult«TkshopCreatorDetailVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkshopCreatorDetailVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkshopCreatorExpiredAutoTags»": {"title": "WebResult«TkshopCreatorExpiredAutoTags»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkshopCreatorExpiredAutoTags"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkshopCreatorShopDto»": {"title": "WebResult«TkshopCreatorShopDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkshopCreatorShopDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkshopInteractionDto»": {"title": "WebResult«TkshopInteractionDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkshopInteractionDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkshopLiveVo»": {"title": "WebResult«TkshopLiveVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkshopLiveVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkshopOrderCountVo»": {"title": "WebResult«TkshopOrderCountVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkshopOrderCountVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkshopOrderStatVo»": {"title": "WebResult«TkshopOrderStatVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkshopOrderStatVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkshopPlanChainLayoutVo»": {"title": "WebResult«TkshopPlanChainLayoutVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkshopPlanChainLayoutVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkshopProductDto»": {"title": "WebResult«TkshopProductDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkshopProductDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkshopSampleRequestPolicyVo»": {"title": "WebResult«TkshopSampleRequestPolicyVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkshopSampleRequestPolicyVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkshopSearchProfileDto»": {"title": "WebResult«TkshopSearchProfileDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkshopSearchProfileDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkshopShopBuyerResult»": {"title": "WebResult«TkshopShopBuyerResult»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkshopShopBuyerResult"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkshopSystemStatusVo»": {"title": "WebResult«TkshopSystemStatusVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkshopSystemStatusVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkshopTrialCodeDto»": {"title": "WebResult«TkshopTrialCodeDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkshopTrialCodeDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkshopVideoVo»": {"title": "WebResult«TkshopVideoVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkshopVideoVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«int»": {"title": "WebResult«int»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«long»": {"title": "WebResult«long»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "integer", "format": "int64"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«string»": {"title": "WebResult«string»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "string"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "创建一个tkshop定时计划": {"title": "创建一个tkshop定时计划", "type": "object", "properties": {"bizScene": {"type": "string", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}, "creatorFilter": {"type": "string", "description": "达人筛选条件，json格式"}, "cronExpression": {"type": "string", "description": "重复日期，只包含星期信息的表达式(具体参考自动流程计划)"}, "enabled": {"type": "boolean"}, "expressions": {"type": "array", "description": "如果是随机执行，用-隔开开始时间和结束时间[start, start-end, start, ...]", "items": {"type": "string"}}, "jobType": {"type": "string", "enum": ["Acco<PERSON><PERSON><PERSON><PERSON>", "AccountHealthCheck", "AccountMaintenance", "AccountMessageCheck", "KOL_LiveRewards", "<PERSON><PERSON><PERSON><PERSON>", "SendInvite", "SendPm", "SendUserCard", "ShareVideo", "<PERSON><PERSON><PERSON>", "SyncContactToPhone", "SyncContacts", "SyncCreator", "SyncLiveStream", "SyncPm", "TS_AddContacts", "TS_AddFriends", "TS_DemandPayment", "TS_IMChatByFilter", "TS_IMChatByHandle", "TS_LoginCheck", "TS_SampleApprove", "TS_SendBuyerIMChat", "TS_SendEmail", "TS_SendFacebook", "TS_SendLine", "TS_SendWhatsApp", "TS_SendZalo", "TS_SyncBuyerInfo", "TS_SyncCreator", "TS_SyncOrders", "TS_SyncProducts", "TS_SyncSampleCreator", "TS_SyncShopInfo", "TS_SyncVideos", "TS_TargetPlanByFilter", "TS_TargetPlanByHandle", "TS_TargetPlanClear", "TS_VideoADCode"]}, "params": {"type": "string"}, "planGroupId": {"type": "integer", "description": "所属的分组", "format": "int64"}}}, "批量执行抽屉里选中的任务": {"title": "批量执行抽屉里选中的任务", "type": "object", "properties": {"deviceId": {"type": "string"}, "drawerJobIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}, "x-device-token": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-device-token", "in": "header"}, "x-dm-team-id": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-dm-team-id", "in": "header"}}}}