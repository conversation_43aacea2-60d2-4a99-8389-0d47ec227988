{"openapi": "3.0.3", "info": {"title": "Extension API", "version": "App version 1.0,Spring Boot Version:2.7.14"}, "servers": [{"url": "http://dev.thinkoncloud.cn", "description": "Inferred Url"}], "tags": [{"name": "插件分身 extension shop API", "description": "Extension Shop Controller"}], "paths": {"/api/extension/addShops": {"post": {"tags": ["ExtensionShopController"], "summary": "插件新增或绑定店铺", "operationId": "extensionAddShopsPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddExtensionShopsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/extension/crx/latestVersion": {"get": {"tags": ["ExtensionShopController"], "summary": "latestVersion", "operationId": "extensionCrxLatestVersionGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ExtensionShopCrxVersionVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "put": {"tags": ["ExtensionShopController"], "summary": "latestVersion", "operationId": "extensionCrxLatestVersionPut", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ExtensionShopCrxVersionVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "post": {"tags": ["ExtensionShopController"], "summary": "latestVersion", "operationId": "extensionCrxLatestVersionPost", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ExtensionShopCrxVersionVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "delete": {"tags": ["ExtensionShopController"], "summary": "latestVersion", "operationId": "extensionCrxLatestVersionDelete", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ExtensionShopCrxVersionVo»"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "options": {"tags": ["ExtensionShopController"], "summary": "latestVersion", "operationId": "extensionCrxLatestVersionOptions", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ExtensionShopCrxVersionVo»"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "head": {"tags": ["ExtensionShopController"], "summary": "latestVersion", "operationId": "extensionCrxLatestVersionHead", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ExtensionShopCrxVersionVo»"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "patch": {"tags": ["ExtensionShopController"], "summary": "latestVersion", "operationId": "extensionCrxLatestVersionPatch", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ExtensionShopCrxVersionVo»"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "trace": {"tags": ["ExtensionShopController"], "summary": "latestVersion", "operationId": "extensionCrxLatestVersionTrace", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ExtensionShopCrxVersionVo»"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/extension/declareDevice": {"post": {"tags": ["ExtensionShopController"], "summary": "插件声明一个设备", "operationId": "extensionDeclareDevicePost", "parameters": [{"name": "appId", "in": "query", "description": "appId", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "version", "in": "query", "description": "version", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "hostName", "in": "query", "description": "hostName", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/extension/shop/byCoordinateId/{coordinateId}": {"get": {"tags": ["ExtensionShopController"], "summary": "根据主账号ID(coordinateId)查询所有店铺（包括未授权）", "operationId": "extensionShopByCoordinateIdByCoordinateIdGet", "parameters": [{"name": "coordinateId", "in": "path", "description": "coordinateId", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«ExtensionShopVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/extension/shop/byShopNo/{shopNo}": {"get": {"tags": ["ExtensionShopController"], "summary": "根据shopNo查询店铺信息(包括未授权的）", "operationId": "extensionShopByShopNoByShopNoGet", "parameters": [{"name": "shopNo", "in": "path", "description": "shopNo", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ExtensionShopVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/extension/shop/checkNoExtension/{shopNo}": {"get": {"tags": ["ExtensionShopController"], "summary": "根据shopNo查询非插件店铺导入情况", "operationId": "extensionShopCheckNoExtensionByShopNoGet", "parameters": [{"name": "shopNo", "in": "path", "description": "shopNo", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«boolean»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}}, "components": {"schemas": {"AddExtensionShopVo": {"title": "AddExtensionShopVo", "type": "object", "properties": {"region": {"type": "string", "description": "区域代码，通常是国家代码，大写"}, "shopName": {"type": "string", "description": "店铺名称"}, "shopNo": {"type": "string", "description": "当前店铺的ID，团队内唯一"}}}, "AddExtensionShopsRequest": {"title": "AddExtensionShopsRequest", "type": "object", "properties": {"coordinateId": {"type": "string", "description": "主账号ID，多个分身可以关联一个主账号"}, "platformType": {"type": "string", "description": "平台类型，比如TikTok,Coupang等"}, "shopType": {"type": "string", "description": "跨境店或本地店（如果支持）", "enum": ["Global", "Local", "None"]}, "shops": {"type": "array", "description": "多个区域的店铺（如果不分区域，则shops长度为1）", "items": {"$ref": "#/components/schemas/AddExtensionShopVo"}}}}, "ExtensionShopCrxVersionVo": {"title": "ExtensionShopCrxVersionVo", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "description": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "numberVersion": {"type": "integer", "description": "数字版本", "format": "int64"}, "version": {"type": "string", "description": "必须是 /\\d+\\.\\d+\\.\\d+/ 的格式"}}}, "ExtensionShopVo": {"title": "ExtensionShopVo", "type": "object", "properties": {"coordinateId": {"type": "string"}, "extension": {"type": "string", "enum": ["both", "extension", "huayang"]}, "granted": {"type": "boolean", "description": "是否已经授权", "example": false}, "id": {"type": "integer", "format": "int64"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "platformType": {"type": "string"}, "region": {"type": "string"}, "shopName": {"type": "string"}, "shopNo": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}}}, "WebResult": {"title": "WebResult", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«ExtensionShopCrxVersionVo»": {"title": "WebResult«ExtensionShopCrxVersionVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ExtensionShopCrxVersionVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«ExtensionShopVo»": {"title": "WebResult«ExtensionShopVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ExtensionShopVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«ExtensionShopVo»»": {"title": "WebResult«List«ExtensionShopVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ExtensionShopVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«boolean»": {"title": "WebResult«boolean»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "boolean"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«string»": {"title": "WebResult«string»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "string"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}, "x-device-token": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-device-token", "in": "header"}, "x-dm-team-id": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-dm-team-id", "in": "header"}}}}