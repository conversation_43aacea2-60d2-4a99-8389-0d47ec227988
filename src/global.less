@import 'style/theme';
@import 'style/mixin';

html,
body,
#root {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
#root {
  overflow: auto;
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  .ant-layout-content {
    display: flex;
    flex-direction: column;
  }
}
button {
  text-transform: capitalize;
}
.ant-pro-global-header {
  padding-left: 0 !important;
  color: white;
  background: white !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); // 添加底部阴影

  button {
    //首字符大字
    padding-right: 0;
    padding-left: 0;
    border: none !important;
    &:active,
    &:focus,
    &:hover {
      color: white !important;
    }
    &:hover {
      background: transparent;
    }
    &.ant-btn-loading {
      &::before {
        display: none;
      }
    }
    .ant-space {
      gap: 4px !important;
    }
  }
  .ant-pro-global-header-logo {
    position: relative;
    width: 200px;
    padding-left: 6px;
    &::after {
      position: absolute;
      top: 14px;
      right: 0;
      bottom: 14px;
      width: 1px;
      background: #5d99cf;
      content: '';
      pointer-events: none;
    }
    & * {
      color: inherit;
    }
    h1 {
      margin-left: 10px;
      font-weight: normal;
      font-size: 20px;
    }

    a img {
      height: 42px;
    }
  }
}
.ant-design-pro > .ant-layout > .ant-layout-sider {
  position: relative;
  border-right: 1px solid @border-color;
  .ant-layout-sider-trigger {
    position: absolute !important;
    width: 100% !important;
    overflow: hidden;
    text-align: left;
  }
}

canvas {
  display: block;
}

body {
  font-family: Microsoft YaHei, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
    'Segoe UI Symbol', 'Noto Color Emoji' !important;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  scrollbar-width: thin;
}

ul,
ol {
  list-style: none;
}

*::-webkit-scrollbar-track-piece {
  background: none;
}
*::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background: #fff;
}
*::-webkit-scrollbar-button {
  display: none;
}
*::-webkit-scrollbar-thumb {
  background: #e1e1e1;
  border: 1px solid @border-color-white;
  border-radius: 5px;
}
*::-webkit-scrollbar-track {
  border: 1px solid @border-color-white;
}

@media (max-width: @screen-xs) {
  .ant-table {
    width: 100%;
    overflow-x: auto;

    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;

        > span {
          display: block;
        }
      }
    }
  }
}

// 兼容IE11
@media screen and(-ms-high-contrast: active), (-ms-high-contrast: none) {
  body .ant-design-pro > .ant-layout {
    min-height: 100vh;
  }
}

.dm-colours-icon {
  width: 1em;
  height: 1em;
  overflow: hidden;
  vertical-align: -0.15em;
  fill: currentColor;
}
.dm-colours-icon__disabled {
  opacity: 0.5;
  filter: grayscale(1);
}

.dm-iconFontIcon {
  display: inline-block;
  width: 1em;
  height: 1em;
  line-height: 1em;
}

.modal-footer {
  display: flex;
  flex: 1;
  justify-content: space-between;
  color: #878787;

  .modal-footer-tips {
    font-size: 12px;
  }
}

.text-ellipsis {
  .text-ellipsis();
}

.table-row-ellipsis {
  .text-ellipsis();

  div {
    display: inline;
  }
}
.ant-table-thead {
  .ant-table-cell {
    * {
      // 首字符大写
      text-transform: capitalize;
    }
    &::before {
      display: none;
    }
  }
}
.ant-table-tbody > tr.ant-table-row-selected > td,
.ant-pro-list .ant-pro-list-row.ant-pro-list-row-selected {
  background: white !important;
}

.ant-pro-field-select-light-select {
  border: 1px solid @border-color-base;
  border-radius: @border-radius-base;
  > .ant-pro-core-field-label {
    width: 120px;
    padding: 0 10px;
  }
}

// 按钮全局样式
.ant-btn > .dm-iconFontIcon + span,
.ant-btn > .dm-colours-icon + span,
.ant-btn > span + .dm-iconFontIcon {
  margin-left: 4px;
}

/** 不显示Tooltip的箭头  **/
.ant-popover-arrow {
  display: none;
}
.dm-show-popover-arrow .ant-popover-arrow {
  display: block;
}
.ant-popover-placement-bottom,
.ant-popover-placement-bottomLeft,
.ant-popover-placement-bottomRight {
  padding-top: 5px;
}

.ant-popover-inner {
  background: #fff;
  border-radius: 5px;
  box-shadow: 0 12px 48px 16px rgba(0, 0, 0, 0.03), 0 9px 28px 0 rgba(0, 0, 0, 0.05),
    0 6px 16px -8px rgba(0, 0, 0, 0.08);
}
.ant-tooltip {
  .ant-tooltip-arrow-content,
  .ant-tooltip-inner {
    font-size: 12px;
    background-color: @primary-color;
    --antd-arrow-background-color: inherit;
    &::selection,
    ::selection {
      color: @primary-color;
      background: white;
    }
    .link-underlined {
      color: inherit;
      text-decoration: underline;
    }
    a {
      color: white;
      text-decoration: underline;
    }
  }
}
//调整全局提示条的样式 JIRA DM-57
.ant-message-notice-content {
  min-width: 100px;
  max-width: 420px;
  overflow: hidden;
  .ant-message-custom-content {
    @height: 20px;

    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    overflow: hidden;
    line-height: @height;
    .anticon {
      display: flex;
      align-items: center;
      align-self: start;
      height: 20px;
    }
    > span:not(.anticon) {
      flex: 1;
      overflow: hidden;
      white-space: pre-wrap;
      text-align: left;
      word-break: break-all;
    }
  }
}
// 大麦风格的 radio button group
.dm-radio-group.ant-radio-group {
  display: inline-flex;
  gap: 8px;
  > .ant-radio-button-wrapper {
    border-width: 1px;
    border-radius: 3px;
    &::before {
      display: none;
    }
    &:hover {
      border-color: @primary-color;
    }
  }
}

// 大麦风格的 tab 页签(tab也出现在左侧的场景）
.ant-tabs.ant-tabs-left {
  > .ant-tabs-nav > .ant-tabs-nav-wrap {
    background: #f7f7f7;
    > .ant-tabs-nav-list {
      &::before {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        border-right: 1px solid @border-color-split;
        content: '';
      }
      > .ant-tabs-tab-active {
        background: #fff;
      }
    }
  }
  &.disabled {
    > .ant-tabs-nav > .ant-tabs-nav-wrap {
      > .ant-tabs-nav-list > :not(.ant-tabs-tab-active) {
        cursor: default;
        > .ant-tabs-tab-btn {
          color: #ccc !important;
        }
      }
    }
  }
}

// 将所有的分隔线颜色全部改成 #ddd DAMAI-629
.ant-tabs-top > .ant-tabs-nav::before {
  border-bottom-color: @border-color;
}
.ant-modal-header {
  text-transform: capitalize;
  border-bottom-color: @border-color;
}
.ant-collapse-header {
  text-transform: capitalize;
}
.ant-modal-body {
  padding-top: 18px;
}

// 对话框样式覆盖
.ant-modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 24px;
  padding-left: 24px;
  border-top-color: @border-color;
  .status-bar {
    flex: 1;
    margin-right: 8px;
    font-size: 12px;
    text-align: left;
  }
  .status-tip {
    height: 100%;
    margin: 0 !important;
  }
  > .ant-row {
    width: 100%;
  }
}

// 数字输入框默认显示上下箭头
.ant-input-number-handler-wrap {
  opacity: 1 !important;
}

// 下拉列表样式覆盖
.ant-select-item {
  color: @primary-color;
}
.ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  color: @primary-color;
  font-weight: normal;
}
// 描述的冒号改成全角,样式与DmFormItem 对齐
.ant-descriptions-item-label:not(.ant-descriptions-item-no-colon)::after {
  margin-left: auto !important;
  content: '：' !important;
}

.chart-tooltip {
  padding-bottom: 16px;
  .title {
    font-weight: bold;
    line-height: 35px;
  }
  .content {
    line-height: 22px;
    > .item {
      position: relative;
      padding-left: 10px;
      &::before {
        position: absolute;
        top: 50%;
        left: 0;
        padding: 3px;
        overflow: hidden;
        background: @primary-color;
        border-radius: 50%;
        transform: translate(-50%, -50%);
        content: '';
      }
    }
  }
  .footer {
    line-height: 22px;
    text-align: right;
  }
}
// 定制表格头部底色
.ant-table-thead > tr > th {
  background-color: #f6f6f6;
}
* .ant-form-item {
  margin-bottom: 18px;
}

.ant-form-item-explain-error {
  font-size: 12px;
  line-height: 1.5;
}
.ant-form-item-explain {
  font-size: 12px;
  line-height: 1.5;
}
// 隐藏必填标识的缩进
.ant-form-hide-required-mark .ant-form-item-label label.ant-form-item-required {
  padding-left: 0;
}

// 修改默认边框颜色 DAMAI-629
.ant-pro-card-border,
.ant-card-bordered {
  border-color: @border-color !important;
}
// 对齐图标在首行
.ant-alert {
  > .anticon {
    align-self: flex-start;
    margin-top: 4px;
  }
}
// 托拽排序的table helper
.row-dragging {
  z-index: 99999999;
  background: #fafafa;
  box-shadow: 0 0 0 5px rgba(0.2);
  .option-cell {
    display: none !important;
  }
}

.row-dragging td {
  padding: 8px;
}

.row-dragging .drag-visible {
  visibility: visible;
}

.ant-pro-global-header-collapsed-button {
  display: none !important;
}

.ant-btn-group {
  > .ant-btn {
    margin: 0 !important;
    &:not(:last-child) {
      border-right-color: transparent !important;
    }
  }
}

.rc-overflow {
  position: relative;
  display: flex;
  flex: 1;
  flex-wrap: nowrap;
  gap: 8px;
  align-content: center;
  align-items: center;
  max-width: 100%;
  height: 100%;
  overflow: hidden;
}
.rc-overflow-item {
  overflow: hidden;
}
.creator-chart-tooltip {
  padding: 10px;
  overflow: hidden;
  > div {
    overflow: hidden;
    > div {
      //row-value
      overflow: hidden;
    }
  }
}
.react-draggable {
  overflow: hidden;
}
.ant-btn-two-chinese-chars > *:not(.anticon) {
  margin-right: 0 !important;
  letter-spacing: 0 !important;
}
.ant-btn-two-chinese-chars::first-letter {
  letter-spacing: 0 !important;
}
.ant-select-selection-item {
  .hide-in-selector {
    display: none;
  }
}
