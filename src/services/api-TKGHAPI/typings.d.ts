declare namespace API {
  type AccountFollowRequest = {
    /** 邀约高级设置 */
    advanceSettings?: Record<string, any>;
    /** 是什么样类型的达人，为空表示 LiveCreator  */
    bizScene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
    /** 拟发送的达人列表 */
    creatorIds?: number[];
    /** 公会平台类型 */
    ghPlatformType?:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
    /** 拟使用的TK账号类型，默认值 Normal */
    ghScheduleType?: 'GhBackstage' | 'Mobile' | 'Normal' | 'Official' | 'Union' | 'Unknown';
    /** 发送邀约手机账号选择策略，仅在 ghScheduleType == Mobile 的时候有意义 */
    mobileChoicePolicy?: MobileChoicePolicy;
    /** 邀约要使用的话术列表，会随机从中选择发送 */
    wordsIds?: number[];
  };

  type AccountHealthCheckRequest = {
    /** 其它的流程关心但引擎不关心的属性 */
    advanceSettings?: Record<string, any>;
    /** first | random | assign  为空表示random */
    friendType?: string;
    /** 公会平台类型 */
    ghPlatformType?:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
    /** 手机账号id列表 */
    mobileAccountIds?: number[];
  };

  type AccountMaintenanceRequest = {
    /** 流程相关的参数全部放到高级设置里 */
    advanceSettings?: Record<string, any>;
    deadTime?: string;
    /** 公会平台类型 */
    ghPlatformType?:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
    /** 有哪些手机账号要养号 */
    mobileAccountIds?: number[];
    triggerTeamScheduleImmediately?: boolean;
    /** 养号评论时要使用的话术列表，会随机从中选择发送 */
    wordsIds?: number[];
  };

  type AddGhInteractionRequest = {
    interactions?: GhInteractionVo[];
  };

  type AddGhMessageRequest = {
    /** 浏览器的分身ID和手机的mobileAccount.id */
    accountId?: number;
    handle?: string;
    messages?: GhMessageVo[];
    /** 达人的类型：主播、视频达人、普通用户 */
    scene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
    /** 私信流程的scheduleType，也是账号类型 */
    scheduleType?: 'GhBackstage' | 'Mobile' | 'Normal' | 'Official' | 'Union' | 'Unknown';
    /** 私信是否成功 */
    status?: 'Fail' | 'Pending' | 'Success';
  };

  type AllocateTkCreatorByQueryRequest = {
    cleanFirst?: boolean;
    query?: Record<string, any>;
    responsibleUserId?: number;
    responsibleUserIds?: number[];
    scene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
    tag?: string;
    teamId?: number;
    userId?: number;
  };

  type AllowTimeConfig = {
    FRI?: string;
    /** 以逗号分隔开的小时，允许在哪几个小时执行就列出哪些数字如：0,1,2,5,23 */
    MON?: string;
    SAT?: string;
    SUN?: string;
    THU?: string;
    TUE?: string;
    WED?: string;
  };

  type CancelJobsFilter = {
    createFrom?: string;
    createTo?: string;
    creator?: string;
    ghCreatorId?: number;
    ghScheduleType?: 'GhBackstage' | 'Mobile' | 'Normal' | 'Official' | 'Union' | 'Unknown';
    jobType?:
      | 'AccountFollow'
      | 'AccountHealthCheck'
      | 'AccountMaintenance'
      | 'AccountMessageCheck'
      | 'KOL_LiveRewards'
      | 'ParentJob'
      | 'SendInvite'
      | 'SendPm'
      | 'SendUserCard'
      | 'ShareVideo'
      | 'SubJob'
      | 'SyncContactToPhone'
      | 'SyncContacts'
      | 'SyncCreator'
      | 'SyncLiveStream'
      | 'SyncPm'
      | 'TS_AddContacts'
      | 'TS_AddFriends'
      | 'TS_DemandPayment'
      | 'TS_IMChatByFilter'
      | 'TS_IMChatByHandle'
      | 'TS_LoginCheck'
      | 'TS_SampleApprove'
      | 'TS_SendBuyerIMChat'
      | 'TS_SendEmail'
      | 'TS_SendFacebook'
      | 'TS_SendLine'
      | 'TS_SendWhatsApp'
      | 'TS_SendZalo'
      | 'TS_SyncBuyerInfo'
      | 'TS_SyncCreator'
      | 'TS_SyncOrders'
      | 'TS_SyncProducts'
      | 'TS_SyncSampleCreator'
      | 'TS_SyncShopInfo'
      | 'TS_SyncVideos'
      | 'TS_TargetPlanByFilter'
      | 'TS_TargetPlanByHandle'
      | 'TS_TargetPlanClear'
      | 'TS_VideoADCode';
    mobileId?: number;
    platformType?:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
    shopId?: number;
    status?: 'Cancelled' | 'Failed' | 'Pending' | 'Running' | 'Scheduled' | 'Succeed';
    userEvent?: boolean;
  };

  type CheckSpeechAndGroupRequest = {
    groupId?: number;
    speechIds?: number[];
  };

  type CloudMobileInfo = {
    /** 是否自动续费 */
    autoRenew?: boolean;
    cloudMobileInsId?: number;
    id?: number;
    networkEnabled?: boolean;
    padCode?: string;
    /** 计价周期单位 */
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    /** 手机代理 */
    proxy?: string;
    /** 续费价格 */
    renewPrice?: number;
    /** 过期时间 */
    validEndDate?: string;
  };

  type CommonCreatorIdsRequest = {
    creatorIds?: string[];
  };

  type CommonIdsRequest = {
    ids?: number[];
  };

  type CreateGhJobPlanRequest = {
    /** 场景 */
    bizScene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
    /** 邀约时评论要使用的话术列表，会随机从中选择发送。只在 ghJobType 为 SendInvite 时有意义 */
    commentWordIds?: number[];
    /** 查询条件，由 /api/gh/creator/page 这个接口的params JSON.stringify(params) 而来 */
    creatorFilter?: string;
    /** 重复日期，只包含星期信息的表达式(具体参考自动流程计划) */
    cronExpression?: string;
    deleteMsg?: boolean;
    /** 同步时是否删除与主播的聊天记录，默认为true。只在 ghJobType 为 SyncPm 时有意义 */
    deleteSession?: boolean;
    /** 如果是随机执行，用-隔开开始时间和结束时间[start, start-end, start, ...] */
    expressions?: string[];
    ghJobType?:
      | 'AccountFollow'
      | 'AccountHealthCheck'
      | 'AccountMaintenance'
      | 'AccountMessageCheck'
      | 'KOL_LiveRewards'
      | 'ParentJob'
      | 'SendInvite'
      | 'SendPm'
      | 'SendUserCard'
      | 'ShareVideo'
      | 'SubJob'
      | 'SyncContactToPhone'
      | 'SyncContacts'
      | 'SyncCreator'
      | 'SyncLiveStream'
      | 'SyncPm'
      | 'TS_AddContacts'
      | 'TS_AddFriends'
      | 'TS_DemandPayment'
      | 'TS_IMChatByFilter'
      | 'TS_IMChatByHandle'
      | 'TS_LoginCheck'
      | 'TS_SampleApprove'
      | 'TS_SendBuyerIMChat'
      | 'TS_SendEmail'
      | 'TS_SendFacebook'
      | 'TS_SendLine'
      | 'TS_SendWhatsApp'
      | 'TS_SendZalo'
      | 'TS_SyncBuyerInfo'
      | 'TS_SyncCreator'
      | 'TS_SyncOrders'
      | 'TS_SyncProducts'
      | 'TS_SyncSampleCreator'
      | 'TS_SyncShopInfo'
      | 'TS_SyncVideos'
      | 'TS_TargetPlanByFilter'
      | 'TS_TargetPlanByHandle'
      | 'TS_TargetPlanClear'
      | 'TS_VideoADCode';
    /** 公会平台类型，tk、抖音、小红书 */
    ghPlatformType?:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
    /** 拟使用的TK账号类型，默认值 Normal。只在 ghJobType 为 SendInvite 时有意义 */
    ghScheduleType?: 'GhBackstage' | 'Mobile' | 'Normal' | 'Official' | 'Union' | 'Unknown';
    /** 私信高级设置，只在 ghJobType 为 SendInvite/AccountMaintenance 时有意义 */
    inviteAdvanceSettings?: Record<string, any>;
    /** 发送邀约手机账号选择策略，仅在 ghScheduleType == Mobile 的时候有意义 */
    inviteMobileChoicePolicy?: MobileChoicePolicy;
    /** 发送邀约分身选择策略，仅在 ghScheduleType != Mobile 的时候有意义 */
    inviteShopChoicePolicy?: ShopChoicePolicy;
    /** 最多允许执行多少分钟(需求方要求是流程自己实现，所以应该只有部分流程支持这个参数) */
    maxMinutes?: number;
    /** 相应的手机账号id，只有在 ghJobType 为 AccountMaintenance 时有意义 */
    mobileAccountIds?: number[];
    name?: string;
    planType?: 'Team' | 'User';
    /** 发送私信前关注主播并将私信内容评论到主播的第一条视频 */
    sendComment?: boolean;
    /** 是否增加随机表情 */
    sendEmoji?: boolean;
    /** 是否发送邀约卡片，只有发送邀约建联计划才有意义(只有公会后台账号才支持此特性） */
    sendInviteCard?: boolean;
    /** 已分配/已认领的达人不再发送 (为空表示 true )，只对发送邀约建联的计划有意义 */
    skipResponsible?: boolean;
    /** 邀约要使用的话术列表，会随机从中选择发送。只在 ghJobType 为 SendInvite 时有意义 */
    wordsIds?: number[];
  };

  type CreateSpeechGroupRequest = {
    bizScene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
    name?: string;
    speechType?:
      | 'BuyerImChat'
      | 'BuyerSendMsg'
      | 'Comment'
      | 'ImChat'
      | 'Invite'
      | 'LiveComment'
      | 'RequestAdCode'
      | 'SendMsg'
      | 'TargetPlan';
  };

  type CreateSpeechRequest = {
    groupId?: number;
    name?: string;
    textList?: string[];
  };

  type CreatorBriefVo = {
    creatorId?: string;
    handle?: string;
    region?: string;
  };

  type DepartmentDto = {
    createTime?: string;
    hidden?: boolean;
    id?: number;
    invitingAuditEnabled?: boolean;
    invitingEnabled?: boolean;
    name?: string;
    parentId?: number;
    sortNumber?: number;
    teamId?: number;
  };

  type ExportTkCreatorByQueryRequest = {
    props?: string[];
    query?: Record<string, any>;
    scene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
    teamId?: number;
    userId?: number;
  };

  type ExportTkCreatorRequest = {
    ids?: number[];
    props?: string[];
    scene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
    teamId?: number;
  };

  type FavoriteTkCreatorByQueryRequest = {
    /** 关注或取消关注 */
    favorite?: boolean;
    query?: Record<string, any>;
    scene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
    teamId?: number;
    userId?: number;
  };

  type FilterExistsRequest = {
    creators?: KolHandleRegionVo[];
  };

  type FilterStatInfo = {
    /** 当天是否应继续筛选 */
    continueFilter?: boolean;
    /** 限制团队每日筛选达人数量（0表示不限制） */
    filterTotal?: number;
    /** 当天已经筛选的达人数量 */
    filteredCount?: number;
  };

  type FindGhCreatorRequest = {
    alias?: string;
    anchor?: boolean;
    avgLikesFrom?: number;
    avgLikesTo?: number;
    avgViewersFrom?: number;
    avgViewersTo?: number;
    /** 包含指定标签。取false时，tagLc只能是OR */
    containsTag?: boolean;
    createTimeLastDays?: number;
    deleting?: boolean;
    elite?: boolean;
    filterByFavorite?: boolean;
    followerCntFrom?: number;
    followerCntTo?: number;
    general?: boolean;
    handle?: string;
    hasNewMsg?: boolean;
    hasNoRegion?: boolean;
    hasResponsibleUser?: boolean;
    importTimeFrom?: string;
    importTimeLastDays?: number;
    importTimeTo?: string;
    interactTypes?: ('ImportCreator' | 'Invite' | 'SendMsg' | 'SyncMsg' | 'UpdateInfo')[];
    lastLiveTimeFrom?: string;
    lastLiveTimeLastDays?: number;
    lastLiveTimeTo?: string;
    lastSyncTimeFrom?: string;
    lastSyncTimeLastDays?: number;
    lastSyncTimeTo?: string;
    limit?: number;
    maxGameRankFrom?: number;
    maxGameRankTo?: number;
    maxHourRevenueFrom?: number;
    maxHourRevenueTo?: number;
    maxLikesFrom?: number;
    maxLikesTo?: number;
    maxPopularRankFrom?: number;
    maxPopularRankTo?: number;
    maxRevenueFrom?: number;
    maxRevenueTo?: number;
    maxViewersFrom?: number;
    maxViewersTo?: number;
    maxWeekRevenueFrom?: number;
    maxWeekRevenueTo?: number;
    messageStatus?: 'Everyone' | 'Friends' | 'None' | 'Recommands' | 'Unset';
    messageStatusList?: ('Everyone' | 'Friends' | 'None' | 'Recommands' | 'Unset')[];
    name?: string;
    platformType?:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
    region?: string;
    regions?: string[];
    responsibleUserId?: number;
    revenueLc?: 'AND' | 'NOT' | 'OR';
    showcase?: boolean;
    statusList?: ('NotContacted' | 'Replied' | 'Sent' | 'SignUnsupported' | 'Signed')[];
    tagIds?: number[];
    /** 标签的逻辑条件，支持OR|AND */
    tagLc?: 'AND' | 'NOT' | 'OR';
  };

  type FindGhGifterRequest = {
    alias?: string;
    /** 包含指定标签。取false时，tagLc只能是OR */
    containsTag?: boolean;
    createTimeFrom?: string;
    createTimeLastDays?: number;
    createTimeTo?: string;
    creatorHandle?: string;
    deleting?: boolean;
    filterByFavorite?: boolean;
    followerCntFrom?: number;
    followerCntTo?: number;
    giftMaxFrom?: number;
    giftMaxTo?: number;
    giftTotalFrom?: number;
    giftTotalTo?: number;
    gifterLevelFrom?: number;
    gifterLevelTo?: number;
    handle?: string;
    hasNewMsg?: boolean;
    hasNoRegion?: boolean;
    hasResponsibleUser?: boolean;
    lastSyncTimeFrom?: string;
    lastSyncTimeLastDays?: number;
    lastSyncTimeTo?: string;
    limit?: number;
    messageStatusList?: ('Everyone' | 'Friends' | 'None' | 'Recommands' | 'Unset')[];
    myStatusList?: ('NotContacted' | 'Replied' | 'Sent' | 'SignUnsupported' | 'Signed')[];
    region?: string;
    regions?: string[];
    responsibleUserId?: number;
    statusList?: ('NotContacted' | 'Replied' | 'Sent' | 'SignUnsupported' | 'Signed')[];
    tagIds?: number[];
    /** 标签的逻辑条件，支持OR|AND */
    tagLc?: 'AND' | 'NOT' | 'OR';
    videoCntFrom?: number;
    videoCntTo?: number;
    videoLikesFrom?: number;
    videoLikesTo?: number;
  };

  type FindGhUserRequest = {
    alias?: string;
    /** 包含指定标签。取false时，tagLc只能是OR */
    containsTag?: boolean;
    createTimeFrom?: string;
    createTimeLastDays?: number;
    createTimeTo?: string;
    deleting?: boolean;
    filterByFavorite?: boolean;
    followerCntFrom?: number;
    followerCntTo?: number;
    gifterLevelFrom?: number;
    gifterLevelTo?: number;
    handle?: string;
    hasNewMsg?: boolean;
    hasNoRegion?: boolean;
    hasResponsibleUser?: boolean;
    lastSyncTimeFrom?: string;
    lastSyncTimeLastDays?: number;
    lastSyncTimeTo?: string;
    limit?: number;
    messageStatusList?: ('Everyone' | 'Friends' | 'None' | 'Recommands' | 'Unset')[];
    region?: string;
    regions?: string[];
    responsibleUserId?: number;
    statusList?: ('NotContacted' | 'Replied' | 'Sent' | 'SignUnsupported' | 'Signed')[];
    tagIds?: number[];
    /** 标签的逻辑条件，支持OR|AND */
    tagLc?: 'AND' | 'NOT' | 'OR';
    videoCntFrom?: number;
    videoCntTo?: number;
    videoLikesFrom?: number;
    videoLikesTo?: number;
  };

  type FindGhVideoCreatorRequest = {
    alias?: string;
    /** 包含指定标签。取false时，tagLc只能是OR */
    containsTag?: boolean;
    createTimeFrom?: string;
    createTimeLastDays?: number;
    createTimeTo?: string;
    deleting?: boolean;
    filterByFavorite?: boolean;
    followerCntFrom?: number;
    followerCntTo?: number;
    handle?: string;
    hasNewMsg?: boolean;
    hasNoRegion?: boolean;
    hasResponsibleUser?: boolean;
    lastSyncTimeFrom?: string;
    lastSyncTimeLastDays?: number;
    lastSyncTimeTo?: string;
    limit?: number;
    messageStatusList?: ('Everyone' | 'Friends' | 'None' | 'Recommands' | 'Unset')[];
    region?: string;
    regions?: string[];
    responsibleUserId?: number;
    statusList?: ('NotContacted' | 'Replied' | 'Sent' | 'SignUnsupported' | 'Signed')[];
    tagIds?: number[];
    /** 标签的逻辑条件，支持OR|AND */
    tagLc?: 'AND' | 'NOT' | 'OR';
    videoCntFrom?: number;
    videoCntTo?: number;
    videoLikesFrom?: number;
    videoLikesTo?: number;
  };

  type GhAvailableVo = {
    available?: boolean;
    availableStatus?: 'Available' | 'Unavailable' | 'Unset';
    creatorId?: string;
    handle?: string;
    id?: number;
    region?: string;
  };

  type ghBindMobileAccountToCreatorPutParams = {
    /** creatorId */
    creatorId: number;
    /** mobileAccountId */
    mobileAccountId: number;
    /** ghBizScene */
    ghBizScene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
  };

  type ghBindPmShopToCreatorPutParams = {
    /** creatorId */
    creatorId: number;
    /** shopId */
    shopId?: number;
    /** ghBizScene */
    ghBizScene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
    /** ghScheduleType */
    ghScheduleType?: 'GhBackstage' | 'Mobile' | 'Normal' | 'Official' | 'Union' | 'Unknown';
  };

  type ghByHandleGetParams = {
    /** handle */
    handle: string;
  };

  type ghCreatorAllocateByQueryPutParams = {
    /** responsibleUserId */
    responsibleUserId: number;
  };

  type GhCreatorAllocateRequest = {
    ids?: number[];
    responsibleUserId?: number;
    tag?: string;
  };

  type ghCreatorAvailableListGetParams = {
    /** handles */
    handles?: string;
    /** creatorIds */
    creatorIds?: string;
  };

  type ghCreatorAvatarGetParams = {
    /** creatorId */
    creatorId: string;
    /** region */
    region: string;
  };

  type ghCreatorByCreatorIdGetParams = {
    /** 花漾达人ID */
    creatorId: number;
  };

  type ghCreatorByKeepInfoDeleteParams = {
    /** keepDays */
    keepDays: number;
    /** filterRevenue */
    filterRevenue?: boolean;
  };

  type ghCreatorCountV2GetParams = {
    alias?: string;
    anchor?: boolean;
    avgLikesFrom?: number;
    avgLikesTo?: number;
    avgViewersFrom?: number;
    avgViewersTo?: number;
    /** 包含指定标签。取false时，tagLc只能是OR */
    containsTag?: boolean;
    createTimeLastDays?: number;
    deleting?: boolean;
    elite?: boolean;
    filterByFavorite?: boolean;
    followerCntFrom?: number;
    followerCntTo?: number;
    general?: boolean;
    handle?: string;
    hasNewMsg?: boolean;
    hasNoRegion?: boolean;
    hasResponsibleUser?: boolean;
    importTimeFrom?: string;
    importTimeLastDays?: number;
    importTimeTo?: string;
    interactTypes?: 'ImportCreator' | 'Invite' | 'SendMsg' | 'SyncMsg' | 'UpdateInfo';
    lastLiveTimeFrom?: string;
    lastLiveTimeLastDays?: number;
    lastLiveTimeTo?: string;
    lastSyncTimeFrom?: string;
    lastSyncTimeLastDays?: number;
    lastSyncTimeTo?: string;
    limit?: number;
    maxGameRankFrom?: number;
    maxGameRankTo?: number;
    maxHourRevenueFrom?: number;
    maxHourRevenueTo?: number;
    maxLikesFrom?: number;
    maxLikesTo?: number;
    maxPopularRankFrom?: number;
    maxPopularRankTo?: number;
    maxRevenueFrom?: number;
    maxRevenueTo?: number;
    maxViewersFrom?: number;
    maxViewersTo?: number;
    maxWeekRevenueFrom?: number;
    maxWeekRevenueTo?: number;
    messageStatus?: 'Everyone' | 'Friends' | 'None' | 'Recommands' | 'Unset';
    messageStatusList?: 'Everyone' | 'Friends' | 'None' | 'Recommands' | 'Unset';
    name?: string;
    pageNum?: number;
    pageSize?: number;
    platformType?:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
    region?: string;
    regions?: string[];
    responsibleUserId?: number;
    revenueLc?: 'AND' | 'NOT' | 'OR';
    showcase?: boolean;
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
    statusList?: 'NotContacted' | 'Replied' | 'Sent' | 'SignUnsupported' | 'Signed';
    tagIds?: number[];
    /** 标签的逻辑条件，支持OR|AND */
    tagLc?: 'AND' | 'NOT' | 'OR';
  };

  type ghCreatorDeleteByQueryPostParams = {
    /** force */
    force?: boolean;
    /** deleteRemark */
    deleteRemark?: string;
  };

  type ghCreatorDeletePostParams = {
    /** force */
    force?: boolean;
    /** deleteRemark */
    deleteRemark?: string;
  };

  type GhCreatorDetailVo = {
    alias?: string;
    anchor?: boolean;
    available?: 'Available' | 'Unavailable' | 'Unset';
    avatar?: string;
    avgLikes?: number;
    avgRevenue?: number;
    avgViewers?: number;
    backstageShopId?: number;
    commerceUser?: boolean;
    createTime?: string;
    creatorId?: string;
    deleteRemark?: string;
    elite?: boolean;
    email?: string;
    /** 首次交互 */
    firstInteraction?: GhInteractionDto;
    followerCnt?: number;
    gaming?: boolean;
    general?: boolean;
    handle?: string;
    hasNewMsg?: boolean;
    hyperlink?: string;
    id?: number;
    importTime?: string;
    importType?: 'Collected' | 'Imported';
    lastGameRank?: number;
    lastHourRevenue?: number;
    lastInteractTime?: string;
    lastInteractType?: 'ImportCreator' | 'Invite' | 'SendMsg' | 'SyncMsg' | 'UpdateInfo';
    /** 最后一次交互 */
    lastInteraction?: GhInteractionDto;
    lastLikes?: number;
    lastLiveTime?: string;
    lastPopularRank?: number;
    lastRevenue?: number;
    lastSyncTime?: string;
    lastViewers?: number;
    lastWeekRevenue?: number;
    /** 直播次数 */
    liveCount?: number;
    liveRate?: number;
    liveRewardsMobileAccountId?: number;
    liveRewardsShopId?: number;
    maxGameRank?: number;
    maxHourRevenue?: number;
    maxLikes?: number;
    maxPopularRank?: number;
    maxRevenue?: number;
    maxViewers?: number;
    maxWeekRevenue?: number;
    messageStatus?: 'Everyone' | 'Friends' | 'None' | 'Recommands' | 'Unset';
    mobile?: string;
    mobileAccountId?: number;
    officialShopId?: number;
    platformType?:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
    pmShopId?: number;
    region?: string;
    /** 认领人 */
    responsibleUser?: UserDto;
    responsibleUserId?: number;
    showcase?: boolean;
    status?: 'NotContacted' | 'Replied' | 'Sent' | 'SignUnsupported' | 'Signed';
    /** 标签 */
    tags?: TagDto[];
    teamId?: number;
    ttSeller?: boolean;
    unionShopId?: number;
    videoCnt?: number;
    videoLikes?: number;
    whatsapp?: string;
  };

  type GhCreatorDocument = {
    alias?: string;
    anchor?: boolean;
    avatar?: string;
    categories?: string[];
    commerceUser?: boolean;
    creatorId?: string;
    elite?: boolean;
    email?: string;
    followerCnt?: number;
    gaming?: boolean;
    general?: boolean;
    handle?: string;
    liveDocument?: GhLiveDocument;
    messageStatus?: 'Everyone' | 'Friends' | 'None' | 'Recommands' | 'Unset';
    mobile?: string;
    platformType?:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
    region?: string;
    showcase?: boolean;
    ttSeller?: boolean;
    videoCnt?: number;
    videoLikes?: number;
    whatsapp?: string;
  };

  type GhCreatorDocumentBatch = {
    creators?: GhCreatorDocument[];
  };

  type GhCreatorDto = {
    alias?: string;
    anchor?: boolean;
    available?: 'Available' | 'Unavailable' | 'Unset';
    avatar?: string;
    avgLikes?: number;
    avgRevenue?: number;
    avgViewers?: number;
    backstageShopId?: number;
    commerceUser?: boolean;
    createTime?: string;
    creatorId?: string;
    deleteRemark?: string;
    elite?: boolean;
    email?: string;
    followerCnt?: number;
    gaming?: boolean;
    general?: boolean;
    handle?: string;
    hasNewMsg?: boolean;
    id?: number;
    importTime?: string;
    importType?: 'Collected' | 'Imported';
    lastGameRank?: number;
    lastHourRevenue?: number;
    lastInteractTime?: string;
    lastInteractType?: 'ImportCreator' | 'Invite' | 'SendMsg' | 'SyncMsg' | 'UpdateInfo';
    lastLikes?: number;
    lastLiveTime?: string;
    lastPopularRank?: number;
    lastRevenue?: number;
    lastSyncTime?: string;
    lastViewers?: number;
    lastWeekRevenue?: number;
    liveRate?: number;
    liveRewardsMobileAccountId?: number;
    liveRewardsShopId?: number;
    maxGameRank?: number;
    maxHourRevenue?: number;
    maxLikes?: number;
    maxPopularRank?: number;
    maxRevenue?: number;
    maxViewers?: number;
    maxWeekRevenue?: number;
    messageStatus?: 'Everyone' | 'Friends' | 'None' | 'Recommands' | 'Unset';
    mobile?: string;
    mobileAccountId?: number;
    officialShopId?: number;
    platformType?:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
    pmShopId?: number;
    region?: string;
    responsibleUserId?: number;
    showcase?: boolean;
    status?: 'NotContacted' | 'Replied' | 'Sent' | 'SignUnsupported' | 'Signed';
    teamId?: number;
    ttSeller?: boolean;
    unionShopId?: number;
    videoCnt?: number;
    videoLikes?: number;
    whatsapp?: string;
  };

  type GhCreatorExpiredAutoTags = {
    new_message?: string;
  };

  type GhCreatorImportRequest = {
    handles?: string[];
    /** 所属区域 */
    region?: string;
    tags?: string[];
  };

  type GhCreatorInterface = true;

  type ghCreatorPageByAvailableGetParams = {
    availableStatus?: 'Available' | 'Unavailable' | 'Unset';
    createTimeFrom?: string;
    createTimeTo?: string;
    filterRevenue?: boolean;
    pageNum?: number;
    pageSize?: number;
    platformType?:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
  };

  type ghCreatorPageGetParams = {
    alias?: string;
    anchor?: boolean;
    avgLikesFrom?: number;
    avgLikesTo?: number;
    avgViewersFrom?: number;
    avgViewersTo?: number;
    /** 包含指定标签。取false时，tagLc只能是OR */
    containsTag?: boolean;
    createTimeLastDays?: number;
    deleting?: boolean;
    elite?: boolean;
    filterByFavorite?: boolean;
    followerCntFrom?: number;
    followerCntTo?: number;
    general?: boolean;
    handle?: string;
    hasNewMsg?: boolean;
    hasNoRegion?: boolean;
    hasResponsibleUser?: boolean;
    importTimeFrom?: string;
    importTimeLastDays?: number;
    importTimeTo?: string;
    interactTypes?: 'ImportCreator' | 'Invite' | 'SendMsg' | 'SyncMsg' | 'UpdateInfo';
    lastLiveTimeFrom?: string;
    lastLiveTimeLastDays?: number;
    lastLiveTimeTo?: string;
    lastSyncTimeFrom?: string;
    lastSyncTimeLastDays?: number;
    lastSyncTimeTo?: string;
    limit?: number;
    maxGameRankFrom?: number;
    maxGameRankTo?: number;
    maxHourRevenueFrom?: number;
    maxHourRevenueTo?: number;
    maxLikesFrom?: number;
    maxLikesTo?: number;
    maxPopularRankFrom?: number;
    maxPopularRankTo?: number;
    maxRevenueFrom?: number;
    maxRevenueTo?: number;
    maxViewersFrom?: number;
    maxViewersTo?: number;
    maxWeekRevenueFrom?: number;
    maxWeekRevenueTo?: number;
    messageStatus?: 'Everyone' | 'Friends' | 'None' | 'Recommands' | 'Unset';
    messageStatusList?: 'Everyone' | 'Friends' | 'None' | 'Recommands' | 'Unset';
    name?: string;
    pageNum?: number;
    pageSize?: number;
    platformType?:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
    region?: string;
    regions?: string[];
    responsibleUserId?: number;
    revenueLc?: 'AND' | 'NOT' | 'OR';
    showcase?: boolean;
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
    statusList?: 'NotContacted' | 'Replied' | 'Sent' | 'SignUnsupported' | 'Signed';
    tagIds?: number[];
    /** 标签的逻辑条件，支持OR|AND */
    tagLc?: 'AND' | 'NOT' | 'OR';
  };

  type ghCreatorPageResponsibleGetParams = {
    alias?: string;
    anchor?: boolean;
    avgLikesFrom?: number;
    avgLikesTo?: number;
    avgViewersFrom?: number;
    avgViewersTo?: number;
    /** 包含指定标签。取false时，tagLc只能是OR */
    containsTag?: boolean;
    createTimeLastDays?: number;
    deleting?: boolean;
    elite?: boolean;
    filterByFavorite?: boolean;
    followerCntFrom?: number;
    followerCntTo?: number;
    general?: boolean;
    handle?: string;
    hasNewMsg?: boolean;
    hasNoRegion?: boolean;
    hasResponsibleUser?: boolean;
    importTimeFrom?: string;
    importTimeLastDays?: number;
    importTimeTo?: string;
    interactTypes?: 'ImportCreator' | 'Invite' | 'SendMsg' | 'SyncMsg' | 'UpdateInfo';
    lastLiveTimeFrom?: string;
    lastLiveTimeLastDays?: number;
    lastLiveTimeTo?: string;
    lastSyncTimeFrom?: string;
    lastSyncTimeLastDays?: number;
    lastSyncTimeTo?: string;
    limit?: number;
    maxGameRankFrom?: number;
    maxGameRankTo?: number;
    maxHourRevenueFrom?: number;
    maxHourRevenueTo?: number;
    maxLikesFrom?: number;
    maxLikesTo?: number;
    maxPopularRankFrom?: number;
    maxPopularRankTo?: number;
    maxRevenueFrom?: number;
    maxRevenueTo?: number;
    maxViewersFrom?: number;
    maxViewersTo?: number;
    maxWeekRevenueFrom?: number;
    maxWeekRevenueTo?: number;
    messageStatus?: 'Everyone' | 'Friends' | 'None' | 'Recommands' | 'Unset';
    messageStatusList?: 'Everyone' | 'Friends' | 'None' | 'Recommands' | 'Unset';
    name?: string;
    pageNum?: number;
    pageSize?: number;
    platformType?:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
    region?: string;
    regions?: string[];
    responsibleUserId?: number;
    revenueLc?: 'AND' | 'NOT' | 'OR';
    showcase?: boolean;
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
    statusList?: 'NotContacted' | 'Replied' | 'Sent' | 'SignUnsupported' | 'Signed';
    tagIds?: number[];
    /** 标签的逻辑条件，支持OR|AND */
    tagLc?: 'AND' | 'NOT' | 'OR';
  };

  type ghCreatorPageV2GetParams = {
    alias?: string;
    anchor?: boolean;
    avgLikesFrom?: number;
    avgLikesTo?: number;
    avgViewersFrom?: number;
    avgViewersTo?: number;
    /** 包含指定标签。取false时，tagLc只能是OR */
    containsTag?: boolean;
    createTimeLastDays?: number;
    deleting?: boolean;
    elite?: boolean;
    filterByFavorite?: boolean;
    followerCntFrom?: number;
    followerCntTo?: number;
    general?: boolean;
    handle?: string;
    hasNewMsg?: boolean;
    hasNoRegion?: boolean;
    hasResponsibleUser?: boolean;
    importTimeFrom?: string;
    importTimeLastDays?: number;
    importTimeTo?: string;
    interactTypes?: 'ImportCreator' | 'Invite' | 'SendMsg' | 'SyncMsg' | 'UpdateInfo';
    lastLiveTimeFrom?: string;
    lastLiveTimeLastDays?: number;
    lastLiveTimeTo?: string;
    lastSyncTimeFrom?: string;
    lastSyncTimeLastDays?: number;
    lastSyncTimeTo?: string;
    limit?: number;
    maxGameRankFrom?: number;
    maxGameRankTo?: number;
    maxHourRevenueFrom?: number;
    maxHourRevenueTo?: number;
    maxLikesFrom?: number;
    maxLikesTo?: number;
    maxPopularRankFrom?: number;
    maxPopularRankTo?: number;
    maxRevenueFrom?: number;
    maxRevenueTo?: number;
    maxViewersFrom?: number;
    maxViewersTo?: number;
    maxWeekRevenueFrom?: number;
    maxWeekRevenueTo?: number;
    messageStatus?: 'Everyone' | 'Friends' | 'None' | 'Recommands' | 'Unset';
    messageStatusList?: 'Everyone' | 'Friends' | 'None' | 'Recommands' | 'Unset';
    name?: string;
    pageNum?: number;
    pageSize?: number;
    platformType?:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
    region?: string;
    regions?: string[];
    responsibleUserId?: number;
    revenueLc?: 'AND' | 'NOT' | 'OR';
    showcase?: boolean;
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
    statusList?: 'NotContacted' | 'Replied' | 'Sent' | 'SignUnsupported' | 'Signed';
    tagIds?: number[];
    /** 标签的逻辑条件，支持OR|AND */
    tagLc?: 'AND' | 'NOT' | 'OR';
  };

  type GhCreatorPerformanceStatVo = {
    /** 已分配的 */
    allocated?: number;
    /** 已建联的 */
    replied?: number;
    /** 认领人ID */
    responsibleUserId?: number;
    /** 已触达的 */
    sent?: number;
  };

  type ghCreatorRegionsGetParams = {
    /** platformType */
    platformType?:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
  };

  type GhCreatorRequest = {
    ids?: number[];
  };

  type ghCreatorResponsibleUserListGetParams = {
    /** platformType */
    platformType?:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
  };

  type ghCreatorStatusListGetParams = {
    /** handles */
    handles: string;
  };

  type GhCreatorStatusVo = {
    handle?: string;
    id?: number;
    status?: 'NotContacted' | 'Replied' | 'Sent' | 'SignUnsupported' | 'Signed';
  };

  type ghCreatorSyncBatchPostParams = {
    /** rank */
    rank?: boolean;
    /** availableStatus */
    availableStatus?: 'Available' | 'Unavailable' | 'Unset';
  };

  type GhCreatorSyncHasNewMsgRequest = {
    handles?: string[];
    hasNewMsg?: boolean;
  };

  type ghCreatorSyncListGetParams = {
    /** limit */
    limit?: number;
  };

  type ghCreatorSyncPostParams = {
    /** rank */
    rank?: boolean;
    /** availableStatus */
    availableStatus?: 'Available' | 'Unavailable' | 'Unset';
  };

  type GhCreatorSyncStatusRequest = {
    handles?: string[];
    status?: 'NotContacted' | 'Replied' | 'Sent' | 'SignUnsupported' | 'Signed';
  };

  type GhCreatorUpdateAnchorRequest = {
    anchor?: boolean;
    handles?: string[];
    ids?: number[];
  };

  type GhCreatorUpdateAvailableRequest = {
    available?: boolean;
    ids?: number[];
    nameList?: string[];
    risk?: boolean;
    signed?: boolean;
  };

  type GhCreatorUpdateBackstageShopIdRequest = {
    backstageShopId?: number;
    creatorIds?: string[];
    handles?: string[];
    ids?: number[];
  };

  type GhCreatorUpdateEliteRequest = {
    elite?: boolean;
    handles?: string[];
    ids?: number[];
  };

  type GhCreatorUpdateGeneralRequest = {
    general?: boolean;
    handles?: string[];
    ids?: number[];
  };

  type GhCreatorUpdateHasNewMsgRequest = {
    handles?: string[];
    hasNewMsg?: boolean;
    ids?: number[];
  };

  type GhCreatorUpdateMessageStatusRequest = {
    handles?: string[];
    ids?: number[];
    messageStatus?: 'Everyone' | 'Friends' | 'None' | 'Recommands' | 'Unset';
    pmByFollow?: boolean;
  };

  type GhCreatorUpdateShowcaseRequest = {
    handles?: string[];
    ids?: number[];
    showcase?: boolean;
  };

  type GhCreatorUpdateStatusRequest = {
    assigned?: boolean;
    handles?: string[];
    ids?: number[];
    status?: 'NotContacted' | 'Replied' | 'Sent' | 'SignUnsupported' | 'Signed';
  };

  type ghDownloadByQueryByTokenGetParams = {
    /** token */
    token: string;
  };

  type GhGeneralSettingsVo = {
    devices?: GhJobDeviceVo[];
    failPolicies?: Record<string, any>;
    ghBackstageConfig?: GhScheduleConfig;
    mobileConfig?: GhScheduleConfig;
    normalConfig?: GhScheduleConfig;
    officialConfig?: GhScheduleConfig;
    /** 流程调度优先级，以逗号分隔开，排在前面的优先调度，默认值为 'SendPm,SyncPm,SendInvite,SyncCreator'  */
    priority?: string;
    /** 主播信息更新配置 */
    syncCreatorConfig?: SyncCreatorConfig;
    /** 联盟号相关配置 */
    unionConfig?: GhScheduleConfig;
  };

  type GhGetRegionByCreatorIdRequest = {
    ids?: string[];
  };

  type GhGifterAllocateRequest = {
    cleanFirst?: boolean;
    ids?: number[];
    responsibleUserIds?: number[];
    tag?: string;
  };

  type GhGifterAssignUser = {
    avatar?: string;
    createTime?: string;
    gender?: 'FEMALE' | 'MALE' | 'UNSPECIFIC';
    ghStatus?: 'NotContacted' | 'Replied' | 'Sent' | 'SignUnsupported' | 'Signed';
    id?: number;
    nickname?: string;
    partnerId?: number;
    residentCity?: string;
    signature?: string;
    status?: 'ACTIVE' | 'BLOCK' | 'DELETED' | 'INACTIVE';
    tenant?: number;
    userType?: 'NORMAL' | 'PARTNER' | 'SHADOW';
  };

  type ghGifterAvatarGetParams = {
    /** creatorId */
    creatorId: string;
  };

  type ghGifterByCreatorIdGetParams = {
    /** creatorId */
    creatorId: number;
  };

  type ghGifterByGifterIdLiveGiftListGetParams = {
    /** gifterId */
    gifterId: number;
  };

  type ghGifterCountGetParams = {
    alias?: string;
    /** 包含指定标签。取false时，tagLc只能是OR */
    containsTag?: boolean;
    createTimeFrom?: string;
    createTimeLastDays?: number;
    createTimeTo?: string;
    creatorHandle?: string;
    deleting?: boolean;
    filterByFavorite?: boolean;
    followerCntFrom?: number;
    followerCntTo?: number;
    giftMaxFrom?: number;
    giftMaxTo?: number;
    giftTotalFrom?: number;
    giftTotalTo?: number;
    gifterLevelFrom?: number;
    gifterLevelTo?: number;
    handle?: string;
    hasNewMsg?: boolean;
    hasNoRegion?: boolean;
    hasResponsibleUser?: boolean;
    lastSyncTimeFrom?: string;
    lastSyncTimeLastDays?: number;
    lastSyncTimeTo?: string;
    limit?: number;
    messageStatusList?: 'Everyone' | 'Friends' | 'None' | 'Recommands' | 'Unset';
    myStatusList?: 'NotContacted' | 'Replied' | 'Sent' | 'SignUnsupported' | 'Signed';
    pageNum?: number;
    pageSize?: number;
    region?: string;
    regions?: string[];
    responsibleUserId?: number;
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
    statusList?: 'NotContacted' | 'Replied' | 'Sent' | 'SignUnsupported' | 'Signed';
    tagIds?: number[];
    /** 标签的逻辑条件，支持OR|AND */
    tagLc?: 'AND' | 'NOT' | 'OR';
    videoCntFrom?: number;
    videoCntTo?: number;
    videoLikesFrom?: number;
    videoLikesTo?: number;
  };

  type ghGifterDeleteByQueryPostParams = {
    /** force */
    force?: boolean;
    /** deleteRemark */
    deleteRemark?: string;
  };

  type ghGifterDeletePostParams = {
    /** force */
    force?: boolean;
    /** deleteRemark */
    deleteRemark?: string;
  };

  type GhGifterDetailVo = {
    alias?: string;
    avatar?: string;
    backstageShopId?: number;
    createTime?: string;
    creatorId?: string;
    deleteRemark?: string;
    deleteTime?: string;
    deleting?: boolean;
    followerCnt?: number;
    /** 打赏记录条数 */
    giftCount?: number;
    giftMax?: number;
    giftTotal?: number;
    gifterLevel?: number;
    handle?: string;
    hasNewMsg?: boolean;
    hyperlink?: string;
    id?: number;
    lastSyncTime?: string;
    messageStatus?: 'Everyone' | 'Friends' | 'None' | 'Recommands' | 'Unset';
    mobileAccountId?: number;
    officialShopId?: number;
    pmShopId?: number;
    region?: string;
    responsibleUserId?: number;
    responsibleUsers?: GhGifterAssignUser[];
    status?: 'NotContacted' | 'Replied' | 'Sent' | 'SignUnsupported' | 'Signed';
    /** 标签 */
    tags?: TagDto[];
    teamId?: number;
    videoCnt?: number;
    videoLikes?: number;
  };

  type GhGifterDto = {
    alias?: string;
    avatar?: string;
    backstageShopId?: number;
    createTime?: string;
    creatorId?: string;
    deleteRemark?: string;
    deleteTime?: string;
    deleting?: boolean;
    followerCnt?: number;
    giftMax?: number;
    giftTotal?: number;
    gifterLevel?: number;
    handle?: string;
    hasNewMsg?: boolean;
    id?: number;
    lastSyncTime?: string;
    messageStatus?: 'Everyone' | 'Friends' | 'None' | 'Recommands' | 'Unset';
    mobileAccountId?: number;
    officialShopId?: number;
    pmShopId?: number;
    region?: string;
    responsibleUserId?: number;
    status?: 'NotContacted' | 'Replied' | 'Sent' | 'SignUnsupported' | 'Signed';
    teamId?: number;
    videoCnt?: number;
    videoLikes?: number;
  };

  type ghGifterPageGetParams = {
    alias?: string;
    /** 包含指定标签。取false时，tagLc只能是OR */
    containsTag?: boolean;
    createTimeFrom?: string;
    createTimeLastDays?: number;
    createTimeTo?: string;
    creatorHandle?: string;
    deleting?: boolean;
    filterByFavorite?: boolean;
    followerCntFrom?: number;
    followerCntTo?: number;
    giftMaxFrom?: number;
    giftMaxTo?: number;
    giftTotalFrom?: number;
    giftTotalTo?: number;
    gifterLevelFrom?: number;
    gifterLevelTo?: number;
    handle?: string;
    hasNewMsg?: boolean;
    hasNoRegion?: boolean;
    hasResponsibleUser?: boolean;
    lastSyncTimeFrom?: string;
    lastSyncTimeLastDays?: number;
    lastSyncTimeTo?: string;
    limit?: number;
    messageStatusList?: 'Everyone' | 'Friends' | 'None' | 'Recommands' | 'Unset';
    myStatusList?: 'NotContacted' | 'Replied' | 'Sent' | 'SignUnsupported' | 'Signed';
    pageNum?: number;
    pageSize?: number;
    region?: string;
    regions?: string[];
    responsibleUserId?: number;
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
    statusList?: 'NotContacted' | 'Replied' | 'Sent' | 'SignUnsupported' | 'Signed';
    tagIds?: number[];
    /** 标签的逻辑条件，支持OR|AND */
    tagLc?: 'AND' | 'NOT' | 'OR';
    videoCntFrom?: number;
    videoCntTo?: number;
    videoLikesFrom?: number;
    videoLikesTo?: number;
  };

  type ghGifterPageResponsibleGetParams = {
    alias?: string;
    /** 包含指定标签。取false时，tagLc只能是OR */
    containsTag?: boolean;
    createTimeFrom?: string;
    createTimeLastDays?: number;
    createTimeTo?: string;
    creatorHandle?: string;
    deleting?: boolean;
    filterByFavorite?: boolean;
    followerCntFrom?: number;
    followerCntTo?: number;
    giftMaxFrom?: number;
    giftMaxTo?: number;
    giftTotalFrom?: number;
    giftTotalTo?: number;
    gifterLevelFrom?: number;
    gifterLevelTo?: number;
    handle?: string;
    hasNewMsg?: boolean;
    hasNoRegion?: boolean;
    hasResponsibleUser?: boolean;
    lastSyncTimeFrom?: string;
    lastSyncTimeLastDays?: number;
    lastSyncTimeTo?: string;
    limit?: number;
    messageStatusList?: 'Everyone' | 'Friends' | 'None' | 'Recommands' | 'Unset';
    myStatusList?: 'NotContacted' | 'Replied' | 'Sent' | 'SignUnsupported' | 'Signed';
    pageNum?: number;
    pageSize?: number;
    region?: string;
    regions?: string[];
    responsibleUserId?: number;
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
    statusList?: 'NotContacted' | 'Replied' | 'Sent' | 'SignUnsupported' | 'Signed';
    tagIds?: number[];
    /** 标签的逻辑条件，支持OR|AND */
    tagLc?: 'AND' | 'NOT' | 'OR';
    videoCntFrom?: number;
    videoCntTo?: number;
    videoLikesFrom?: number;
    videoLikesTo?: number;
  };

  type ghHasInteractionGetParams = {
    /** 店铺ID，为null时，从团队全局统计 */
    shopId?: number;
    /** 达人账号 */
    handle: string;
    /** 交互类型 */
    interactType?: 'ImportCreator' | 'Invite' | 'SendMsg' | 'SyncMsg' | 'UpdateInfo';
    /** interactTimeFrom */
    interactTimeFrom?: number;
    /** interactTimeTo */
    interactTimeTo?: number;
  };

  type ghImportExcelByScenePostParams = {
    /** scene */
    scene:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
  };

  type ghImportPostParams = {
    /** scene */
    scene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
  };

  type ghInteractionByIdGetParams = {
    /** id */
    id: number;
  };

  type GhInteractionDetailVo = {
    creatorId?: number;
    description?: string;
    extraInfo?: Record<string, any>;
    flowId?: number;
    id?: number;
    interactTime?: number;
    interactType?: 'ImportCreator' | 'Invite' | 'SendMsg' | 'SyncMsg' | 'UpdateInfo';
    interactionId?: string;
    operator?: UserDto;
    operatorId?: number;
    rpaFlow?: RpaFlowDto;
    rpaTask?: RpaTaskDto;
    rpaType?: 'Browser' | 'Extension' | 'IOS' | 'Mobile';
    shop?: ShopDto;
    shopId?: number;
    success?: boolean;
    taskId?: number;
    teamId?: number;
  };

  type GhInteractionDto = {
    creatorId?: number;
    description?: string;
    flowId?: number;
    id?: number;
    interactTime?: number;
    interactType?: 'ImportCreator' | 'Invite' | 'SendMsg' | 'SyncMsg' | 'UpdateInfo';
    interactionId?: string;
    operatorId?: number;
    rpaType?: 'Browser' | 'Extension' | 'IOS' | 'Mobile';
    shopId?: number;
    success?: boolean;
    taskId?: number;
    teamId?: number;
  };

  type ghInteractionPageGetParams = {
    /** shopId */
    shopId?: number;
    /** 花漾达人库ID，不是TK库的ID */
    creatorId: number;
    /** 交互类型 */
    interactType?: 'ImportCreator' | 'Invite' | 'SendMsg' | 'SyncMsg' | 'UpdateInfo';
    /** 操作者ID */
    operatorId?: number;
    /** 交互时间开始 */
    interactTimeFrom?: string;
    /** 交互时间截止 */
    interactTimeTo?: string;
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type GhInteractionVo = {
    /** 达人ID */
    creatorId?: string;
    /** 交互的描述 */
    description?: string;
    /** 额外信息，转化成JSON总长度不要超过8000 */
    extraInfo?: Record<string, any>;
    /** 操作流程 */
    flowId?: number;
    /** 达人账号 */
    handle?: string;
    /** 交互时间戳 */
    interactTimestamp?: number;
    /** 交互类型 */
    interactType?: 'ImportCreator' | 'Invite' | 'SendMsg' | 'SyncMsg' | 'UpdateInfo';
    /** 交互D */
    interactionId?: string;
    /** RpaType */
    rpaType?: 'Browser' | 'Extension' | 'IOS' | 'Mobile';
    /** 店铺ID */
    shopId?: number;
    /** 是否成功 */
    success?: boolean;
    /** 流程任务ID */
    taskId?: number;
  };

  type GhJobDetailVo = {
    /** 什么类型的达人 */
    bizScene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
    clientId?: string;
    /** 该条任务创建时间 */
    createTime?: string;
    /** 创建者id */
    creatorId?: number;
    /** 创建者昵称 */
    creatorName?: string;
    creators?: SimpleGhCreatorVo[];
    description?: string;
    /** 执行设备 */
    device?: LoginDeviceDto;
    /** 执行设备的名称 */
    deviceName?: string;
    /** 执行结束时间 */
    executeEndTime?: string;
    /** 开始执行时间 */
    executeTime?: string;
    /** 主播id */
    ghCreatorId?: number;
    /** 主播昵称 */
    ghCreatorName?: string;
    ghPlanId?: number;
    /** 公会平台类型，tk、抖音、小红书 */
    ghPlatformType?:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
    ghScheduleType?: 'GhBackstage' | 'Mobile' | 'Normal' | 'Official' | 'Union' | 'Unknown';
    id?: number;
    jobType?:
      | 'AccountFollow'
      | 'AccountHealthCheck'
      | 'AccountMaintenance'
      | 'AccountMessageCheck'
      | 'KOL_LiveRewards'
      | 'ParentJob'
      | 'SendInvite'
      | 'SendPm'
      | 'SendUserCard'
      | 'ShareVideo'
      | 'SubJob'
      | 'SyncContactToPhone'
      | 'SyncContacts'
      | 'SyncCreator'
      | 'SyncLiveStream'
      | 'SyncPm'
      | 'TS_AddContacts'
      | 'TS_AddFriends'
      | 'TS_DemandPayment'
      | 'TS_IMChatByFilter'
      | 'TS_IMChatByHandle'
      | 'TS_LoginCheck'
      | 'TS_SampleApprove'
      | 'TS_SendBuyerIMChat'
      | 'TS_SendEmail'
      | 'TS_SendFacebook'
      | 'TS_SendLine'
      | 'TS_SendWhatsApp'
      | 'TS_SendZalo'
      | 'TS_SyncBuyerInfo'
      | 'TS_SyncCreator'
      | 'TS_SyncOrders'
      | 'TS_SyncProducts'
      | 'TS_SyncSampleCreator'
      | 'TS_SyncShopInfo'
      | 'TS_SyncVideos'
      | 'TS_TargetPlanByFilter'
      | 'TS_TargetPlanByHandle'
      | 'TS_TargetPlanClear'
      | 'TS_VideoADCode';
    /** 任务参数，json格式 */
    params?: string;
    planRunId?: string;
    rpaTaskId?: number;
    rpaType?: 'Browser' | 'Extension' | 'IOS' | 'Mobile';
    /** 用来执行该job的分身 */
    shopId?: number;
    /** 用来执行该job的分身名 */
    shopName?: string;
    shops?: SimpleShopVo[];
    /** job状态 */
    status?: 'Cancelled' | 'Failed' | 'Pending' | 'Running' | 'Scheduled' | 'Succeed';
    /** 子任务个数，当任务为信息更新/私信更新时，根据该字段来显示 xxx个主播  */
    subCounts?: number;
    teamId?: number;
  };

  type GhJobDeviceVo = {
    appVersion?: string;
    clientId?: string;
    concurrent?: number;
    cpus?: number;
    deviceType?: 'App' | 'Browser' | 'Extension' | 'HYRuntime' | 'RpaExecutor';
    hostName?: string;
    id?: number;
    lastUserId?: number;
    mem?: number;
    online?: boolean;
    osName?: string;
    rpaVoucherValid?: boolean;
    userAgent?: string;
  };

  type GhJobDto = {
    /** 什么类型的达人 */
    bizScene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
    clientId?: string;
    /** 该条任务创建时间 */
    createTime?: string;
    /** 创建者id */
    creatorId?: number;
    /** 创建者昵称 */
    creatorName?: string;
    description?: string;
    /** 执行设备 */
    device?: LoginDeviceDto;
    /** 执行设备的名称 */
    deviceName?: string;
    /** 执行结束时间 */
    executeEndTime?: string;
    /** 开始执行时间 */
    executeTime?: string;
    /** 主播id */
    ghCreatorId?: number;
    /** 主播昵称 */
    ghCreatorName?: string;
    ghPlanId?: number;
    /** 公会平台类型，tk、抖音、小红书 */
    ghPlatformType?:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
    ghScheduleType?: 'GhBackstage' | 'Mobile' | 'Normal' | 'Official' | 'Union' | 'Unknown';
    id?: number;
    jobType?:
      | 'AccountFollow'
      | 'AccountHealthCheck'
      | 'AccountMaintenance'
      | 'AccountMessageCheck'
      | 'KOL_LiveRewards'
      | 'ParentJob'
      | 'SendInvite'
      | 'SendPm'
      | 'SendUserCard'
      | 'ShareVideo'
      | 'SubJob'
      | 'SyncContactToPhone'
      | 'SyncContacts'
      | 'SyncCreator'
      | 'SyncLiveStream'
      | 'SyncPm'
      | 'TS_AddContacts'
      | 'TS_AddFriends'
      | 'TS_DemandPayment'
      | 'TS_IMChatByFilter'
      | 'TS_IMChatByHandle'
      | 'TS_LoginCheck'
      | 'TS_SampleApprove'
      | 'TS_SendBuyerIMChat'
      | 'TS_SendEmail'
      | 'TS_SendFacebook'
      | 'TS_SendLine'
      | 'TS_SendWhatsApp'
      | 'TS_SendZalo'
      | 'TS_SyncBuyerInfo'
      | 'TS_SyncCreator'
      | 'TS_SyncOrders'
      | 'TS_SyncProducts'
      | 'TS_SyncSampleCreator'
      | 'TS_SyncShopInfo'
      | 'TS_SyncVideos'
      | 'TS_TargetPlanByFilter'
      | 'TS_TargetPlanByHandle'
      | 'TS_TargetPlanClear'
      | 'TS_VideoADCode';
    /** 任务参数，json格式 */
    params?: string;
    planRunId?: string;
    rpaTaskId?: number;
    rpaType?: 'Browser' | 'Extension' | 'IOS' | 'Mobile';
    /** 用来执行该job的分身 */
    shopId?: number;
    /** 用来执行该job的分身名 */
    shopName?: string;
    /** job状态 */
    status?: 'Cancelled' | 'Failed' | 'Pending' | 'Running' | 'Scheduled' | 'Succeed';
    /** 子任务个数，当任务为信息更新/私信更新时，根据该字段来显示 xxx个主播  */
    subCounts?: number;
    teamId?: number;
  };

  type GhJobPlanVo = {
    /** 场景 */
    bizScene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
    /** 邀约时评论要使用的话术列表，会随机从中选择发送。只在 ghJobType 为 SendInvite 时有意义 */
    commentWordIds?: number[];
    createTime?: string;
    /** 查询条件，由 /api/gh/creator/page 这个接口的params JSON.stringify(params) 而来 */
    creatorFilter?: string;
    /** 重复日期，只包含星期信息的表达式(具体参考自动流程计划) */
    cronExpression?: string;
    deleteMsg?: boolean;
    /** 同步时是否删除与主播的聊天记录，默认为true。只在 ghJobType 为 SyncPm 时有意义 */
    deleteSession?: boolean;
    /** 是否启用(目前UI无法体现这一属性) */
    enabled?: boolean;
    /** 如果是随机执行，用-隔开开始时间和结束时间[start, start-end, start, ...] */
    expressions?: string[];
    /** 公会平台类型，tk、抖音、小红书 */
    ghPlatformType?:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
    /** 拟使用的TK账号类型，默认值 Normal。只在 ghJobType 为 SendInvite 时有意义 */
    ghScheduleType?: 'GhBackstage' | 'Mobile' | 'Normal' | 'Official' | 'Union' | 'Unknown';
    id?: number;
    /** 私信高级设置，只在 ghJobType 为 SendInvite/AccountMaintenance 时有意义 */
    inviteAdvanceSettings?: Record<string, any>;
    /** 发送邀约手机账号选择策略，仅在 ghScheduleType == Mobile 的时候有意义 */
    inviteMobileChoicePolicy?: MobileChoicePolicy;
    /** 发送邀约分身选择策略，仅在 ghScheduleType != Mobile 的时候有意义 */
    inviteShopChoicePolicy?: ShopChoicePolicy;
    jobType?:
      | 'AccountFollow'
      | 'AccountHealthCheck'
      | 'AccountMaintenance'
      | 'AccountMessageCheck'
      | 'KOL_LiveRewards'
      | 'ParentJob'
      | 'SendInvite'
      | 'SendPm'
      | 'SendUserCard'
      | 'ShareVideo'
      | 'SubJob'
      | 'SyncContactToPhone'
      | 'SyncContacts'
      | 'SyncCreator'
      | 'SyncLiveStream'
      | 'SyncPm'
      | 'TS_AddContacts'
      | 'TS_AddFriends'
      | 'TS_DemandPayment'
      | 'TS_IMChatByFilter'
      | 'TS_IMChatByHandle'
      | 'TS_LoginCheck'
      | 'TS_SampleApprove'
      | 'TS_SendBuyerIMChat'
      | 'TS_SendEmail'
      | 'TS_SendFacebook'
      | 'TS_SendLine'
      | 'TS_SendWhatsApp'
      | 'TS_SendZalo'
      | 'TS_SyncBuyerInfo'
      | 'TS_SyncCreator'
      | 'TS_SyncOrders'
      | 'TS_SyncProducts'
      | 'TS_SyncSampleCreator'
      | 'TS_SyncShopInfo'
      | 'TS_SyncVideos'
      | 'TS_TargetPlanByFilter'
      | 'TS_TargetPlanByHandle'
      | 'TS_TargetPlanClear'
      | 'TS_VideoADCode';
    /** 最多允许执行多少分钟(需求方要求是流程自己实现，所以应该只有部分流程支持这个参数) */
    maxMinutes?: number;
    /** 相应的手机账号id，只有在 ghJobType 为 AccountMaintenance 时有意义 */
    mobileAccountIds?: number[];
    name?: string;
    /** 下次执行时间 */
    nextFireTime?: string;
    /** 计划类型，归属团队还是个人 */
    planType?: 'Team' | 'User';
    /** 发送私信前关注主播并将私信内容评论到主播的第一条视频 */
    sendComment?: boolean;
    /** 是否增加随机表情 */
    sendEmoji?: boolean;
    /** 是否发送邀约卡片，只有发送邀约建联计划才有意义(只有公会后台账号才支持此特性） */
    sendInviteCard?: boolean;
    /** 已分配/已认领的达人不再发送 (为空表示 true )，只对发送邀约建联的计划有意义 */
    skipResponsible?: boolean;
    teamId?: number;
    /** 邀约要使用的话术列表，会随机从中选择发送。只在 ghJobType 为 SendInvite 时有意义 */
    wordsIds?: number[];
  };

  type ghJobsBatchCancelJobsDeleteParams = {
    /** jobIds */
    jobIds: number;
  };

  type ghJobsCancelJobDeleteParams = {
    /** jobId */
    jobId: number;
  };

  type ghJobsJobDetailGetParams = {
    /** jobId */
    jobId: number;
    /** 是不是查任务池，否则查历史任务表 */
    alive?: boolean;
  };

  type ghJobsListJobsHistoryGetParams = {
    /** 为空表示所有类型 */
    jobType?:
      | 'AccountFollow'
      | 'AccountHealthCheck'
      | 'AccountMaintenance'
      | 'AccountMessageCheck'
      | 'KOL_LiveRewards'
      | 'ParentJob'
      | 'SendInvite'
      | 'SendPm'
      | 'SendUserCard'
      | 'ShareVideo'
      | 'SubJob'
      | 'SyncContactToPhone'
      | 'SyncContacts'
      | 'SyncCreator'
      | 'SyncLiveStream'
      | 'SyncPm'
      | 'TS_AddContacts'
      | 'TS_AddFriends'
      | 'TS_DemandPayment'
      | 'TS_IMChatByFilter'
      | 'TS_IMChatByHandle'
      | 'TS_LoginCheck'
      | 'TS_SampleApprove'
      | 'TS_SendBuyerIMChat'
      | 'TS_SendEmail'
      | 'TS_SendFacebook'
      | 'TS_SendLine'
      | 'TS_SendWhatsApp'
      | 'TS_SendZalo'
      | 'TS_SyncBuyerInfo'
      | 'TS_SyncCreator'
      | 'TS_SyncOrders'
      | 'TS_SyncProducts'
      | 'TS_SyncSampleCreator'
      | 'TS_SyncShopInfo'
      | 'TS_SyncVideos'
      | 'TS_TargetPlanByFilter'
      | 'TS_TargetPlanByHandle'
      | 'TS_TargetPlanClear'
      | 'TS_VideoADCode';
    /** 为空表示所有类型，仅当jobType=SendInvite时有意义 */
    ghScheduleType?: 'GhBackstage' | 'Mobile' | 'Normal' | 'Official' | 'Union' | 'Unknown';
    /** 为空表示所有类型 */
    ghPlatformType?:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
    /** 为空表示所有状态 */
    status?: 'Cancelled' | 'Failed' | 'Pending' | 'Running' | 'Scheduled' | 'Succeed';
    /** 按达人精准ID过滤，注意不是like！！！ */
    creator?: string;
    /** 手机id，为空表示不限制手机（实际可能以该手机下对应的手机账号去查询） */
    mobileId?: number;
    /** 分身id或者手机账号id */
    shopId?: number;
    /** 是否用户触发的查询，区别setInterval查询 */
    userEvent?: boolean;
    /** createFrom */
    createFrom?: string;
    /** createTo */
    createTo?: string;
    /** executeFrom */
    executeFrom?: string;
    /** executeTo */
    executeTo?: string;
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** 格式为[field_name asc|desc], 可选列：create_time,execute_end_time */
    orderBy?: string;
  };

  type ghJobsListJobsPendingGetParams = {
    /** 为空表示所有类型 */
    jobType?:
      | 'AccountFollow'
      | 'AccountHealthCheck'
      | 'AccountMaintenance'
      | 'AccountMessageCheck'
      | 'KOL_LiveRewards'
      | 'ParentJob'
      | 'SendInvite'
      | 'SendPm'
      | 'SendUserCard'
      | 'ShareVideo'
      | 'SubJob'
      | 'SyncContactToPhone'
      | 'SyncContacts'
      | 'SyncCreator'
      | 'SyncLiveStream'
      | 'SyncPm'
      | 'TS_AddContacts'
      | 'TS_AddFriends'
      | 'TS_DemandPayment'
      | 'TS_IMChatByFilter'
      | 'TS_IMChatByHandle'
      | 'TS_LoginCheck'
      | 'TS_SampleApprove'
      | 'TS_SendBuyerIMChat'
      | 'TS_SendEmail'
      | 'TS_SendFacebook'
      | 'TS_SendLine'
      | 'TS_SendWhatsApp'
      | 'TS_SendZalo'
      | 'TS_SyncBuyerInfo'
      | 'TS_SyncCreator'
      | 'TS_SyncOrders'
      | 'TS_SyncProducts'
      | 'TS_SyncSampleCreator'
      | 'TS_SyncShopInfo'
      | 'TS_SyncVideos'
      | 'TS_TargetPlanByFilter'
      | 'TS_TargetPlanByHandle'
      | 'TS_TargetPlanClear'
      | 'TS_VideoADCode';
    /** 为空表示所有类型，仅当jobType=SendInvite时有意义 */
    ghScheduleType?: 'GhBackstage' | 'Mobile' | 'Normal' | 'Official' | 'Union' | 'Unknown';
    /** 为空表示所有类型 */
    ghPlatformType?:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
    /** 为空表示所有状态 */
    status?: 'Cancelled' | 'Failed' | 'Pending' | 'Running' | 'Scheduled' | 'Succeed';
    /** 按达人精准ID过滤，注意不是like！！！ */
    creator?: string;
    /** 手机id，为空表示不限制手机（实际可能以该手机下对应的手机账号去查询） */
    mobileId?: number;
    /** 分身id或者手机账号id */
    shopId?: number;
    /** 是否用户触发的查询，区别setInterval查询 */
    userEvent?: boolean;
    /** createFrom */
    createFrom?: string;
    /** createTo */
    createTo?: string;
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** 格式为[field_name asc|desc], 可选列：create_time,execute_end_time */
    orderBy?: string;
  };

  type ghJobsListShopsGetParams = {
    /** history */
    history: boolean;
    /** ghPlatformType */
    ghPlatformType:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
    /** rpaType */
    rpaType: 'Browser' | 'Extension' | 'IOS' | 'Mobile';
  };

  type ghJobsQueryCreatingInfoGetParams = {
    /** jobId */
    jobId: string;
  };

  type ghJobsSyncAllPmRequestPutParams = {
    /** 同步时是否同步删除与主播的聊天记录，默认为true */
    deleteSession?: boolean;
    /** ghPlatformType */
    ghPlatformType:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
    /** ghBizScene */
    ghBizScene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
  };

  type ghJobsSyncKakaoChatsPutParams = {
    /** mobileId */
    mobileId: number;
  };

  type ghJobsSyncKakaoContactsPutParams = {
    /** mobileId */
    mobileId: number;
  };

  type ghJobsTaskFetchJobGetParams = {
    /** rpaTaskId */
    rpaTaskId: number;
  };

  type ghLatestInteractionGetParams = {
    /** 店铺ID，为null时，从团队全局统计 */
    shopId?: number;
    /** 达人账号 */
    handle: string;
    /** 交互类型 */
    interactType: 'ImportCreator' | 'Invite' | 'SendMsg' | 'SyncMsg' | 'UpdateInfo';
  };

  type GhLiveCreatorDetailVo = {
    createTime?: string;
    creator?: GhCreatorDto;
    gameRank?: number;
    handle?: string;
    hourRevenue?: number;
    id?: number;
    lastSyncTime?: string;
    likes?: number;
    liveId?: string;
    liveTime?: string;
    popularRank?: number;
    region?: string;
    revenue?: number;
    /** 标签 */
    tags?: TagDto[];
    teamId?: number;
    viewers?: number;
    weekRevenue?: number;
  };

  type GhLiveCreatorDocumentBatch = {
    creators?: GhLiveDocument[];
  };

  type ghLiveCreatorMarkOfflinePostParams = {
    /** handle */
    handle: string;
  };

  type ghLiveCreatorPageGetParams = {
    /** 包含指定标签。取false时，tagLc只能是OR */
    containsTag?: boolean;
    elite?: boolean;
    filterByFavorite?: boolean;
    gameRankFrom?: number;
    gameRankTo?: number;
    handle?: string;
    hasResponsibleUser?: boolean;
    lastSyncTimeFrom?: string;
    lastSyncTimeTo?: string;
    likesFrom?: number;
    likesTo?: number;
    liveTimeFrom?: string;
    liveTimeTo?: string;
    messageStatusList?: 'Everyone' | 'Friends' | 'None' | 'Recommands' | 'Unset';
    pageNum?: number;
    pageSize?: number;
    popularRankFrom?: number;
    popularRankTo?: number;
    region?: string;
    regions?: string[];
    responsibleUserId?: number;
    revenueFrom?: number;
    revenueTo?: number;
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
    statusList?: 'NotContacted' | 'Replied' | 'Sent' | 'SignUnsupported' | 'Signed';
    tagIds?: number[];
    /** 标签的逻辑条件，支持OR|AND */
    tagLc?: 'AND' | 'NOT' | 'OR';
    viewersFrom?: number;
    viewersTo?: number;
  };

  type GhLiveDocument = {
    createTimestamp?: number;
    creatorId?: string;
    gameRank?: number;
    handle?: string;
    hourRevenue?: number;
    likes?: number;
    liveId?: string;
    popularRank?: number;
    region?: string;
    revenue?: number;
    viewers?: number;
    weekRevenue?: number;
  };

  type GhLiveGiftDocument = {
    avatar?: string;
    createTimestamp?: number;
    gift?: number;
    gifterAvatar?: string;
    gifterHandle?: string;
    /** 送礼者等级 */
    gifterLevel?: number;
    gitferCreatorId?: string;
    gitferRegion?: string;
    handle?: string;
    liveId?: string;
    liveLevel?: number;
  };

  type GhLiveGiftDto = {
    avatar?: string;
    gift?: number;
    gifterHandle?: string;
    gifterId?: number;
    gitferCreatorId?: string;
    handle?: string;
    id?: number;
    liveId?: string;
    liveLevel?: number;
    liveTime?: string;
    teamId?: number;
  };

  type ghLivePageGetParams = {
    /** handle */
    handle: string;
    /** region */
    region: string;
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** sortField */
    sortField?: string;
    /** sortOrder */
    sortOrder?: 'asc' | 'desc';
  };

  type GhMessageDto = {
    accountId?: number;
    accountName?: string;
    bizScene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
    cid?: string;
    content?: string;
    creatorId?: number;
    extra?: string;
    handle?: string;
    id?: number;
    messageId?: string;
    messageSource?: 'Creator' | 'TkUser';
    replyUserId?: number;
    rpaType?: 'Browser' | 'Extension' | 'IOS' | 'Mobile';
    scheduleType?: 'GhBackstage' | 'Mobile' | 'Normal' | 'Official' | 'Union' | 'Unknown';
    sendTime?: string;
    status?: 'Fail' | 'Pending' | 'Success';
    syncTime?: string;
    teamId?: number;
  };

  type ghMessagePageGetParams = {
    /** creatorId */
    creatorId: number;
    /** rpaType */
    rpaType?: 'Browser' | 'Extension' | 'IOS' | 'Mobile';
    /** scene */
    scene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type ghMessageSessionByIdStatusPutParams = {
    /** id */
    id: number;
    /** status */
    status: 'Error' | 'NeedReply' | 'Replied' | 'Replying';
  };

  type GhMessageVo = {
    /** 消息内容 */
    content?: string;
    /** json格式的额外信息，保存消息是通过哪个手机的账号发出去的 */
    extra?: string;
    /** 消息ID，全局唯一（如果有） */
    messageId?: string;
    /** 谁发送的消息 */
    messageSource?: 'Creator' | 'TkUser';
    /** 消息是手机发送的还是浏览器发送的 */
    rpaType?: 'Browser' | 'Extension' | 'IOS' | 'Mobile';
    /** 消息发送时间（如果有） */
    sendTimestamp?: number;
  };

  type ghPlansCheckNameExistsGetParams = {
    /** name */
    name: string;
    /** ghPlatformType */
    ghPlatformType:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
  };

  type ghPlansDeletePlanDeleteParams = {
    /** ghJobPlanId */
    ghJobPlanId: number;
  };

  type ghPlansPlansGetParams = {
    /** ghPlatformType */
    ghPlatformType:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
    /** ghPlanType */
    ghPlanType?: 'Team' | 'User';
  };

  type ghPlansTogglePlanEnabledPutParams = {
    /** ghJobPlanId */
    ghJobPlanId: number;
    /** enabled */
    enabled?: boolean;
  };

  type ghPubCreatorEarliestUpdatedCreatorsGetParams = {
    /** limit */
    limit?: number;
    /** region */
    region?: string;
    /** countryCode */
    countryCode?: string;
  };

  type ghPubCreatorScrapTkUserProfileGetParams = {
    /** handle */
    handle: string;
  };

  type ghPubCreatorSyncBatchPostParams = {
    /** rank */
    rank?: boolean;
  };

  type GhRevenueDocument = {
    gameRank?: number;
    handle?: string;
    hourRevenue?: number;
    popularRank?: number;
    region?: string;
    revenue?: number;
    weekRevenue?: number;
  };

  type GhScheduleConfig = {
    /** 有哪些表示账号已经封禁的标签，目前key的取值: account_logged_out | account_banned | account_limited | pm_too_fast | need_config_privacy */
    blockTags?: Record<string, any>;
    /** 发送时最高并发数，为空或者为<=0都表示不限制 (目前只在公会后台账号邀约建联时有意义) */
    concurrent?: number;
    /** 允许邀请的时间 */
    inviteTimeFrame?: AllowTimeConfig;
    /** 单账号每日最多私信条数 */
    maxShopDayPm?: number;
    /** 单账号每批次最多私信条数 */
    maxShopTaskPm?: number;
    /** 打上了这个标签的分身的个数 */
    shopCount?: number;
    /** 发送私信的分身的标签 */
    shopTag?: string;
    /** 单账号每批私信间隔，单位分钟 */
    shopTaskPmInterval?: number;
  };

  type GhSessionVo = {
    accountId?: number;
    avatar?: string;
    bizScene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
    cid?: string;
    content?: string;
    creatorId?: number;
    description?: string;
    handle?: string;
    id?: number;
    lastPushTime?: string;
    lastReplyTime?: string;
    receiveIds?: number[];
    replyUserId?: number;
    status?: 'Error' | 'NeedReply' | 'Replied' | 'Replying';
    teamId?: number;
  };

  type ghSettingsBindRpaDevicePutParams = {
    /** concurrent */
    concurrent?: number;
  };

  type ghSettingsCountriesPutParams = {
    /** area */
    area: string;
    /** countries */
    countries: string;
  };

  type ghSettingsCreatorTypesPutParams = {
    /** types */
    types: string;
  };

  type ghSettingsDeleteParams = {
    /** ghJobDeviceId */
    ghJobDeviceId: number;
  };

  type ghSettingsDeleteRemarksPutParams = {
    /** deleteRemarks */
    deleteRemarks: string;
  };

  type ghSettingsGetShopStatisticsGetParams = {
    /** ghPlatformType */
    ghPlatformType:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
  };

  type ghSettingsKeepDaysPutParams = {
    /** keepDays */
    keepDays: number;
  };

  type ghSettingsKeepTagDaysPutParams = {
    /** keepTagDays */
    keepTagDays: number;
  };

  type ghSettingsLiveHoursPutParams = {
    /** liveHours */
    liveHours: number;
  };

  type ghSettingsLoadBlockTagsGetParams = {
    /** ghPlatformType */
    ghPlatformType:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
    /** mobile */
    mobile: boolean;
  };

  type ghSettingsLoadSettingsGetParams = {
    /** ghPlatformType */
    ghPlatformType:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
  };

  type ghSettingsMinRevenuePutParams = {
    /** minRevenue */
    minRevenue: number;
  };

  type ghSettingsMobileDetailGetParams = {
    /** mobileId */
    mobileId: number;
  };

  type ghSettingsUpdateFailPolicyPutParams = {
    /** ghPlatformType */
    ghPlatformType:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
    /** jobType */
    jobType:
      | 'AccountFollow'
      | 'AccountHealthCheck'
      | 'AccountMaintenance'
      | 'AccountMessageCheck'
      | 'KOL_LiveRewards'
      | 'ParentJob'
      | 'SendInvite'
      | 'SendPm'
      | 'SendUserCard'
      | 'ShareVideo'
      | 'SubJob'
      | 'SyncContactToPhone'
      | 'SyncContacts'
      | 'SyncCreator'
      | 'SyncLiveStream'
      | 'SyncPm'
      | 'TS_AddContacts'
      | 'TS_AddFriends'
      | 'TS_DemandPayment'
      | 'TS_IMChatByFilter'
      | 'TS_IMChatByHandle'
      | 'TS_LoginCheck'
      | 'TS_SampleApprove'
      | 'TS_SendBuyerIMChat'
      | 'TS_SendEmail'
      | 'TS_SendFacebook'
      | 'TS_SendLine'
      | 'TS_SendWhatsApp'
      | 'TS_SendZalo'
      | 'TS_SyncBuyerInfo'
      | 'TS_SyncCreator'
      | 'TS_SyncOrders'
      | 'TS_SyncProducts'
      | 'TS_SyncSampleCreator'
      | 'TS_SyncShopInfo'
      | 'TS_SyncVideos'
      | 'TS_TargetPlanByFilter'
      | 'TS_TargetPlanByHandle'
      | 'TS_TargetPlanClear'
      | 'TS_VideoADCode';
    /** failPolicy */
    failPolicy: 'Ignore' | 'Retry';
  };

  type ghSettingsUpdateRpaDeviceConcurrentPutParams = {
    /** ghJobDeviceId */
    ghJobDeviceId: number;
    /** concurrent */
    concurrent?: number;
  };

  type ghSettingsUpdateSchedulePriorityPutParams = {
    /** ghPlatformType */
    ghPlatformType:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
    /** priority */
    priority: string;
  };

  type GhShopStatistics = {
    /** 筛选主播账号个数 */
    filterShopCount?: number;
    /** 公会后台私信账号个数 */
    ghBackendShopCount?: number;
    ghPlatformType?:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
    /** 手机私信账号数量(暂时不可用) */
    invalidPmMobileAccountCount?: number;
    /** 官方账号个数 */
    officePmShopCount?: number;
    /** 联盟号数量 */
    unionPmShopCount?: number;
    /** 手机私信账号数量(正常) */
    validPmMobileAccountCount?: number;
  };

  type ghSpeechAccountConfigGetParams = {
    /** accountId */
    accountId: number;
    /** rpaType */
    rpaType: 'Browser' | 'Extension' | 'IOS' | 'Mobile';
  };

  type ghSpeechAccountConfigPutParams = {
    /** accountId */
    accountId: number;
    /** rpaType */
    rpaType: 'Browser' | 'Extension' | 'IOS' | 'Mobile';
    /** groupId */
    groupId?: number;
  };

  type ghSpeechByIdGetParams = {
    /** id */
    id: number;
  };

  type ghSpeechByIdPutParams = {
    /** id */
    id: number;
    /** name */
    name?: string;
    /** text */
    text: string;
  };

  type GhSpeechDto = {
    bizScene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
    content?: string;
    createTime?: string;
    groupId?: number;
    id?: number;
    name?: string;
    sortNo?: number;
    speechType?:
      | 'BuyerImChat'
      | 'BuyerSendMsg'
      | 'Comment'
      | 'ImChat'
      | 'Invite'
      | 'LiveComment'
      | 'RequestAdCode'
      | 'SendMsg'
      | 'TargetPlan';
    teamId?: number;
  };

  type ghSpeechGroupByGroupIdDeleteParams = {
    /** groupId */
    groupId: number;
    /** confirm */
    confirm: boolean;
  };

  type ghSpeechGroupByIdNamePutParams = {
    /** id */
    id: number;
    /** name */
    name: string;
  };

  type GhSpeechGroupDto = {
    bizScene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
    createTime?: string;
    id?: number;
    name?: string;
    speechType?:
      | 'BuyerImChat'
      | 'BuyerSendMsg'
      | 'Comment'
      | 'ImChat'
      | 'Invite'
      | 'LiveComment'
      | 'RequestAdCode'
      | 'SendMsg'
      | 'TargetPlan';
    teamId?: number;
  };

  type ghSpeechGroupListGetParams = {
    /** speechType */
    speechType?:
      | 'BuyerImChat'
      | 'BuyerSendMsg'
      | 'Comment'
      | 'ImChat'
      | 'Invite'
      | 'LiveComment'
      | 'RequestAdCode'
      | 'SendMsg'
      | 'TargetPlan';
    /** scene */
    scene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
  };

  type GhSpeechGroupVo = {
    bizScene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
    count?: number;
    createTime?: string;
    id?: number;
    name?: string;
    speechType?:
      | 'BuyerImChat'
      | 'BuyerSendMsg'
      | 'Comment'
      | 'ImChat'
      | 'Invite'
      | 'LiveComment'
      | 'RequestAdCode'
      | 'SendMsg'
      | 'TargetPlan';
    teamId?: number;
  };

  type ghSpeechPageGetParams = {
    /** content */
    content?: string;
    /** speechType */
    speechType?:
      | 'BuyerImChat'
      | 'BuyerSendMsg'
      | 'Comment'
      | 'ImChat'
      | 'Invite'
      | 'LiveComment'
      | 'RequestAdCode'
      | 'SendMsg'
      | 'TargetPlan';
    /** scene */
    scene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
    /** groupId */
    groupId?: number;
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** sortField */
    sortField?: string;
    /** sortOrder */
    sortOrder?: 'asc' | 'desc';
  };

  type ghSpeechSwitchSortNoPutParams = {
    /** groupId */
    groupId: number;
    /** speechId1 */
    speechId1: number;
    /** speechId2 */
    speechId2: number;
  };

  type ghSpeechUpdateSortNoPutParams = {
    /** groupId */
    groupId: number;
  };

  type ghStatRowsGetParams = {
    /** days */
    days?: number;
  };

  type GhStatRowVo = {
    /** 日期 */
    day?: string;
    stats?: Record<string, any>;
  };

  type ghSubTeamBySubTeamIdPutParams = {
    /** subTeamId */
    subTeamId: number;
  };

  type ghSubTeamBySubTeamIdValidPutParams = {
    /** subTeamId */
    subTeamId: number;
    /** valid */
    valid: boolean;
  };

  type ghSubTeamPageGetParams = {
    pageNum?: number;
    pageSize?: number;
    query?: string;
    sortField?: 'createTime' | 'subTeamId' | 'valid' | 'validTimeFrom' | 'validTimeTo';
    sortOrder?: 'asc' | 'desc';
    valid?: boolean;
  };

  type GhSyncNotFoundRequest = {
    handles?: string[];
  };

  type GhTagByHandleRequest = {
    bizScene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
    handles?: string[];
    tags?: string[];
  };

  type GhTagRequest = {
    ids?: number[];
    mobileAccount?: string;
    resourceType?:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    tag?: string;
  };

  type ghUserAvatarGetParams = {
    /** creatorId */
    creatorId: string;
  };

  type ghUserByCreatorIdGetParams = {
    /** creatorId */
    creatorId: number;
  };

  type ghUserCountGetParams = {
    alias?: string;
    /** 包含指定标签。取false时，tagLc只能是OR */
    containsTag?: boolean;
    createTimeFrom?: string;
    createTimeLastDays?: number;
    createTimeTo?: string;
    deleting?: boolean;
    filterByFavorite?: boolean;
    followerCntFrom?: number;
    followerCntTo?: number;
    gifterLevelFrom?: number;
    gifterLevelTo?: number;
    handle?: string;
    hasNewMsg?: boolean;
    hasNoRegion?: boolean;
    hasResponsibleUser?: boolean;
    lastSyncTimeFrom?: string;
    lastSyncTimeLastDays?: number;
    lastSyncTimeTo?: string;
    limit?: number;
    messageStatusList?: 'Everyone' | 'Friends' | 'None' | 'Recommands' | 'Unset';
    region?: string;
    regions?: string[];
    responsibleUserId?: number;
    statusList?: 'NotContacted' | 'Replied' | 'Sent' | 'SignUnsupported' | 'Signed';
    tagIds?: number[];
    /** 标签的逻辑条件，支持OR|AND */
    tagLc?: 'AND' | 'NOT' | 'OR';
    videoCntFrom?: number;
    videoCntTo?: number;
    videoLikesFrom?: number;
    videoLikesTo?: number;
  };

  type ghUserDeleteByQueryPostParams = {
    /** force */
    force?: boolean;
    /** deleteRemark */
    deleteRemark?: string;
  };

  type ghUserDeletePostParams = {
    /** force */
    force?: boolean;
    /** deleteRemark */
    deleteRemark?: string;
  };

  type GhUserDetailVo = {
    alias?: string;
    avatar?: string;
    backstageShopId?: number;
    createTime?: string;
    creatorId?: string;
    deleteRemark?: string;
    deleteTime?: string;
    deleting?: boolean;
    followerCnt?: number;
    gifterLevel?: number;
    handle?: string;
    hasNewMsg?: boolean;
    hyperlink?: string;
    id?: number;
    lastSyncTime?: string;
    messageStatus?: 'Everyone' | 'Friends' | 'None' | 'Recommands' | 'Unset';
    mobileAccountId?: number;
    officialShopId?: number;
    pmShopId?: number;
    region?: string;
    responsibleUser?: UserDto;
    responsibleUserId?: number;
    status?: 'NotContacted' | 'Replied' | 'Sent' | 'SignUnsupported' | 'Signed';
    /** 标签 */
    tags?: TagDto[];
    teamId?: number;
    videoCnt?: number;
    videoLikes?: number;
  };

  type GhUserDocument = {
    alias?: string;
    avatar?: string;
    categories?: string[];
    creatorId?: string;
    followerCnt?: number;
    /** 送礼者等级 */
    gifterLevel?: number;
    handle?: string;
    messageStatus?: 'Everyone' | 'Friends' | 'None' | 'Recommands' | 'Unset';
    region?: string;
    videoCnt?: number;
    videoLikes?: number;
  };

  type GhUserDocumentBatch = {
    creators?: GhUserDocument[];
  };

  type GhUserDto = {
    alias?: string;
    avatar?: string;
    backstageShopId?: number;
    createTime?: string;
    creatorId?: string;
    deleteRemark?: string;
    deleteTime?: string;
    deleting?: boolean;
    followerCnt?: number;
    gifterLevel?: number;
    handle?: string;
    hasNewMsg?: boolean;
    id?: number;
    lastSyncTime?: string;
    messageStatus?: 'Everyone' | 'Friends' | 'None' | 'Recommands' | 'Unset';
    mobileAccountId?: number;
    officialShopId?: number;
    pmShopId?: number;
    region?: string;
    responsibleUserId?: number;
    status?: 'NotContacted' | 'Replied' | 'Sent' | 'SignUnsupported' | 'Signed';
    teamId?: number;
    videoCnt?: number;
    videoLikes?: number;
  };

  type ghUserPageGetParams = {
    alias?: string;
    /** 包含指定标签。取false时，tagLc只能是OR */
    containsTag?: boolean;
    createTimeFrom?: string;
    createTimeLastDays?: number;
    createTimeTo?: string;
    deleting?: boolean;
    filterByFavorite?: boolean;
    followerCntFrom?: number;
    followerCntTo?: number;
    gifterLevelFrom?: number;
    gifterLevelTo?: number;
    handle?: string;
    hasNewMsg?: boolean;
    hasNoRegion?: boolean;
    hasResponsibleUser?: boolean;
    lastSyncTimeFrom?: string;
    lastSyncTimeLastDays?: number;
    lastSyncTimeTo?: string;
    limit?: number;
    messageStatusList?: 'Everyone' | 'Friends' | 'None' | 'Recommands' | 'Unset';
    pageNum?: number;
    pageSize?: number;
    region?: string;
    regions?: string[];
    responsibleUserId?: number;
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
    statusList?: 'NotContacted' | 'Replied' | 'Sent' | 'SignUnsupported' | 'Signed';
    tagIds?: number[];
    /** 标签的逻辑条件，支持OR|AND */
    tagLc?: 'AND' | 'NOT' | 'OR';
    videoCntFrom?: number;
    videoCntTo?: number;
    videoLikesFrom?: number;
    videoLikesTo?: number;
  };

  type ghUserPageResponsibleGetParams = {
    alias?: string;
    /** 包含指定标签。取false时，tagLc只能是OR */
    containsTag?: boolean;
    createTimeFrom?: string;
    createTimeLastDays?: number;
    createTimeTo?: string;
    deleting?: boolean;
    filterByFavorite?: boolean;
    followerCntFrom?: number;
    followerCntTo?: number;
    gifterLevelFrom?: number;
    gifterLevelTo?: number;
    handle?: string;
    hasNewMsg?: boolean;
    hasNoRegion?: boolean;
    hasResponsibleUser?: boolean;
    lastSyncTimeFrom?: string;
    lastSyncTimeLastDays?: number;
    lastSyncTimeTo?: string;
    limit?: number;
    messageStatusList?: 'Everyone' | 'Friends' | 'None' | 'Recommands' | 'Unset';
    pageNum?: number;
    pageSize?: number;
    region?: string;
    regions?: string[];
    responsibleUserId?: number;
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
    statusList?: 'NotContacted' | 'Replied' | 'Sent' | 'SignUnsupported' | 'Signed';
    tagIds?: number[];
    /** 标签的逻辑条件，支持OR|AND */
    tagLc?: 'AND' | 'NOT' | 'OR';
    videoCntFrom?: number;
    videoCntTo?: number;
    videoLikesFrom?: number;
    videoLikesTo?: number;
  };

  type ghV2DownloadByQueryByTokenGetParams = {
    /** token */
    token: string;
  };

  type ghV2DownloadByTokenGetParams = {
    /** token */
    token: string;
  };

  type ghV2ExportHandleByQueryByTokenGetParams = {
    /** token */
    token: string;
    /** prefix */
    prefix?: string;
    /** separator */
    separator: string;
  };

  type ghV2ExportHandleByTokenGetParams = {
    /** token */
    token: string;
    /** prefix */
    prefix?: string;
    /** separator */
    separator: string;
  };

  type ghVideoCreatorAvatarGetParams = {
    /** creatorId */
    creatorId: string;
  };

  type ghVideoCreatorByCreatorIdGetParams = {
    /** creatorId */
    creatorId: number;
  };

  type ghVideoCreatorCountGetParams = {
    alias?: string;
    /** 包含指定标签。取false时，tagLc只能是OR */
    containsTag?: boolean;
    createTimeFrom?: string;
    createTimeLastDays?: number;
    createTimeTo?: string;
    deleting?: boolean;
    filterByFavorite?: boolean;
    followerCntFrom?: number;
    followerCntTo?: number;
    handle?: string;
    hasNewMsg?: boolean;
    hasNoRegion?: boolean;
    hasResponsibleUser?: boolean;
    lastSyncTimeFrom?: string;
    lastSyncTimeLastDays?: number;
    lastSyncTimeTo?: string;
    limit?: number;
    messageStatusList?: 'Everyone' | 'Friends' | 'None' | 'Recommands' | 'Unset';
    pageNum?: number;
    pageSize?: number;
    region?: string;
    regions?: string[];
    responsibleUserId?: number;
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
    statusList?: 'NotContacted' | 'Replied' | 'Sent' | 'SignUnsupported' | 'Signed';
    tagIds?: number[];
    /** 标签的逻辑条件，支持OR|AND */
    tagLc?: 'AND' | 'NOT' | 'OR';
    videoCntFrom?: number;
    videoCntTo?: number;
    videoLikesFrom?: number;
    videoLikesTo?: number;
  };

  type ghVideoCreatorDeleteByQueryPostParams = {
    /** force */
    force?: boolean;
    /** deleteRemark */
    deleteRemark?: string;
  };

  type ghVideoCreatorDeletePostParams = {
    /** force */
    force?: boolean;
    /** deleteRemark */
    deleteRemark?: string;
  };

  type GhVideoCreatorDetailVo = {
    alias?: string;
    avatar?: string;
    backstageShopId?: number;
    createTime?: string;
    creatorId?: string;
    deleteRemark?: string;
    deleteTime?: string;
    deleting?: boolean;
    followerCnt?: number;
    handle?: string;
    hasNewMsg?: boolean;
    hyperlink?: string;
    id?: number;
    lastSyncTime?: string;
    messageStatus?: 'Everyone' | 'Friends' | 'None' | 'Recommands' | 'Unset';
    mobileAccountId?: number;
    officialShopId?: number;
    pmShopId?: number;
    region?: string;
    responsibleUser?: UserDto;
    responsibleUserId?: number;
    status?: 'NotContacted' | 'Replied' | 'Sent' | 'SignUnsupported' | 'Signed';
    /** 标签 */
    tags?: TagDto[];
    teamId?: number;
    videoCnt?: number;
    videoLikes?: number;
  };

  type GhVideoCreatorDocument = {
    alias?: string;
    avatar?: string;
    categories?: string[];
    creatorId?: string;
    followerCnt?: number;
    handle?: string;
    messageStatus?: 'Everyone' | 'Friends' | 'None' | 'Recommands' | 'Unset';
    region?: string;
    videoCnt?: number;
    videoLikes?: number;
  };

  type GhVideoCreatorDocumentBatch = {
    creators?: GhVideoCreatorDocument[];
  };

  type ghVideoCreatorPageGetParams = {
    alias?: string;
    /** 包含指定标签。取false时，tagLc只能是OR */
    containsTag?: boolean;
    createTimeFrom?: string;
    createTimeLastDays?: number;
    createTimeTo?: string;
    deleting?: boolean;
    filterByFavorite?: boolean;
    followerCntFrom?: number;
    followerCntTo?: number;
    handle?: string;
    hasNewMsg?: boolean;
    hasNoRegion?: boolean;
    hasResponsibleUser?: boolean;
    lastSyncTimeFrom?: string;
    lastSyncTimeLastDays?: number;
    lastSyncTimeTo?: string;
    limit?: number;
    messageStatusList?: 'Everyone' | 'Friends' | 'None' | 'Recommands' | 'Unset';
    pageNum?: number;
    pageSize?: number;
    region?: string;
    regions?: string[];
    responsibleUserId?: number;
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
    statusList?: 'NotContacted' | 'Replied' | 'Sent' | 'SignUnsupported' | 'Signed';
    tagIds?: number[];
    /** 标签的逻辑条件，支持OR|AND */
    tagLc?: 'AND' | 'NOT' | 'OR';
    videoCntFrom?: number;
    videoCntTo?: number;
    videoLikesFrom?: number;
    videoLikesTo?: number;
  };

  type ghVideoCreatorPageResponsibleGetParams = {
    alias?: string;
    /** 包含指定标签。取false时，tagLc只能是OR */
    containsTag?: boolean;
    createTimeFrom?: string;
    createTimeLastDays?: number;
    createTimeTo?: string;
    deleting?: boolean;
    filterByFavorite?: boolean;
    followerCntFrom?: number;
    followerCntTo?: number;
    handle?: string;
    hasNewMsg?: boolean;
    hasNoRegion?: boolean;
    hasResponsibleUser?: boolean;
    lastSyncTimeFrom?: string;
    lastSyncTimeLastDays?: number;
    lastSyncTimeTo?: string;
    limit?: number;
    messageStatusList?: 'Everyone' | 'Friends' | 'None' | 'Recommands' | 'Unset';
    pageNum?: number;
    pageSize?: number;
    region?: string;
    regions?: string[];
    responsibleUserId?: number;
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
    statusList?: 'NotContacted' | 'Replied' | 'Sent' | 'SignUnsupported' | 'Signed';
    tagIds?: number[];
    /** 标签的逻辑条件，支持OR|AND */
    tagLc?: 'AND' | 'NOT' | 'OR';
    videoCntFrom?: number;
    videoCntTo?: number;
    videoLikesFrom?: number;
    videoLikesTo?: number;
  };

  type HandleCreatorIdVo = {
    creatorId?: string;
    handle?: string;
  };

  type HandleRegionBatchRequest = {
    creators?: HandleRegionVo[];
  };

  type HandleRegionVo = {
    handle?: string;
    region?: string;
  };

  type HasGhInteractionRequest = {
    handlers?: string[];
    interactTimeFrom?: number;
    interactTimeTo?: number;
    interactType?: 'ImportCreator' | 'Invite' | 'SendMsg' | 'SyncMsg' | 'UpdateInfo';
    shopId?: number;
  };

  type HasInteractionVo = {
    count?: number;
    handle?: string;
  };

  type HealthCheckConfig = {
    /** 其它的流程关心但引擎不关心的属性 */
    advanceSettings?: Record<string, any>;
    disabled?: boolean;
    /** 健康检查工作时间段，格式如 03:22-15:34 */
    duration?: string;
    /** first | random | assign  为空表示random */
    friendType?: string;
    /** 健康检查频率，为空表示不检查；最小10分钟，最大1000分钟 */
    interval?: number;
    /** 调度quartz id */
    scheduleId?: string;
  };

  type IdNameVo = {
    id?: number;
    name?: string;
  };

  type JobCreatingInfo = {
    /** 任务池当前已有任务 */
    currentAliveCount?: number;
    /** 任务池可容纳的最大任务数量 */
    maxAliveJobsCount?: number;
    /** 本次操作拟创建任务 */
    plannedJobsCount?: number;
    /** 实际创建任务 */
    reallyJobsCount?: number;
    /** 当前状态 creating | done  */
    status?: string;
  };

  type KolAvailableVo = {
    creatorId?: string;
    handle?: string;
    region?: string;
  };

  type KolHandleRegionVo = {
    handle?: string;
    region?: string;
  };

  type KolLiveDto = {
    createTime?: string;
    creatorId?: number;
    gameRank?: number;
    handle?: string;
    hourRevenue?: number;
    id?: number;
    likes?: number;
    liveId?: string;
    popularRank?: number;
    region?: string;
    revenue?: number;
    viewers?: number;
    weekRevenue?: number;
  };

  type KolRegionMapDto = {
    id?: string;
    region?: string;
  };

  type KolSubTeamDetailVo = {
    /** 每日采集主播数量 */
    allocateTotal?: number;
    createTime?: string;
    id?: number;
    parentTeamId?: number;
    subTeam?: TeamDto;
    subTeamAdmin?: UserBriefVo;
    subTeamId?: number;
    /** 用户数量 */
    userQuota?: number;
    valid?: boolean;
    validTimeFrom?: string;
    validTimeTo?: string;
  };

  type KolSubTeamDto = {
    createTime?: string;
    id?: number;
    parentTeamId?: number;
    subTeamId?: number;
    valid?: boolean;
    validTimeFrom?: string;
    validTimeTo?: string;
  };

  type KolSubTeamVo = {
    /** 每日采集主播数量 */
    allocateTotal?: number;
    createTime?: string;
    id?: number;
    parentTeamId?: number;
    subTeamId?: number;
    /** 用户数量 */
    userQuota?: number;
    valid?: boolean;
    validTimeFrom?: string;
    validTimeTo?: string;
  };

  type KolTagRequest = {
    creators?: HandleRegionVo[];
    tag?: string;
  };

  type KolUntagBySuffixRequest = {
    creators?: HandleRegionVo[];
    tagSuffixList?: string[];
  };

  type KolUpdateSubTeamRequest = {
    /** 每日采集主播数量 */
    allocateTotal?: number;
    /** 用户数量 */
    userQuota?: number;
    validTimeFrom?: string;
    validTimeTo?: string;
  };

  type KolUserInfo = {
    creator?: GhCreatorInterface;
    scene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
  };

  type LoginDeviceDto = {
    appId?: string;
    appVersion?: string;
    clientIp?: string;
    clientLocation?: number;
    cpus?: number;
    createTime?: string;
    deviceId?: string;
    deviceType?: 'App' | 'Browser' | 'Extension' | 'HYRuntime' | 'RpaExecutor';
    domestic?: boolean;
    hostName?: string;
    id?: number;
    ipDataId?: number;
    lastActiveTime?: string;
    lastCity?: string;
    lastLogTime?: string;
    lastRemoteIp?: string;
    lastUserId?: number;
    logUrl?: string;
    mem?: number;
    online?: boolean;
    osName?: string;
    teamId?: number;
    userAgent?: string;
    version?: string;
  };

  type MemberVo = {
    /** 账号 */
    account?: string;
    avatar?: string;
    createTime?: string;
    /** 邮箱 */
    email?: string;
    gender?: 'FEMALE' | 'MALE' | 'UNSPECIFIC';
    id?: number;
    nickname?: string;
    partnerId?: number;
    /** 手机 */
    phone?: string;
    residentCity?: string;
    roleCode?: 'boss' | 'manager' | 'staff' | 'superadmin';
    signature?: string;
    status?: 'ACTIVE' | 'BLOCK' | 'DELETED' | 'INACTIVE';
    teamNickname?: string;
    tenant?: number;
    userType?: 'NORMAL' | 'PARTNER' | 'SHADOW';
    weixin?: string;
  };

  type MobileChoicePolicy = {
    /** 使用的手机账号：reuse 使用最近使用的 | designated 指定手机 | reset 强制重新分配 */
    choiceType?: string;
    /** 手机账号分配原则：除了发送私信之外的标签，为空说明只需要发送私信标签 */
    extraAccountTag?: string;
    /** 指定的手机账号，当 choiceType=designated 时，具体使用哪一个手机账号 */
    mobileAccountId?: number;
  };

  type NotifyNewTkMessageRequest = {
    accounts?: string[];
    handles?: string[];
    mobileId?: number;
    tags?: string[];
  };

  type NotifyScheduleVo = {
    times?: string[];
    userIds?: number[];
  };

  type PageGhSessionRequest = {
    handle?: string;
    lastPushTimeBeforeDays?: number;
    lastPushTimeFrom?: string;
    lastPushTimeLastDays?: number;
    lastPushTimeTo?: string;
    lastReplyTimeBeforeDays?: number;
    lastReplyTimeFrom?: string;
    lastReplyTimeLastDays?: number;
    lastReplyTimeTo?: string;
    pageNum?: number;
    pageSize?: number;
    receiveUserId?: number;
    replyUserId?: number;
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
    statusList?: ('Error' | 'NeedReply' | 'Replied' | 'Replying')[];
  };

  type PageResultGhCreatorDetailVo = {
    current?: number;
    list?: GhCreatorDetailVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultGhGifterDetailVo = {
    current?: number;
    list?: GhGifterDetailVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultGhInteractionDetailVo = {
    current?: number;
    list?: GhInteractionDetailVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultGhJobDto = {
    current?: number;
    list?: GhJobDto[];
    pageSize?: number;
    total?: number;
  };

  type PageResultGhLiveCreatorDetailVo = {
    current?: number;
    list?: GhLiveCreatorDetailVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultGhMessageDto = {
    current?: number;
    list?: GhMessageDto[];
    pageSize?: number;
    total?: number;
  };

  type PageResultGhSessionVo = {
    current?: number;
    list?: GhSessionVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultGhSpeechDto = {
    current?: number;
    list?: GhSpeechDto[];
    pageSize?: number;
    total?: number;
  };

  type PageResultGhUserDetailVo = {
    current?: number;
    list?: GhUserDetailVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultGhVideoCreatorDetailVo = {
    current?: number;
    list?: GhVideoCreatorDetailVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultKolLiveDto = {
    current?: number;
    list?: KolLiveDto[];
    pageSize?: number;
    total?: number;
  };

  type PageResultKolSubTeamDetailVo = {
    current?: number;
    list?: KolSubTeamDetailVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultstring = {
    current?: number;
    list?: string[];
    pageSize?: number;
    total?: number;
  };

  type ReportGhJobRequest = {
    jobId?: number;
    message?: string;
    rpaTaskId?: number;
    /** 汇报的时候允许指定状态。如果未指定将根据 success 来设置为成功或失败。 Succeed | Failed | Cancelled  */
    status?: 'Cancelled' | 'Failed' | 'Pending' | 'Running' | 'Scheduled' | 'Succeed';
    success?: boolean;
    /** 是否解绑达人和分身，目前只有在公会后台邀约建联时有意义 */
    unbindCreatorShop?: boolean;
  };

  type Resource = true;

  type RpaFlowDto = {
    attachmentSize?: number;
    bizCode?: string;
    configId?: string;
    console?: boolean;
    createTime?: string;
    createType?: 'FileCopy' | 'Manual' | 'Market' | 'MarketCopy' | 'Shared' | 'TkPack' | 'Tkshop';
    creatorId?: number;
    description?: string;
    dirty?: boolean;
    execCount?: number;
    expireTime?: string;
    flowShareCode?: string;
    groupId?: number;
    id?: number;
    lastExecTime?: string;
    marketId?: number;
    name?: string;
    nameBrief?: string;
    numberVersion?: number;
    packId?: number;
    publishTime?: string;
    rpaType?: 'Browser' | 'Extension' | 'IOS' | 'Mobile';
    sessionInner?: boolean;
    sharedFlowId?: number;
    shopId?: number;
    sortNo?: number;
    status?: 'Draft' | 'Published';
    supportConcurrent?: boolean;
    teamId?: number;
    tkFlowId?: number;
    updateTime?: string;
    version?: string;
  };

  type RpaTaskDto = {
    clientId?: string;
    clientIp?: string;
    cloudInstanceId?: number;
    concurrent?: number;
    configId?: string;
    console?: boolean;
    createTime?: string;
    creatorId?: number;
    creditDetailId?: number;
    description?: string;
    deviceName?: string;
    errorCode?: number;
    errorMsg?: string;
    executeEndTime?: string;
    executeTime?: string;
    extra?: string;
    fileLocked?: boolean;
    fileStatus?: 'Deleted' | 'Undefined' | 'Valid';
    flowId?: number;
    flowName?: string;
    forceRecord?: boolean;
    heartbeatTime?: string;
    hyRuntimeId?: number;
    id?: number;
    manualRun?: boolean;
    name?: string;
    planId?: number;
    planName?: string;
    preview?: boolean;
    provider?:
      | 'aliyun'
      | 'aws'
      | 'aws_cn'
      | 'aws_ls'
      | 'azure'
      | 'azure_cn'
      | 'baidu'
      | 'baoliannet'
      | 'bluevps'
      | 'dmit'
      | 'ecloud10086'
      | 'googlecloud'
      | 'huawei'
      | 'huayang'
      | 'huoshan'
      | 'jdbox'
      | 'jdcloud'
      | 'jdeip'
      | 'lan'
      | 'oracle'
      | 'other'
      | 'qcloud'
      | 'raincloud'
      | 'ucloud'
      | 'vlcloud'
      | 'vps'
      | 'ygeip';
    region?: string;
    rpaVoucherId?: number;
    runOnCloud?: boolean;
    snapshot?: 'Node' | 'Not' | 'OnFail';
    status?:
      | 'Cancelled'
      | 'CreateFailed'
      | 'Ended'
      | 'Ended_All_Failed'
      | 'Ended_Partial_Failed'
      | 'Ignored'
      | 'NotStart'
      | 'Running'
      | 'ScheduleCancelled'
      | 'Scheduled'
      | 'Scheduling'
      | 'UnusualEnded';
    teamId?: number;
  };

  type SendInvitePMRequest = {
    /** 邀约高级设置 */
    advanceSettings?: Record<string, any>;
    /** 是什么样类型的达人，为空表示 LiveCreator  */
    bizScene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
    /** 邀约时评论要使用的话术列表，会随机从中选择发送。只在 ghJobType 为 SendInvite 时有意义 */
    commentWordIds?: number[];
    /** 拟发送的达人列表 */
    creatorIds?: number[];
    deleteMsg?: boolean;
    /** 公会平台类型 */
    ghPlatformType?:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
    /** 拟使用的TK账号类型，默认值 Normal */
    ghScheduleType?: 'GhBackstage' | 'Mobile' | 'Normal' | 'Official' | 'Union' | 'Unknown';
    /** 发送邀约手机账号选择策略，仅在 ghScheduleType == Mobile 的时候有意义 */
    mobileChoicePolicy?: MobileChoicePolicy;
    /** 发送私信前关注主播并将私信内容评论到主播的第一条视频 */
    sendComment?: boolean;
    /** 是否增加随机表情 */
    sendEmoji?: boolean;
    /** 是否发送邀约卡片(只有公会后台账号才支持此特性） */
    sendInviteCard?: boolean;
    /** 发送邀约分身选择策略 */
    shopChoicePolicy?: ShopChoicePolicy;
    /** 已分配/已认领的达人不再发送 (为空表示 true ) */
    skipResponsible?: boolean;
    /** 邀约要使用的话术列表，会随机从中选择发送 */
    wordsIds?: number[];
  };

  type SendKakaoMessageRequest = {
    /** 给指定的kakao聊天记录发消息，只有当#friendId为空时才有意义 */
    chatId?: number;
    content?: string;
    /** 给指定的kakao联系人发消息 */
    friendId?: number;
  };

  type SendReplyPMRequest = {
    /** 邀约高级设置 */
    advanceSettings?: Record<string, any>;
    /** 是什么样类型的达人，为空表示 LiveCreator  */
    bizScene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
    /** 自定义输入内容，如果不为空，wordIds会被忽略 */
    content?: string;
    ghCreatorId?: number;
    /** 公会平台类型 */
    ghPlatformType?:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
    /** 拟使用的TK账号类型，默认值 Normal */
    ghScheduleType?: 'GhBackstage' | 'Mobile' | 'Normal' | 'Official' | 'Union' | 'Unknown';
    /** 发送邀约手机账号选择策略，仅在 ghScheduleType == Mobile 的时候有意义 */
    mobileChoicePolicy?: MobileChoicePolicy;
    /** 邀约要使用的话术列表，会随机从中选择发送 */
    wordsIds?: number[];
  };

  type SendUserCardRequest = {
    /** 发送卡片高级设置 */
    advanceSettings?: Record<string, any>;
    /** 是什么样类型的达人，为空表示 User  */
    bizScene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
    /** 拟发送的达人列表 */
    creatorIds?: number[];
    /** 公会平台类型 */
    ghPlatformType?:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
    mobileAccountId?: number;
    /** 接收者handle，会原样转给流程 */
    receiverHandle?: string;
  };

  type ShareVideoRequest = {
    /** 邀约高级设置 */
    advanceSettings?: Record<string, any>;
    /** 是什么样类型的达人，为空表示 LiveCreator  */
    bizScene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
    /** 拟发送的达人列表 */
    creatorIds?: number[];
    /** 公会平台类型 */
    ghPlatformType?:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
    /** 拟使用的TK账号类型，默认值 Normal */
    ghScheduleType?: 'GhBackstage' | 'Mobile' | 'Normal' | 'Official' | 'Union' | 'Unknown';
    /** 发送邀约手机账号选择策略，仅在 ghScheduleType == Mobile 的时候有意义 */
    mobileChoicePolicy?: MobileChoicePolicy;
    /** 邀约要使用的话术列表，会随机从中选择发送 */
    wordsIds?: number[];
  };

  type ShopChoicePolicy = {
    /** 如果达人没有分配过对应类型的分身，shopId不为空表示强制使用该分身，否则从打了相应tag的分身中随机选择 */
    shopId?: number;
  };

  type ShopDto = {
    /** 账户账号 */
    account?: string;
    /** 是否允许用户自行安装插件 */
    allowExtension?: boolean;
    /** 会话是否允许监视 */
    allowMonitor?: boolean;
    /** 是否允许跳过敏感操作 */
    allowSkip?: boolean;
    /** 是否自动代填 */
    autoFill?: boolean;
    bookmarkBar?: string;
    coordinateId?: string;
    /** 创建时间 */
    createTime?: string;
    /** 创建者 */
    creatorId?: number;
    deleteTime?: string;
    deleting?: boolean;
    /** 描述 */
    description?: string;
    domainPolicy?: 'Blacklist' | 'None' | 'Whitelist';
    /** 是否独占访问 */
    exclusive?: boolean;
    extension?: 'both' | 'extension' | 'huayang';
    extraProp?: string;
    /** 绑定的指纹Id */
    fingerprintId?: number;
    /** 绑定的指纹模板Id（只有无状态账号才允许绑定指纹模板） */
    fingerprintTemplateId?: number;
    frontUrl?: string;
    googleTranslateSpeed?: boolean;
    homePage?: string;
    homePageSites?: string;
    /** id */
    id?: number;
    imageForbiddenSize?: number;
    intranetEnabled?: boolean;
    ipSwitchCheckInterval?: number;
    ipSwitchStrategy?: 'Abort' | 'Alert' | 'Off';
    lastAccessTime?: string;
    lastAccessUser?: number;
    lastSyncTime?: string;
    loginStatus?: 'Offline' | 'Online' | 'Unknown';
    /** 任务栏分身标记文字 */
    markCode?: string;
    /** 任务栏分身标记背景颜色 */
    markCodeBg?: number;
    monitorPerception?: boolean;
    /** 账户名称 */
    name?: string;
    nameBookmarkEnabled?: boolean;
    operateStatus?: 'shared' | 'sharing' | 'sole' | 'transferring';
    /** 经营品类 */
    operatingCategory?:
      | '医药保健'
      | '图书文具'
      | '宠物用品'
      | '家具建材'
      | '家电电器'
      | '工业用品'
      | '户外运动'
      | '手机数码'
      | '手表眼镜'
      | '护肤美妆'
      | '母婴玩具'
      | '汽车配件'
      | '生活家居'
      | '电商其他'
      | '电脑平板'
      | '艺术珠宝'
      | '花园聚会'
      | '计生情趣'
      | '软件程序'
      | '鞋服箱包'
      | '音乐影视'
      | '食品生鲜'
      | '鲜花绿植';
    parentShopId?: number;
    /** 密码 */
    password?: string;
    /** 平台ID */
    platformId?: number;
    privateAddress?: string;
    privateTitle?: string;
    recordPerception?: boolean;
    recordPolicy?: 'Chosen' | 'Disabled' | 'Forced';
    requireIp?: boolean;
    resourcePolicy?: number;
    securityPolicyEnabled?: boolean;
    securityPolicyUpdateTime?: string;
    sharePolicyId?: string;
    shopDataSize?: number;
    shopNo?: string;
    stateless?: boolean;
    statelessChangeFp?: boolean;
    statelessSyncPolicy?: number;
    syncPolicy?: number;
    /** 团队ID */
    teamId?: number;
    trafficAlertStrategy?: 'Abort' | 'Off';
    trafficAlertThreshold?: number;
    trafficSaving?: boolean;
    type?: 'Global' | 'Local' | 'None';
    webSecurity?: boolean;
  };

  type shoujixiaoxijianchapeizhi = {
    disabled?: boolean;
    /** 消息检查工作时间段，格式如 03:22-15:34 */
    duration?: string;
    /** 消息同步频率，单位分钟，为空表示不同步；最小10分钟，最大1000分钟 */
    interval?: number;
    /** 以逗号分隔开的用户id列表，表示如果有新消息要通知哪些用户 */
    receiveIds?: string;
    /** 调度quartz id */
    scheduleId?: string;
  };

  type shoujiyaoyuepeizhi = {
    /** 单账号每批发卡片间隔，单位分钟 */
    taskCardInterval?: number;
    /** 单账号每批私信间隔，单位分钟 */
    taskPmInterval?: number;
  };

  type SimpleGhCreatorVo = {
    alias?: string;
    handle?: string;
    id?: number;
  };

  type SimpleShopVo = {
    id?: number;
    name?: string;
  };

  type StartWatchLiveRewardsRequest = {
    /** 其它的流程关心但引擎不关心的属性。引擎会关注 (minViewers: number = 10, maxViewers: number = 20) 两个参数 */
    advanceSettings?: Record<string, any>;
    /** 公会平台类型 */
    ghPlatformType?:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
    /** 区分是浏览器还是手机，非Mobile一律认为是浏览器 */
    ghScheduleType?: 'GhBackstage' | 'Mobile' | 'Normal' | 'Official' | 'Union' | 'Unknown';
    /** 如果未分配打赏的店铺/手机账号，通过这个标签去查找 */
    rewardTag?: string;
    /** 如果已经打赏过的策略 */
    rewardedPolicy?: 'change' | 'ignore' | 'reuse';
    /** 直播间发言话术，会随机从中选择发送 */
    wordsIds?: number[];
  };

  type SyncBatchResult = {
    /** 新创建个数 */
    created?: number;
    /** 总数 */
    total?: number;
    /** 更新个数 */
    updated?: number;
  };

  type SyncCreatorAvatarRequest = {
    /** 头像数据base64编码 */
    avatarImageBase64?: string;
    /** 头像扩展名 */
    avatarImageExt?: string;
    creatorId?: string;
    handle?: string;
    region?: string;
  };

  type SyncCreatorConfig = {
    /** 单分身每次最多更新多少个主播 */
    maxShopTaskSync?: number;
    /** 信息更新账号所用标签 */
    shopTag?: string;
  };

  type SyncGhLiveGiftRequest = {
    liveGifts?: GhLiveGiftDocument[];
  };

  type SyncKaokaoContactsToPhoneRequest = {
    /** 要同步到手机的联系人列表 */
    friendIds?: number[];
  };

  type SyncMessageResult = {
    /** 新消息数量 */
    newMessageCount?: number;
    /** 新打的标签 */
    tag?: string;
  };

  type SyncPMRequest = {
    /** 是什么样类型的达人，为空表示 LiveCreator  */
    bizScene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
    /** 打算更新PM信息的达人列表 */
    creatorIds?: number[];
    /** 同步时是否同步删除与主播的聊天记录，默认为true */
    deleteSession?: boolean;
    /** 公会平台类型 */
    ghPlatformType?:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
    /** 拟使用的TK账号类型，默认值 Normal */
    ghScheduleType?: 'GhBackstage' | 'Mobile' | 'Normal' | 'Official' | 'Union' | 'Unknown';
  };

  type TagCreatorRequest = {
    handles?: string[];
    tags?: string[];
  };

  type TagDto = {
    bizCode?: string;
    color?: number;
    createTime?: string;
    expireTime?: string;
    id?: number;
    resourceType?:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    system?: boolean;
    tag?: string;
    teamId?: number;
  };

  type TagTkCreatorByQueryRequest = {
    query?: Record<string, any>;
    scene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
    /** 打标签或清空标签 */
    tag?: boolean;
    tagIds?: number[];
    teamId?: number;
    userId?: number;
  };

  type TeamDto = {
    avatar?: string;
    createTime?: string;
    creatorId?: number;
    deleteTime?: string;
    domesticCloudEnabled?: boolean;
    id?: number;
    invalidTime?: string;
    inviteCode?: number;
    name?: string;
    overseaCloudEnabled?: boolean;
    paid?: boolean;
    partnerId?: number;
    payTime?: string;
    repurchaseTime?: string;
    repurchased?: boolean;
    status?: 'Blocked' | 'Deleted' | 'Pending' | 'Ready';
    teamType?: 'crs' | 'gh' | 'krShop' | 'normal' | 'partner' | 'plugin' | 'tk' | 'tkshop';
    tenantId?: number;
    testing?: boolean;
    validateTime?: string;
    validated?: boolean;
    verified?: boolean;
  };

  type TeamKolConfig = {
    agents?: number;
    allocateInterval?: number;
    /** 流水过滤字段 */
    allocateRevenue?: 'max_hour_revenue' | 'max_revenue' | 'max_week_revenue' | 'revenue';
    allocateTotal?: number;
    /** 允许用户设置主播缓存时长 */
    allowUserSetKeepDays?: boolean;
    area?: string;
    commerceUser?: boolean;
    countries?: string[];
    creatorTypes?: string[];
    deleteRemarks?: string[];
    filterTotal?: number;
    invalidJobId?: string;
    invalidateTime?: string;
    keepDays?: number;
    keepStatusList?: ('NotContacted' | 'Replied' | 'Sent' | 'SignUnsupported' | 'Signed')[];
    /** 标签保留时长 */
    keepTagDays?: number;
    liveHours?: number;
    maxAliveJobsCount?: number;
    maxFollowerCnt?: number;
    maxRevenue?: number;
    minFollowerCnt?: number;
    minRevenue?: number;
    platformModules?: string[];
    platformTypes?: (
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop'
    )[];
    regionSwitchQuota?: number;
    remarks?: string;
    subTeamCount?: number;
    ttSeller?: boolean;
    validEndTime?: string;
    xhsConfig?: XiaohongshuConfig;
  };

  type TeamMobileVo = {
    /** 当前手机是否允许使用快照功能 */
    allowBackup?: boolean;
    /** 系统版本，由于历史原因取名叫androidVersion  */
    androidVersion?: string;
    /** 当手机为云手机时才有值 */
    cloudInfo?: CloudMobileInfo;
    /** adb devices -l 获取到的设备标识 */
    code?: string;
    connectType?: 'ARMCLOUD' | 'Baidu' | 'QCloud' | 'USB' | 'WIFI';
    /** 导入时间 */
    createTime?: string;
    /** 创建者id */
    creatorId?: number;
    description?: string;
    /** 所在设备的id，关联login_device.device_id */
    deviceId?: string;
    /** 所在设备的当前是否在线 */
    deviceOnline?: boolean;
    /** 授权的部门 */
    grantDepartmentList?: DepartmentDto[];
    /** 授权给的用户 */
    grantUserVoList?: MemberVo[];
    /** 手机账号可用性检查配置 */
    healthCheckConfig?: HealthCheckConfig;
    id?: number;
    /** 手机邀约配置，如设置每手机每批次间隔 */
    intervalConfig?: shoujiyaoyuepeizhi;
    /** 手机新消息检查配置 */
    messageCheckConfig?: shoujixiaoxijianchapeizhi;
    /** 存手机当前的geo, sim等信息 */
    metas?: Record<string, any>;
    /** adb shell getprop ro.product.model */
    mode?: string;
    /** 手机设备名字，例如 MI 14 */
    name?: string;
    operateStatus?: 'shared' | 'sharing' | 'sole' | 'transferring';
    /** 从哪台手机联营而来 */
    parentMobileId?: number;
    /** android | ios */
    platform?: 'Android' | 'IOS';
    /** 是否暂时禁用rpa执行 */
    rpaSilent?: boolean;
    screenHeight?: number;
    screenWidth?: number;
    status?: 'EXPIRED' | 'OFFLINE' | 'ONLINE' | 'RESETING';
    /** 标签，只有查询的时候声明 fetchTags 才会有 */
    tags?: TagDto[];
    /** 所属团队 */
    teamId?: number;
    workTimeConfig?: WorkTimeConfig;
  };

  type TkCreatorByQueryRequest = {
    query?: Record<string, any>;
    scene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
    teamId?: number;
    userId?: number;
  };

  type TkCreatorFiledVo = {
    /** 字段描述（可能为null） */
    desc?: string;
    filedType?:
      | 'CreatorColumn'
      | 'CreatorContact'
      | 'CreatorProp'
      | 'CreatorStat'
      | 'ProductColumn';
    /** 方案必须包含 */
    force?: boolean;
    /** 字段Key */
    key?: string;
    /** 字段名称 */
    label?: string;
    path?: string;
  };

  type TkInteractionDetailVo = {
    creatorId?: number;
    description?: string;
    extraInfo?: Record<string, any>;
    flowId?: number;
    id?: number;
    interactTime?: number;
    interactType?:
      | 'Chat'
      | 'Email'
      | 'FetchCreator'
      | 'ImChat'
      | 'ImportCreator'
      | 'Ranking'
      | 'SampleRequest'
      | 'SampleRequestApproved'
      | 'TargetPlan'
      | 'UpdateCreator';
    interactionId?: string;
    operator?: UserDto;
    operatorId?: number;
    rpaFlow?: RpaFlowDto;
    rpaTask?: RpaTaskDto;
    shop?: ShopDto;
    shopId?: number;
    success?: boolean;
    taskId?: number;
    teamId?: number;
  };

  type UpdateGhCreatorInfoRequest = {
    /** 是什么样类型的达人，为空表示 LiveCreator  */
    bizScene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
    /** 拟更新信息的达人列表 */
    creatorIds?: number[];
    /** 公会平台类型 */
    ghPlatformType?:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
  };

  type UpdateGhJobPlanRequest = {
    /** 邀约时评论要使用的话术列表，会随机从中选择发送。只在 ghJobType 为 SendInvite 时有意义 */
    commentWordIds?: number[];
    /** 查询条件，由 /api/gh/creator/page 这个接口的params JSON.stringify(params) 而来 */
    creatorFilter?: string;
    /** 重复日期，只包含星期信息的表达式(具体参考自动流程计划) */
    cronExpression?: string;
    deleteMsg?: boolean;
    /** 同步时是否删除与主播的聊天记录，默认为true。只在 ghJobType 为 SyncPm 时有意义 */
    deleteSession?: boolean;
    /** 如果是随机执行，用-隔开开始时间和结束时间[start, start-end, start, ...] */
    expressions?: string[];
    ghJobType?:
      | 'AccountFollow'
      | 'AccountHealthCheck'
      | 'AccountMaintenance'
      | 'AccountMessageCheck'
      | 'KOL_LiveRewards'
      | 'ParentJob'
      | 'SendInvite'
      | 'SendPm'
      | 'SendUserCard'
      | 'ShareVideo'
      | 'SubJob'
      | 'SyncContactToPhone'
      | 'SyncContacts'
      | 'SyncCreator'
      | 'SyncLiveStream'
      | 'SyncPm'
      | 'TS_AddContacts'
      | 'TS_AddFriends'
      | 'TS_DemandPayment'
      | 'TS_IMChatByFilter'
      | 'TS_IMChatByHandle'
      | 'TS_LoginCheck'
      | 'TS_SampleApprove'
      | 'TS_SendBuyerIMChat'
      | 'TS_SendEmail'
      | 'TS_SendFacebook'
      | 'TS_SendLine'
      | 'TS_SendWhatsApp'
      | 'TS_SendZalo'
      | 'TS_SyncBuyerInfo'
      | 'TS_SyncCreator'
      | 'TS_SyncOrders'
      | 'TS_SyncProducts'
      | 'TS_SyncSampleCreator'
      | 'TS_SyncShopInfo'
      | 'TS_SyncVideos'
      | 'TS_TargetPlanByFilter'
      | 'TS_TargetPlanByHandle'
      | 'TS_TargetPlanClear'
      | 'TS_VideoADCode';
    /** 拟使用的TK账号类型，默认值 Normal。只在 ghJobType 为 SendInvite 时有意义 */
    ghScheduleType?: 'GhBackstage' | 'Mobile' | 'Normal' | 'Official' | 'Union' | 'Unknown';
    id?: number;
    /** 私信高级设置，只在 ghJobType 为 SendInvite/AccountMaintenance 时有意义 */
    inviteAdvanceSettings?: Record<string, any>;
    /** 发送邀约手机账号选择策略，仅在 ghScheduleType == Mobile 的时候有意义 */
    inviteMobileChoicePolicy?: MobileChoicePolicy;
    /** 发送邀约分身选择策略，仅在 ghScheduleType != Mobile 的时候有意义 */
    inviteShopChoicePolicy?: ShopChoicePolicy;
    /** 最多允许执行多少分钟(需求方要求是流程自己实现，所以应该只有部分流程支持这个参数) */
    maxMinutes?: number;
    /** 相应的手机账号id，只有在 ghJobType 为 AccountMaintenance 时有意义 */
    mobileAccountIds?: number[];
    name?: string;
    /** 发送私信前关注主播并将私信内容评论到主播的第一条视频 */
    sendComment?: boolean;
    /** 是否增加随机表情 */
    sendEmoji?: boolean;
    /** 是否发送邀约卡片，只有发送邀约建联计划才有意义(只有公会后台账号才支持此特性） */
    sendInviteCard?: boolean;
    /** 已分配/已认领的达人不再发送 (为空表示 true )，只对发送邀约建联的计划有意义 */
    skipResponsible?: boolean;
    /** 邀约要使用的话术列表，会随机从中选择发送。只在 ghJobType 为 SendInvite 时有意义 */
    wordsIds?: number[];
  };

  type UpdateGhSessionStatusRequest = {
    ids?: number[];
    status?: 'Error' | 'NeedReply' | 'Replied' | 'Replying';
  };

  type UpdateScheduleConfigRequest = {
    /** 有哪些表示账号已经封禁的标签，目前key的取值: account_logged_out | account_banned | account_limited | pm_too_fast | need_config_privacy */
    blockTags?: Record<string, any>;
    /** 发送时最高并发数，为空或者为<=0都表示不限制 (目前只在公会后台账号邀约建联时有意义) */
    concurrent?: number;
    ghPlatformType?:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
    ghScheduleType?: 'GhBackstage' | 'Mobile' | 'Normal' | 'Official' | 'Union' | 'Unknown';
    /** 允许邀请的时间 */
    inviteTimeFrame?: AllowTimeConfig;
    /** 单账号每日最多私信条数 */
    maxShopDayPm?: number;
    /** 单账号每批次最多私信条数 */
    maxShopTaskPm?: number;
    /** 打上了这个标签的分身的个数 */
    shopCount?: number;
    /** 发送私信的分身的标签 */
    shopTag?: string;
    /** 单账号每批私信间隔，单位分钟 */
    shopTaskPmInterval?: number;
  };

  type UpdateSyncCreatorConfigRequest = {
    ghPlatformType?:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
    /** 单分身每次最多更新多少个主播 */
    maxShopTaskSync?: number;
    /** 信息更新账号所用标签 */
    shopTag?: string;
  };

  type UserBriefVo = {
    avatar?: string;
    nickname?: string;
  };

  type UserDto = {
    avatar?: string;
    createTime?: string;
    gender?: 'FEMALE' | 'MALE' | 'UNSPECIFIC';
    id?: number;
    nickname?: string;
    partnerId?: number;
    residentCity?: string;
    signature?: string;
    status?: 'ACTIVE' | 'BLOCK' | 'DELETED' | 'INACTIVE';
    tenant?: number;
    userType?: 'NORMAL' | 'PARTNER' | 'SHADOW';
  };

  type WebResult = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultboolean = {
    code?: number;
    data?: boolean;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultFilterStatInfo = {
    code?: number;
    data?: FilterStatInfo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultGhCreatorDetailVo = {
    code?: number;
    data?: GhCreatorDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultGhCreatorExpiredAutoTags = {
    code?: number;
    data?: GhCreatorExpiredAutoTags;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultGhGeneralSettingsVo = {
    code?: number;
    data?: GhGeneralSettingsVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultGhGifterDetailVo = {
    code?: number;
    data?: GhGifterDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultGhInteractionDetailVo = {
    code?: number;
    data?: GhInteractionDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultGhInteractionDto = {
    code?: number;
    data?: GhInteractionDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultGhJobDetailVo = {
    code?: number;
    data?: GhJobDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultGhJobDeviceVo = {
    code?: number;
    data?: GhJobDeviceVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultGhJobDto = {
    code?: number;
    data?: GhJobDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultGhJobPlanVo = {
    code?: number;
    data?: GhJobPlanVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultGhShopStatistics = {
    code?: number;
    data?: GhShopStatistics;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultGhSpeechDto = {
    code?: number;
    data?: GhSpeechDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultGhSpeechGroupDto = {
    code?: number;
    data?: GhSpeechGroupDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultGhUserDetailVo = {
    code?: number;
    data?: GhUserDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultGhVideoCreatorDetailVo = {
    code?: number;
    data?: GhVideoCreatorDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultint = {
    code?: number;
    data?: number;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultJobCreatingInfo = {
    code?: number;
    data?: JobCreatingInfo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultKolLiveDto = {
    code?: number;
    data?: KolLiveDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultKolSubTeamDto = {
    code?: number;
    data?: KolSubTeamDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultKolUserInfo = {
    code?: number;
    data?: KolUserInfo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListCreatorBriefVo = {
    code?: number;
    data?: CreatorBriefVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListGhAvailableVo = {
    code?: number;
    data?: GhAvailableVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListGhCreatorDto = {
    code?: number;
    data?: GhCreatorDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListGhCreatorPerformanceStatVo = {
    code?: number;
    data?: GhCreatorPerformanceStatVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListGhCreatorStatusVo = {
    code?: number;
    data?: GhCreatorStatusVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListGhGifterDto = {
    code?: number;
    data?: GhGifterDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListGhJobDeviceVo = {
    code?: number;
    data?: GhJobDeviceVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListGhJobPlanVo = {
    code?: number;
    data?: GhJobPlanVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListGhLiveGiftDto = {
    code?: number;
    data?: GhLiveGiftDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListGhSpeechGroupVo = {
    code?: number;
    data?: GhSpeechGroupVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListGhStatRowVo = {
    code?: number;
    data?: GhStatRowVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListGhUserDto = {
    code?: number;
    data?: GhUserDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListHandleCreatorIdVo = {
    code?: number;
    data?: HandleCreatorIdVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListHasInteractionVo = {
    code?: number;
    data?: HasInteractionVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListIdNameVo = {
    code?: number;
    data?: IdNameVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListKolAvailableVo = {
    code?: number;
    data?: KolAvailableVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListKolRegionMapDto = {
    code?: number;
    data?: KolRegionMapDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListlong = {
    code?: number;
    data?: number[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListstring = {
    code?: number;
    data?: string[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTkCreatorFiledVo = {
    code?: number;
    data?: TkCreatorFiledVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultlong = {
    code?: number;
    data?: number;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultMap = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultMapstring = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultNotifyScheduleVo = {
    code?: number;
    data?: NotifyScheduleVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultGhCreatorDetailVo = {
    code?: number;
    data?: PageResultGhCreatorDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultGhGifterDetailVo = {
    code?: number;
    data?: PageResultGhGifterDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultGhInteractionDetailVo = {
    code?: number;
    data?: PageResultGhInteractionDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultGhJobDto = {
    code?: number;
    data?: PageResultGhJobDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultGhLiveCreatorDetailVo = {
    code?: number;
    data?: PageResultGhLiveCreatorDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultGhMessageDto = {
    code?: number;
    data?: PageResultGhMessageDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultGhSessionVo = {
    code?: number;
    data?: PageResultGhSessionVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultGhSpeechDto = {
    code?: number;
    data?: PageResultGhSpeechDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultGhUserDetailVo = {
    code?: number;
    data?: PageResultGhUserDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultGhVideoCreatorDetailVo = {
    code?: number;
    data?: PageResultGhVideoCreatorDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultKolLiveDto = {
    code?: number;
    data?: PageResultKolLiveDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultKolSubTeamDetailVo = {
    code?: number;
    data?: PageResultKolSubTeamDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultstring = {
    code?: number;
    data?: PageResultstring;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultstring = {
    code?: number;
    data?: string;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultSyncBatchResult = {
    code?: number;
    data?: SyncBatchResult;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultSyncMessageResult = {
    code?: number;
    data?: SyncMessageResult;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTeamKolConfig = {
    code?: number;
    data?: TeamKolConfig;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTeamMobileVo = {
    code?: number;
    data?: TeamMobileVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkInteractionDetailVo = {
    code?: number;
    data?: TkInteractionDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WorkTimeConfig = {
    FRI?: number[];
    /** 每天的数组长度一定是24，分别表示每小时属于什么时间 0: 空闲， 1: 工作时间， 2: 养号时间 */
    MON?: number[];
    SAT?: number[];
    SUN?: number[];
    THU?: number[];
    TUE?: number[];
    WED?: number[];
  };

  type XiaohongshuConfig = true;
}
