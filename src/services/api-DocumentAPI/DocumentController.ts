// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 获取文档详情 GET /api/document/${param0} */
export async function documentByIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.documentByIdGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultDocumentDetailVo>(`/api/document/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 修改文档 PUT /api/document/${param0} */
export async function documentByIdPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.documentByIdPutParams,
  body: API.CreateDocumentRequest,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultDocumentDto>(`/api/document/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 删除文档 DELETE /api/document/${param0} */
export async function documentByIdDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.documentByIdDeleteParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultDocumentDto>(`/api/document/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 记录上传的附件 POST /api/document/${param0}/attachment */
export async function documentByIdAttachmentPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.documentByIdAttachmentPostParams,
  body: API.CreateDocumentAttachmentRequest,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/document/${param0}/attachment`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 获取附件上传的的STS Token GET /api/document/${param0}/ossUploadToken */
export async function documentByIdOssUploadTokenGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.documentByIdOssUploadTokenGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultStsPostSignature>(`/api/document/${param0}/ossUploadToken`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 修改文档状态 PUT /api/document/${param0}/status */
export async function documentByIdStatusPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.documentByIdStatusPutParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultDocumentDto>(`/api/document/${param0}/status`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 创建文档 POST /api/document/create */
export async function documentCreatePost(
  body: API.CreateDocumentRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultDocumentDto>('/api/document/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询文档（不包含正文） GET /api/document/page */
export async function documentPageGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.documentPageGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultDocumentBriefVo>('/api/document/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
