declare namespace API {
  type ActiveVoucherRequest = {
    activeNow?: boolean;
    cardNumber?: string;
    cardPassword?: string;
  };

  type BalanceInOutDetailVo = {
    /** 交易后的账户余额 */
    balance?: number;
    /** 余额抵扣金额 */
    balanceAmount?: number;
    createTime?: string;
    id?: number;
    /** 关联的订单id */
    orderId?: number;
    orderSerialNumber?: string;
    /** 订单来源/创建方式 */
    orderSource?: 'Automatic' | 'Offline' | 'Users';
    orderType?:
      | 'BuyGiftCard'
      | 'BuyIp'
      | 'BuyPluginPack'
      | 'BuyRpaVoucher'
      | 'BuyTkPack'
      | 'BuyTkshop'
      | 'BuyTraffic'
      | 'CashOut'
      | 'MakeupPriceDifference'
      | 'Merge'
      | 'OrderCancel'
      | 'PartnerBuyVoucher'
      | 'PartnerDraw'
      | 'PartnerRecharge'
      | 'PartnerRechargeCredit'
      | 'PartnerRenewRpaVoucher'
      | 'Present'
      | 'Recharge'
      | 'RechargeCredit'
      | 'Refund'
      | 'RenewIp'
      | 'RenewPluginPack'
      | 'RenewRpaVoucher'
      | 'RenewTkPack'
      | 'RenewTkshop'
      | 'UpgradeTkshop';
    payType?: 'AliPay' | 'BalancePay' | 'BankPay' | 'WechatPay';
    /** 订单应付总额，比如说买一年打8折或者销售改价之后的价格 */
    payablePrice?: number;
    /** 充值赠送金额;只在充值订单有效 */
    presentAmount?: number;
    /** 实付金额，即现金支付金额;可开票金额以此为依据 */
    realPrice?: number;
    serialNumber?: string;
    /** 订单总额 */
    totalPrice?: number;
    /** 代金券抵扣金额 */
    voucherAmount?: number;
    /** 代金券卡号 */
    voucherCardNumber?: string;
  };

  type BankPayConfig = {
    accountName?: string;
    accountNumber?: string;
    bankName?: string;
  };

  type CalcPriceResponse = {
    /** 折扣,[0-1] */
    discount?: number;
    /** 打折减掉的金额(如果是打折的话) */
    discountAmount?: number;
    /** 记录每个item的价格及折扣或赠送信息，<key=goodsId, value=PriceInfo> */
    items?: Record<string, any>;
    /** 订单应付价(减掉了打折等信息) */
    payablePrice?: number;
    /** 赠送金额，目前只出现在购买花瓣 */
    presentAmount?: number;
    /** 订单总成本 */
    totalCost?: number;
    /** 订单总价(原价) */
    totalPrice?: number;
  };

  type ConfigVoiceInfoRequest = {
    account?: string;
    address?: string;
    bank?: string;
    email?: string;
    /** 按原型图一个团队只允许有一条发票信息，所以ID字段暂时用不到 */
    id?: number;
    invoiceType?: 'EntNormal' | 'EntSpecial' | 'None' | 'Personal';
    /** 电话号码 */
    number?: string;
    receiverAddress?: string;
    receiverName?: string;
    receiverPhone?: string;
    taxNum?: string;
    title?: string;
  };

  type CostSummaryVo = {
    /** 每日支出（只有当日有支出才会有数据） */
    days?: DayCostVo[];
    /** 当月总支出 */
    total?: number;
    /** 不同种类的支出 */
    typeCosts?: Record<string, any>;
  };

  type CreateBuyCreditOrderRequest = {
    /** 已经勾选阅读和同意使用协议，没什么用 */
    agreement?: boolean;
    /** 余额抵扣金额 */
    balanceAmount?: number;
    /** 购买数量 */
    count?: number;
    /** 优惠码 */
    distributionCode?: string;
    /** 是否立即支付（点稍候支付该属性传false） */
    immediatePay?: boolean;
    payType?: 'AliPay' | 'BalancePay' | 'BankPay' | 'WechatPay';
    /** 代金券抵扣金额，不得大于代金券余额 */
    voucherAmount?: number;
    /** 要使用的代金券id */
    voucherId?: number;
  };

  type CreateOrderResponse = {
    bankAccount?: string;
    bankAccountName?: string;
    bankName?: string;
    bankRemark?: string;
    createTime?: string;
    /** 扣减金额 */
    deductedPrice?: number;
    orderId?: number;
    /** 支付输出的内容 */
    payOutContent?: string;
    /** 支付输出的内容类型 */
    payOutType?: string;
    /** 如果不需要现金支付，该订单状态会直接变成已支付 */
    payStatus?:
      | 'CANCELED'
      | 'Created'
      | 'Locked'
      | 'PAID'
      | 'PartialRefund'
      | 'REFUNDED'
      | 'WAIT_CONFIRM';
    /** 支付方式 */
    payType?: 'AliPay' | 'BalancePay' | 'BankPay' | 'WechatPay';
    /** 需要现金支付的money */
    realPrice?: number;
    salesReduction?: number;
    serialNumber?: string;
  };

  type CreateRechargeOrderRequest = {
    /** 充值金额 */
    amount?: number;
    /** 支付方式 */
    payType?: 'AliPay' | 'BalancePay' | 'BankPay' | 'WechatPay';
  };

  type CreditCostItemVo = {
    cost?: number;
    creditType?:
      | 'BuyCredit'
      | 'ConsumeCredit'
      | 'FingerprintQuota'
      | 'GiftCard'
      | 'InitPresent'
      | 'IosDeveloperApprove'
      | 'IpOverQuotaTraffic'
      | 'OrderRefund'
      | 'Present'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMarketFlow'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TransferIn'
      | 'TransferOut'
      | 'TransitTraffic';
    desc?: string;
  };

  type CreditCostPackVo = {
    desc?: string;
    itemMap?: Record<string, any>;
    pack?: 'IpTraffic' | 'Member' | 'Rpa' | 'Shop' | 'Storage';
    totalCost?: number;
  };

  type CreditInOutDetailVo = {
    amount?: number;
    bizDesc?: string;
    bizId?: number;
    createTime?: string;
    creatorId?: number;
    creatorName?: string;
    creatorTeam?: number;
    creatorTeamName?: string;
    credit?: number;
    creditType?:
      | 'BuyCredit'
      | 'ConsumeCredit'
      | 'FingerprintQuota'
      | 'GiftCard'
      | 'InitPresent'
      | 'IosDeveloperApprove'
      | 'IpOverQuotaTraffic'
      | 'OrderRefund'
      | 'Present'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMarketFlow'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TransferIn'
      | 'TransferOut'
      | 'TransitTraffic';
    goodsType?:
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher';
    id?: number;
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    price?: number;
    remainCredit?: number;
    serialNumber?: string;
    teamId?: number;
  };

  type DailyCreditCost = {
    day?: string;
    packs?: Record<string, any>;
    totalCost?: number;
  };

  type DayCostOrderVo = {
    orderId?: number;
    price?: number;
    serialNumber?: string;
  };

  type DayCostVo = {
    /** 天 */
    day?: string;
    /** 当天的订单记录 */
    orders?: DayCostOrderVo[];
    /** 当天总支出 */
    total?: number;
  };

  type DiscountsDto = {
    amount?: number;
    createTime?: string;
    discountCode?: string;
    discountRange?:
      | 'ConsumeCredit'
      | 'Ip'
      | 'Recharge'
      | 'RechargeCredit'
      | 'RpaVoucher'
      | 'TkPack';
    discountType?: 'Discount' | 'LadderPrice' | 'Present';
    enabled?: boolean;
    goodsId?: number;
    id?: number;
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    remarks?: string;
    threshold?: number;
  };

  type DiscountsVo = {
    /** 赠送数量或折扣百分比或阶梯折扣百分比 */
    amount?: number;
    /** 打折code */
    discountCode?: string;
    /** 打折还是赠送 */
    discountType?: 'Discount' | 'LadderPrice' | 'Present';
    /** 周期或数量单位 */
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    /** 备注 */
    remarks?: string;
    /** 期数或数量 */
    threshold?: number;
  };

  type GiftCardPackDetailVo = {
    cardCount?: number;
    createTime?: string;
    giftCardType?: 'Credit' | 'RpaVoucher';
    id?: number;
    name?: string;
    packItems?: GiftCardPackItemDetailVo[];
    partnerId?: number;
    /** 剩余可用礼品卡数量 */
    remainCount?: number;
    remarks?: string;
    serialNumber?: string;
    /** 是否在有效期内（根据createTime & validDays计算出来的） */
    valid?: boolean;
    validDays?: number;
  };

  type GiftCardPackItemDetailVo = {
    activatedTeamId?: number;
    activatedTeamName?: string;
    activeTime?: string;
    amount?: number;
    cardNumber?: string;
    cardPackId?: number;
    cardPassword?: string;
    createTime?: string;
    disabled?: boolean;
    /** 当为rpa礼品卡时，表示该卡有几个 periodUnit 月/周 */
    duration?: number;
    expireDate?: string;
    giftCardType?: 'Credit' | 'RpaVoucher';
    goodsId?: number;
    goodsType?:
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher';
    id?: number;
    orderItemId?: number;
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    remarks?: string;
    /** 是否在有效期内（根据createTime & validDays计算出来的） */
    valid?: boolean;
  };

  type GiftCardPackItemVo = {
    activatedTeamId?: number;
    activeTime?: string;
    amount?: number;
    cardNumber?: string;
    cardPackId?: number;
    cardPassword?: string;
    createTime?: string;
    disabled?: boolean;
    /** 当为rpa礼品卡时，表示该卡有几个 periodUnit 月/周 */
    duration?: number;
    expireDate?: string;
    giftCardType?: 'Credit' | 'RpaVoucher';
    goodsId?: number;
    goodsType?:
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher';
    id?: number;
    orderItemId?: number;
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    remarks?: string;
    /** 是否在有效期内（根据createTime & validDays计算出来的） */
    valid?: boolean;
  };

  type GoodsDto = {
    arch?: string;
    bandwidth?: number;
    buyoutPrice?: number;
    city?: string;
    cost?: number;
    countryCode?: string;
    cpu?: number;
    currency?: 'CREDIT' | 'RMB' | 'USD';
    dayCost?: number;
    dayPrice?: number;
    dayTraffic?: number;
    description?: string;
    disk?: number;
    diskCategory?: string;
    dynamic?: boolean;
    goodsType?:
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher';
    id?: number;
    initialQuantity?: number;
    instanceType?: string;
    ipv6?: boolean;
    listPrice?: number;
    mem?: number;
    name?: string;
    networkType?: 'cloudIdc' | 'mobile' | 'proxyIdc' | 'residential' | 'unknown' | 'unknownIdc';
    onSale?: 'Disabled' | 'Offline' | 'Online';
    perfLevel?:
      | 'CostEffective'
      | 'HighlyConcurrent'
      | 'LargeTraffic'
      | 'None'
      | 'RemoteLogin'
      | 'UnlimitedTraffic';
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    pipeType?: 'None' | 'Proxy' | 'Tunnel' | 'TunnelFailToProxy';
    platform?: 'android' | 'linux' | 'macos' | 'unknown' | 'web' | 'windows' | 'windows7';
    price?: number;
    provider?: string;
    region?: string;
    remoteLogin?: boolean;
    resourceId?: number;
    sortNo?: number;
    tcpfp?: boolean;
    traffic?: number;
    trafficCurrency?: 'CREDIT' | 'RMB' | 'USD';
    trafficPrice?: number;
    updateTime?: string;
    weekCost?: number;
    weekPrice?: number;
    weekTraffic?: number;
  };

  type GoodsVo = {
    arch?: string;
    bandwidth?: number;
    buyoutPrice?: number;
    city?: string;
    cost?: number;
    country?: string;
    countryCode?: string;
    /** 国家英文 */
    countryEn?: string;
    cpu?: number;
    currency?: 'CREDIT' | 'RMB' | 'USD';
    dayCost?: number;
    dayPrice?: number;
    dayTraffic?: number;
    description?: string;
    disk?: number;
    diskCategory?: string;
    dynamic?: boolean;
    goodsType?:
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher';
    id?: number;
    initialQuantity?: number;
    instanceType?: string;
    ipv6?: boolean;
    listPrice?: number;
    mem?: number;
    name?: string;
    networkType?: 'cloudIdc' | 'mobile' | 'proxyIdc' | 'residential' | 'unknown' | 'unknownIdc';
    onSale?: 'Disabled' | 'Offline' | 'Online';
    perfLevel?:
      | 'CostEffective'
      | 'HighlyConcurrent'
      | 'LargeTraffic'
      | 'None'
      | 'RemoteLogin'
      | 'UnlimitedTraffic';
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    pipeType?: 'None' | 'Proxy' | 'Tunnel' | 'TunnelFailToProxy';
    platform?: 'android' | 'linux' | 'macos' | 'unknown' | 'web' | 'windows' | 'windows7';
    price?: number;
    provider?: string;
    providerName?: string;
    region?: string;
    remoteLogin?: boolean;
    resourceId?: number;
    sortNo?: number;
    tcpfp?: boolean;
    traffic?: number;
    trafficCurrency?: 'CREDIT' | 'RMB' | 'USD';
    trafficPrice?: number;
    updateTime?: string;
    weekCost?: number;
    weekPrice?: number;
    weekTraffic?: number;
  };

  type InvoiceHistoryVo = {
    account?: string;
    address?: string;
    amount?: number;
    bank?: string;
    consoleDesc?: string;
    createTime?: string;
    /** 是否电子发票 */
    electronic?: boolean;
    email?: string;
    expressCompany?: string;
    expressNumber?: string;
    id?: number;
    invoiceCode?: string;
    invoiceNumber?: string;
    invoiceStatus?: 'Applied' | 'Invoiced' | 'NotInvoiced';
    invoiceType?: 'EntNormal' | 'EntSpecial' | 'None' | 'Personal';
    number?: string;
    receiverAddress?: string;
    receiverName?: string;
    receiverPhone?: string;
    tax?: number;
    taxNum?: string;
    teamId?: number;
    title?: string;
    userDesc?: string;
  };

  type InvoiceInfoVo = {
    account?: string;
    address?: string;
    bank?: string;
    electronic?: boolean;
    email?: string;
    id?: number;
    invoiceType?: 'EntNormal' | 'EntSpecial' | 'None' | 'Personal';
    /** 电话号码 */
    number?: string;
    receiverAddress?: string;
    receiverName?: string;
    receiverPhone?: string;
    taxNum?: string;
    title?: string;
  };

  type InvoiceSummaryVo = {
    invoiceInfo?: InvoiceInfoVo;
    /** 可开票金额 */
    notInvoicedAmount?: number;
  };

  type IpLocationDto = {
    city?: string;
    cityEn?: string;
    continent?: string;
    continentCode?: string;
    continentEn?: string;
    country?: string;
    countryCode?: string;
    countryEn?: string;
    createTime?: string;
    geonameId?: number;
    id?: number;
    inEu?: boolean;
    latitude?: number;
    level?: 'City' | 'Continent' | 'Country' | 'District' | 'None' | 'Province' | 'Unknown';
    locale?: string;
    longitude?: number;
    postalCode?: string;
    province?: string;
    provinceCode?: string;
    provinceEn?: string;
    show?: boolean;
    teamId?: number;
    timezone?: string;
  };

  type IpWithLocationVo = {
    autoRenew?: boolean;
    cloudProvider?: string;
    cloudRegion?: string;
    createTime?: string;
    creatorId?: number;
    description?: string;
    directDownTraffic?: number;
    directUpTraffic?: number;
    domestic?: boolean;
    downTraffic?: number;
    dynamic?: boolean;
    eipId?: number;
    enableWhitelist?: boolean;
    /** 过期状态 */
    expireStatus?: 'Expired' | 'Expiring' | 'Normal';
    forbiddenLongLatitude?: boolean;
    gatewayId?: number;
    goodsId?: number;
    goodsType?:
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher';
    id?: number;
    importType?: 'Platform' | 'User';
    invalidTime?: string;
    ip?: string;
    ipv6?: boolean;
    lastProbeTime?: string;
    latitude?: number;
    locale?: string;
    location?: IpLocationDto;
    locationId?: number;
    longitude?: number;
    name?: string;
    networkType?: 'cloudIdc' | 'mobile' | 'proxyIdc' | 'residential' | 'unknown' | 'unknownIdc';
    operateStatus?: 'shared' | 'sharing' | 'sole' | 'transferring';
    originalTeam?: number;
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    pipeType?: 'None' | 'Proxy' | 'Tunnel' | 'TunnelFailToProxy';
    preferTransit?: number;
    probeError?: string;
    protoType?: 'http' | 'httpTunnel' | 'ipgo' | 'luminati' | 'socks5' | 'ssh' | 'vps';
    /** 供应商名称 */
    providerName?: string;
    realIp?: string;
    refreshUrl?: string;
    remoteLogin?: boolean;
    renewPrice?: number;
    source?: string;
    speedLimit?: number;
    status?: 'Available' | 'Pending' | 'Unavailable';
    sticky?: boolean;
    teamId?: number;
    testingTime?: number;
    timezone?: string;
    traffic?: number;
    trafficCurrency?: 'CREDIT' | 'RMB' | 'USD';
    trafficPrice?: number;
    trafficUnlimited?: boolean;
    transitType?: 'Auto' | 'Direct' | 'Transit';
    tunnelTypes?: string;
    upTraffic?: number;
    valid?: boolean;
    validEndDate?: string;
    vpsId?: number;
  };

  type ItemPriceInfo = {
    costPrice?: number;
    /** 当前过期时间 */
    currentValidEndTime?: string;
    discount?: DiscountsVo;
    /** 打折减掉的金额，如果是打折的话 */
    discountAmount?: number;
    goodsId?: number;
    /** 应付价格 */
    payablePrice?: number;
    /** 赠送数量，如果是赠送的话。目前只出现在购买花瓣 */
    presentAmount?: number;
    /** item总价 */
    price?: number;
    /** 续费后到期时间 */
    validEndTime?: string;
  };

  type LockAndPayOrderRequest = {
    /** 已经勾选阅读和同意使用协议，没什么用 */
    agreement?: boolean;
    /** 余额抵扣金额 */
    balanceAmount?: number;
    /** 是否立即支付（点稍候支付该属性传false） */
    immediatePay?: boolean;
    orderId?: number;
    payType?: 'AliPay' | 'BalancePay' | 'BankPay' | 'WechatPay';
    /** 代金券抵扣金额，不得大于代金券余额 */
    voucherAmount?: number;
    /** 要使用的代金券id */
    voucherId?: number;
  };

  type MergePayOrdersRequest = {
    /** 已经勾选阅读和同意使用协议，没什么用 */
    agreement?: boolean;
    /** 余额抵扣金额 */
    balanceAmount?: number;
    /** 是否立即支付（点稍候支付该属性传false） */
    immediatePay?: boolean;
    orderIds?: number[];
    payType?: 'AliPay' | 'BalancePay' | 'BankPay' | 'WechatPay';
    /** 代金券抵扣金额，不得大于代金券余额 */
    voucherAmount?: number;
    /** 要使用的代金券id */
    voucherId?: number;
  };

  type NotifyBankPayFailedRequest = {
    orderId?: number;
    payStatus?:
      | 'CANCELED'
      | 'Created'
      | 'Locked'
      | 'PAID'
      | 'PartialRefund'
      | 'REFUNDED'
      | 'WAIT_CONFIRM';
    remarks?: string;
  };

  type NotifyBankPaySuccessRequest = {
    orderId?: number;
    remarks?: string;
    thirdPartOrderNumber?: string;
  };

  type NotInvoicedOrderVo = {
    /** 是否为自动订单，例如自动续费订单 */
    automatic?: boolean;
    createTime?: string;
    id?: number;
    orderType?:
      | 'BuyGiftCard'
      | 'BuyIp'
      | 'BuyPluginPack'
      | 'BuyRpaVoucher'
      | 'BuyTkPack'
      | 'BuyTkshop'
      | 'BuyTraffic'
      | 'CashOut'
      | 'MakeupPriceDifference'
      | 'Merge'
      | 'OrderCancel'
      | 'PartnerBuyVoucher'
      | 'PartnerDraw'
      | 'PartnerRecharge'
      | 'PartnerRechargeCredit'
      | 'PartnerRenewRpaVoucher'
      | 'Present'
      | 'Recharge'
      | 'RechargeCredit'
      | 'Refund'
      | 'RenewIp'
      | 'RenewPluginPack'
      | 'RenewRpaVoucher'
      | 'RenewTkPack'
      | 'RenewTkshop'
      | 'UpgradeTkshop';
    /** 实付金额，即现金支付金额;可开票金额以此为依据 */
    realPrice?: number;
    realRefundAmount?: number;
    serialNumber?: string;
  };

  type OrderDetailVo = {
    /** 是否为自动订单，例如自动续费订单 */
    automatic?: boolean;
    /** 余额抵扣金额 */
    balanceAmount?: number;
    /** 如果是银行卡支付，则包含银行卡信息 */
    bankPayConfig?: BankPayConfig;
    cashPayType?: 'AliPay' | 'BalancePay' | 'BankPay' | 'WechatPay';
    createTime?: string;
    creatorId?: number;
    discountReason?: string;
    earnedPartner?: number;
    /** 钱款入账的代理商信息 */
    earnedPartnerDto?: PartnerDto;
    id?: number;
    /** 订单锁定时间;订单锁定之后60分钟不支付会被取消掉 */
    lockTime?: string;
    nickname?: string;
    orderType?:
      | 'BuyGiftCard'
      | 'BuyIp'
      | 'BuyPluginPack'
      | 'BuyRpaVoucher'
      | 'BuyTkPack'
      | 'BuyTkshop'
      | 'BuyTraffic'
      | 'CashOut'
      | 'MakeupPriceDifference'
      | 'Merge'
      | 'OrderCancel'
      | 'PartnerBuyVoucher'
      | 'PartnerDraw'
      | 'PartnerRecharge'
      | 'PartnerRechargeCredit'
      | 'PartnerRenewRpaVoucher'
      | 'Present'
      | 'Recharge'
      | 'RechargeCredit'
      | 'Refund'
      | 'RenewIp'
      | 'RenewPluginPack'
      | 'RenewRpaVoucher'
      | 'RenewTkPack'
      | 'RenewTkshop'
      | 'UpgradeTkshop';
    /** 如果父订单不为空说明该订单被选中合并支付了 */
    parentOrderId?: number;
    payStatus?:
      | 'CANCELED'
      | 'Created'
      | 'Locked'
      | 'PAID'
      | 'PartialRefund'
      | 'REFUNDED'
      | 'WAIT_CONFIRM';
    /** 订单应付总额，比如说买一年打8折或者销售改价之后的价格 */
    payablePrice?: number;
    /** 充值赠送金额;只在充值订单有效 */
    presentAmount?: number;
    /** 生产备注 */
    productionRemarks?: string;
    /** 生产状态 */
    productionStatus?:
      | 'Finished'
      | 'NotStart'
      | 'ProduceError'
      | 'Producing'
      | 'ReFunded'
      | 'RefundError'
      | 'Refunding'
      | 'WaitReFund';
    /** 实付金额，即现金支付金额;可开票金额以此为依据 */
    realPrice?: number;
    /** 销售改价折现 */
    salesReduction?: number;
    serialNumber?: string;
    /** 订单总额 */
    totalPrice?: number;
    /** 代金券抵扣金额 */
    voucherAmount?: number;
    /** 代金券卡号 */
    voucherCardNumber?: string;
  };

  type OrderResourceVo = {
    cloudName?: string;
    count?: number;
    discountReason?: string;
    extraInfo?: string;
    giftCardPack?: GiftCardPackDetailVo;
    /** 订单Item的商品信息 */
    goods?: GoodsVo;
    goodsId?: number;
    goodsPeriodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    goodsType?:
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher';
    id?: number;
    ip?: IpWithLocationVo;
    ips?: TeamIpDto[];
    itemPrice?: number;
    orderId?: number;
    orderType?:
      | 'BuyGiftCard'
      | 'BuyIp'
      | 'BuyPluginPack'
      | 'BuyRpaVoucher'
      | 'BuyTkPack'
      | 'BuyTkshop'
      | 'BuyTraffic'
      | 'CashOut'
      | 'MakeupPriceDifference'
      | 'Merge'
      | 'OrderCancel'
      | 'PartnerBuyVoucher'
      | 'PartnerDraw'
      | 'PartnerRecharge'
      | 'PartnerRechargeCredit'
      | 'PartnerRenewRpaVoucher'
      | 'Present'
      | 'Recharge'
      | 'RechargeCredit'
      | 'Refund'
      | 'RenewIp'
      | 'RenewPluginPack'
      | 'RenewRpaVoucher'
      | 'RenewTkPack'
      | 'RenewTkshop'
      | 'UpgradeTkshop';
    productionRemarks?: string;
    productionStatus?:
      | 'Finished'
      | 'NotStart'
      | 'ProduceError'
      | 'Producing'
      | 'ReFunded'
      | 'RefundError'
      | 'Refunding'
      | 'WaitReFund';
    proxyIpId?: number;
    resourceId?: number;
    resourceName?: string;
    resourceType?:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    teamId?: number;
    vpsName?: string;
  };

  type OrdersDto = {
    autoRenew?: boolean;
    /** 余额抵扣金额 */
    balanceAmount?: number;
    buyerId?: number;
    cashPayType?: 'AliPay' | 'BalancePay' | 'BankPay' | 'WechatPay';
    costPrice?: number;
    createTime?: string;
    creatorId?: number;
    discountReason?: string;
    distributionCode?: number;
    distributor?: number;
    drawPrice?: number;
    drawStatus?: 'Applying' | 'Canceled' | 'Drawn' | 'NotDrawn' | 'NotSupported' | 'Paying';
    earnedPartner?: number;
    id?: number;
    invalidReason?: string;
    invoiceStatus?: 'Applied' | 'Invoiced' | 'NotInvoiced';
    lockTime?: string;
    orderSource?: 'Automatic' | 'Offline' | 'Users';
    orderType?:
      | 'BuyGiftCard'
      | 'BuyIp'
      | 'BuyPluginPack'
      | 'BuyRpaVoucher'
      | 'BuyTkPack'
      | 'BuyTkshop'
      | 'BuyTraffic'
      | 'CashOut'
      | 'MakeupPriceDifference'
      | 'Merge'
      | 'OrderCancel'
      | 'PartnerBuyVoucher'
      | 'PartnerDraw'
      | 'PartnerRecharge'
      | 'PartnerRechargeCredit'
      | 'PartnerRenewRpaVoucher'
      | 'Present'
      | 'Recharge'
      | 'RechargeCredit'
      | 'Refund'
      | 'RenewIp'
      | 'RenewPluginPack'
      | 'RenewRpaVoucher'
      | 'RenewTkPack'
      | 'RenewTkshop'
      | 'UpgradeTkshop';
    params?: string;
    parentOrderId?: number;
    payStatus?:
      | 'CANCELED'
      | 'Created'
      | 'Locked'
      | 'PAID'
      | 'PartialRefund'
      | 'REFUNDED'
      | 'WAIT_CONFIRM';
    payTime?: string;
    /** 订单应付总额，比如说买一年打8折或者销售改价之后的价格 */
    payablePrice?: number;
    /** 充值赠送金额;只在充值订单有效 */
    presentAmount?: number;
    /** 生产备注 */
    productionRemarks?: string;
    /** 生产状态 */
    productionStatus?:
      | 'Finished'
      | 'NotStart'
      | 'ProduceError'
      | 'Producing'
      | 'ReFunded'
      | 'RefundError'
      | 'Refunding'
      | 'WaitReFund';
    productionTime?: string;
    /** 实付金额，即现金支付金额;可开票金额以此为依据 */
    realPrice?: number;
    realRefundAmount?: number;
    reductionSalesId?: number;
    remarks?: string;
    /** 销售改价折现 */
    salesReduction?: number;
    serialNumber?: string;
    teamId?: number;
    testing?: boolean;
    thirdPartOrderNumber?: string;
    /** 订单总额 */
    totalPrice?: number;
    /** 代金券卡号 */
    voucherAmount?: number;
    voucherId?: number;
  };

  type PageResultBalanceInOutDetailVo = {
    current?: number;
    list?: BalanceInOutDetailVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultCreditInOutDetailVo = {
    current?: number;
    list?: CreditInOutDetailVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultInvoiceHistoryVo = {
    current?: number;
    list?: InvoiceHistoryVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultOrderDetailVo = {
    current?: number;
    list?: OrderDetailVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultVoucherVo = {
    current?: number;
    list?: VoucherVo[];
    pageSize?: number;
    total?: number;
  };

  type PartnerDto = {
    bankAccount?: string;
    bankName?: string;
    bankNo?: string;
    contactName?: string;
    contactPhone?: string;
    createTime?: string;
    fullName?: string;
    id?: number;
    managerId?: number;
    oemSupport?: boolean;
    openapiSupport?: boolean;
    organizedTeamAccountQuota?: number;
    organizedTeamUserQuota?: number;
    password?: string;
    role?: 'Broker' | 'Organizer';
    shortName?: string;
    status?: 'ACTIVE' | 'BLOCK' | 'DELETED' | 'INACTIVE';
    teamId?: number;
    userId?: number;
  };

  type paymentAlipayNotifyByTeamIdPostParams = {
    /** teamId */
    teamId: number;
    /** out_trade_no */
    out_trade_no: string;
    /** trade_no */
    trade_no: string;
    /** trade_status */
    trade_status: string;
    /** total_amount */
    total_amount: string;
    /** refund_fee */
    refund_fee?: string;
  };

  type paymentAlipayNotifyPostParams = {
    /** out_trade_no */
    out_trade_no: string;
    /** trade_no */
    trade_no: string;
    /** trade_status */
    trade_status: string;
    /** total_amount */
    total_amount: string;
    /** refund_fee */
    refund_fee?: string;
  };

  type paymentAlipayReturnByTeamIdPostParams = {
    /** teamId */
    teamId: number;
    /** out_trade_no */
    out_trade_no: string;
  };

  type paymentAlipayReturnPostParams = {
    /** out_trade_no */
    out_trade_no: string;
  };

  type paymentBalanceInOutListGetParams = {
    /** pageNum */
    pageNum: number;
    /** pageSize */
    pageSize: number;
    /** orderType */
    orderType?:
      | 'BuyGiftCard'
      | 'BuyIp'
      | 'BuyPluginPack'
      | 'BuyRpaVoucher'
      | 'BuyTkPack'
      | 'BuyTkshop'
      | 'BuyTraffic'
      | 'CashOut'
      | 'MakeupPriceDifference'
      | 'Merge'
      | 'OrderCancel'
      | 'PartnerBuyVoucher'
      | 'PartnerDraw'
      | 'PartnerRecharge'
      | 'PartnerRechargeCredit'
      | 'PartnerRenewRpaVoucher'
      | 'Present'
      | 'Recharge'
      | 'RechargeCredit'
      | 'Refund'
      | 'RenewIp'
      | 'RenewPluginPack'
      | 'RenewRpaVoucher'
      | 'RenewTkPack'
      | 'RenewTkshop'
      | 'UpgradeTkshop';
    /** payType */
    payType?: 'AliPay' | 'BalancePay' | 'BankPay' | 'WechatPay';
    /** from */
    from?: string;
    /** to */
    to?: string;
  };

  type paymentCalcBuyCreditPriceGetParams = {
    /** 购买多少个 */
    count: number;
    /** distributionCode */
    distributionCode?: string;
  };

  type paymentCalcBuyIpPriceGetParams = {
    /** ip方案对应的商品id */
    goodsIds: number;
    /** 每个goods购买多少个，必须与goodsIds顺序一一对应 */
    counts: number;
    /** 购买多少个周期 */
    duration: number;
    /** 续费周期单位 */
    periodUnit:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
  };

  type paymentCancelInvoiceByInvoiceIdPutParams = {
    /** invoiceId */
    invoiceId: number;
  };

  type paymentCancelOrderByOrderIdPutParams = {
    /** orderId */
    orderId: number;
    /** reason */
    reason?: string;
  };

  type paymentCancelOrdersPutParams = {
    /** orderIds */
    orderIds: string;
    /** reason */
    reason?: string;
  };

  type paymentCostSummaryGetParams = {
    /** 开始日期 */
    from?: string;
    /** 结束日期 */
    to?: string;
  };

  type paymentCreditExcelByTokenGetParams = {
    /** token */
    token: string;
  };

  type paymentCreditExcelGetParams = {
    /** creditTypes */
    creditTypes?: string;
    /** from */
    from?: string;
    /** to */
    to?: string;
  };

  type paymentCreditExcelTokenGetParams = {
    /** creditTypes */
    creditTypes?: string;
    /** from */
    from?: string;
    /** to */
    to?: string;
  };

  type paymentCreditInOutListGetParams = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** creditType */
    creditType?:
      | 'BuyCredit'
      | 'ConsumeCredit'
      | 'FingerprintQuota'
      | 'GiftCard'
      | 'InitPresent'
      | 'IosDeveloperApprove'
      | 'IpOverQuotaTraffic'
      | 'OrderRefund'
      | 'Present'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMarketFlow'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TransferIn'
      | 'TransferOut'
      | 'TransitTraffic';
    /** minCredit */
    minCredit?: number;
    /** serialNumber */
    serialNumber?: string;
    /** from */
    from?: string;
    /** to */
    to?: string;
  };

  type paymentDiscountsGetParams = {
    /** goodsIds */
    goodsIds: number;
  };

  type paymentFindOrderResourcesGetParams = {
    /** orderIds */
    orderIds: number;
  };

  type paymentGiftCardActiveAGiftCardPostParams = {
    /** receivingTeamId */
    receivingTeamId?: number;
    /** cardNumber */
    cardNumber: string;
    /** cardPassword */
    cardPassword: string;
    /** 是否立即激活，如果 false 说明只想检查一下密码对不对，并取回相应信息 */
    activeNow?: boolean;
  };

  type paymentInvoiceHistoryGetParams = {
    /** pageNum */
    pageNum: number;
    /** pageSize */
    pageSize: number;
  };

  type paymentInvoicePutParams = {
    /** orderIds */
    orderIds: number;
    /** electronic */
    electronic?: boolean;
    /** address */
    address?: string;
    /** userDesc */
    userDesc?: string;
  };

  type paymentOrderByOrderIdMarkBankTransferDonePostParams = {
    /** orderId */
    orderId: number;
  };

  type paymentOrderByOrderIdPaidInfoForPayGetParams = {
    /** orderId */
    orderId: number;
  };

  type paymentOrdersByOrderIdDetailGetParams = {
    /** orderId */
    orderId: number;
  };

  type paymentOrdersByOrderIdRecalculateDrawGetParams = {
    /** orderId */
    orderId: number;
  };

  type paymentOrdersGetParams = {
    /** pageNum */
    pageNum: number;
    /** pageSize */
    pageSize: number;
    /** id */
    id?: number;
    /** parentOrderId */
    parentOrderId?: number;
    /** serialNumber */
    serialNumber?: string;
    /** orderType */
    orderType?:
      | 'BuyGiftCard'
      | 'BuyIp'
      | 'BuyPluginPack'
      | 'BuyRpaVoucher'
      | 'BuyTkPack'
      | 'BuyTkshop'
      | 'BuyTraffic'
      | 'CashOut'
      | 'MakeupPriceDifference'
      | 'Merge'
      | 'OrderCancel'
      | 'PartnerBuyVoucher'
      | 'PartnerDraw'
      | 'PartnerRecharge'
      | 'PartnerRechargeCredit'
      | 'PartnerRenewRpaVoucher'
      | 'Present'
      | 'Recharge'
      | 'RechargeCredit'
      | 'Refund'
      | 'RenewIp'
      | 'RenewPluginPack'
      | 'RenewRpaVoucher'
      | 'RenewTkPack'
      | 'RenewTkshop'
      | 'UpgradeTkshop';
    /** orderTypes */
    orderTypes?: string;
    /** payStatus */
    payStatus?:
      | 'CANCELED'
      | 'Created'
      | 'Locked'
      | 'PAID'
      | 'PartialRefund'
      | 'REFUNDED'
      | 'WAIT_CONFIRM';
    /** payStatusList */
    payStatusList?: string;
    /** from */
    from?: string;
    /** to */
    to?: string;
  };

  type PaymentSummaryVo = {
    /** 可用余额（不包含锁定余额） */
    balance?: number;
    /** 可用花瓣 */
    credit?: number;
    /** 昨日花瓣消耗 */
    creditCost?: DailyCreditCost;
    /** 锁定余额 */
    lockBalance?: number;
    /** 每月预估费用 */
    monthCost?: number;
    /** 下次扣费日期 */
    nextDeductionDate?: string;
    /** 可开票金额 */
    notInvoicedAmount?: number;
    /** 已开启自动续费的平台IP数 */
    platformIpAutoRenewCount?: number;
    /** 已购买的平台IP数 */
    platformIpCount?: number;
    teamId?: number;
    thirtyDaysAverageCredit?: number;
    /** 可用代金券数量 */
    voucherCount?: number;
  };

  type paymentVoucherCardNumberByCardNumberGetParams = {
    /** cardNumber */
    cardNumber: string;
  };

  type paymentVouchersDeleteParams = {
    /** voucherIds */
    voucherIds: number;
  };

  type paymentVouchersGetParams = {
    /** pageNum */
    pageNum: number;
    /** pageSize */
    pageSize: number;
    /** voucherStatus */
    voucherStatus: 'Available' | 'ExpireSoon' | 'Invalid' | 'Used';
  };

  type paymentWechatpayNotifyByTeamIdPostParams = {
    /** teamId */
    teamId: number;
  };

  type paymentWechatpayQrcodeGetParams = {
    /** code_url */
    code_url: string;
  };

  type RefundRequest = {
    /** 实付金额是否原路返回，为空和false都表示将实付金额退到余额 */
    backtrack?: boolean;
    /** 余额退多少，不可超过订单实际余额支付额。为空表示全额退 */
    balanceAmount?: number;
    orderId?: number;
    /** 实付金额退多少，不可超过订单实际实付额。为空表示全额退 */
    realPayAmount?: number;
    remarks?: string;
    /** 是否退代金券，为空和true都表示退 */
    voucher?: boolean;
  };

  type remotePaymentCheckProducedPostParams = {
    /** orderId */
    orderId: number;
  };

  type remotePaymentCheckRefundAbleGetParams = {
    /** orderId */
    orderId: number;
  };

  type remotePaymentNotifyOfflineOrderCreatedPostParams = {
    /** orderId */
    orderId: number;
  };

  type remotePaymentReProduceOrderPostParams = {
    /** orderId */
    orderId: number;
  };

  type Resource = true;

  type TeamIpDto = {
    autoRenew?: boolean;
    cloudProvider?: string;
    cloudRegion?: string;
    createTime?: string;
    creatorId?: number;
    description?: string;
    directDownTraffic?: number;
    directUpTraffic?: number;
    domestic?: boolean;
    downTraffic?: number;
    dynamic?: boolean;
    eipId?: number;
    enableWhitelist?: boolean;
    forbiddenLongLatitude?: boolean;
    gatewayId?: number;
    goodsId?: number;
    goodsType?:
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher';
    id?: number;
    importType?: 'Platform' | 'User';
    invalidTime?: string;
    ip?: string;
    lastProbeTime?: string;
    latitude?: number;
    locale?: string;
    locationId?: number;
    longitude?: number;
    name?: string;
    networkType?: 'cloudIdc' | 'mobile' | 'proxyIdc' | 'residential' | 'unknown' | 'unknownIdc';
    operateStatus?: 'shared' | 'sharing' | 'sole' | 'transferring';
    originalTeam?: number;
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    pipeType?: 'None' | 'Proxy' | 'Tunnel' | 'TunnelFailToProxy';
    preferTransit?: number;
    probeError?: string;
    realIp?: string;
    refreshUrl?: string;
    remoteLogin?: boolean;
    renewPrice?: number;
    source?: string;
    speedLimit?: number;
    status?: 'Available' | 'Pending' | 'Unavailable';
    sticky?: boolean;
    teamId?: number;
    testingTime?: number;
    timezone?: string;
    traffic?: number;
    trafficCurrency?: 'CREDIT' | 'RMB' | 'USD';
    trafficPrice?: number;
    trafficUnlimited?: boolean;
    transitType?: 'Auto' | 'Direct' | 'Transit';
    tunnelTypes?: string;
    upTraffic?: number;
    valid?: boolean;
    validEndDate?: string;
    vpsId?: number;
  };

  type UpdateOrderPaymentRequest = {
    /** 已经勾选阅读和同意使用协议，没什么用 */
    agreement?: boolean;
    /** 余额抵扣金额 */
    balanceAmount?: number;
    /** 是否立即支付（点稍候支付该属性传false） */
    immediatePay?: boolean;
    orderId?: number;
    payType?: 'AliPay' | 'BalancePay' | 'BankPay' | 'WechatPay';
    /** 代金券抵扣金额，不得大于代金券余额 */
    voucherAmount?: number;
    /** 要使用的代金券id */
    voucherId?: number;
  };

  type VoucherVo = {
    amount?: number;
    /** 可用余额（不包含锁定余额） */
    balance?: number;
    cardNumber?: string;
    expireDate?: string;
    /** 是否已经失效 */
    expired?: boolean;
    id?: number;
    /** 剩余可扣次数 */
    life?: number;
    /** 锁定余额 */
    lockBalance?: number;
    /** 最小可用金额 */
    minOrderPrice?: number;
    /** see VoucherType.java */
    voucherType?: number;
  };

  type webhookPaymentCalcBuyIpPriceGetParams = {
    /** ip方案对应的商品id */
    goodsIds: number;
    /** 每个goods购买多少个，必须与goodsIds顺序一一对应 */
    counts: number;
    /** 购买多少个周期 */
    duration: number;
    /** 续费周期单位 */
    periodUnit:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
  };

  type WebResult = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultbigdecimal = {
    code?: number;
    data?: number;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultCalcPriceResponse = {
    code?: number;
    data?: CalcPriceResponse;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultCostSummaryVo = {
    code?: number;
    data?: CostSummaryVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultCreateOrderResponse = {
    code?: number;
    data?: CreateOrderResponse;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultGiftCardPackItemVo = {
    code?: number;
    data?: GiftCardPackItemVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultGoodsDto = {
    code?: number;
    data?: GoodsDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultint = {
    code?: number;
    data?: number;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultInvoiceSummaryVo = {
    code?: number;
    data?: InvoiceSummaryVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListDiscountsDto = {
    code?: number;
    data?: DiscountsDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListNotInvoicedOrderVo = {
    code?: number;
    data?: NotInvoicedOrderVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultMaplong = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultMaplong = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultMapstring = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultOrderDetailVo = {
    code?: number;
    data?: OrderDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultOrdersDto = {
    code?: number;
    data?: OrdersDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultBalanceInOutDetailVo = {
    code?: number;
    data?: PageResultBalanceInOutDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultCreditInOutDetailVo = {
    code?: number;
    data?: PageResultCreditInOutDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultInvoiceHistoryVo = {
    code?: number;
    data?: PageResultInvoiceHistoryVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultOrderDetailVo = {
    code?: number;
    data?: PageResultOrderDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultVoucherVo = {
    code?: number;
    data?: PageResultVoucherVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPaymentSummaryVo = {
    code?: number;
    data?: PaymentSummaryVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultstring = {
    code?: number;
    data?: string;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultVoucherVo = {
    code?: number;
    data?: VoucherVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };
}
