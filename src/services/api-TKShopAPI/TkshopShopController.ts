// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 建联达人数据 GET /api/tkshop/shop/${param0}/creatorInteractStat */
export async function tkshopShopByShopIdCreatorInteractStatGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopShopByShopIdCreatorInteractStatGetParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResultCreatorInteractStatVo>(
    `/api/tkshop/shop/${param0}/creatorInteractStat`,
    {
      method: 'GET',
      params: {
        ...queryParams,
      },
      ...(options || {}),
    },
  );
}

/** 达人统计 GET /api/tkshop/shop/${param0}/creatorStat */
export async function tkshopShopByShopIdCreatorStatGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopShopByShopIdCreatorStatGetParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResultShopCreatorStatVo>(`/api/tkshop/shop/${param0}/creatorStat`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 最近n天首次出单的达人 GET /api/tkshop/shop/${param0}/hasOrderCreator */
export async function tkshopShopByShopIdHasOrderCreatorGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopShopByShopIdHasOrderCreatorGetParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResultPageResultTkshopCreatorDto>(
    `/api/tkshop/shop/${param0}/hasOrderCreator`,
    {
      method: 'GET',
      params: {
        ...queryParams,
      },
      ...(options || {}),
    },
  );
}

/** 包含健康度的详情 GET /api/tkshop/shop/${param0}/healthDetail */
export async function tkshopShopByShopIdHealthDetailGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopShopByShopIdHealthDetailGetParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResultShopHealthVo>(`/api/tkshop/shop/${param0}/healthDetail`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 汇报TK店铺健康状态 PUT /api/tkshop/shop/${param0}/reportHealth */
export async function tkshopShopByShopIdReportHealthPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopShopByShopIdReportHealthPutParams,
  body: API.TkshopReportHealthRequest,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/tkshop/shop/${param0}/reportHealth`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 汇报TK店铺ID PUT /api/tkshop/shop/${param0}/reportTkShopNo */
export async function tkshopShopByIdReportTkShopNoPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopShopByIdReportTkShopNoPutParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/tkshop/shop/${param0}/reportTkShopNo`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 带货直播统计 GET /api/tkshop/shop/${param0}/shopLiveStat */
export async function tkshopShopByShopIdShopLiveStatGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopShopByShopIdShopLiveStatGetParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResultShopMediaStatVo>(`/api/tkshop/shop/${param0}/shopLiveStat`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 索样记录 GET /api/tkshop/shop/${param0}/shopSrStat */
export async function tkshopShopByShopIdShopSrStatGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopShopByShopIdShopSrStatGetParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResultShopSrStatVo>(`/api/tkshop/shop/${param0}/shopSrStat`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 带货视频统计 GET /api/tkshop/shop/${param0}/shopVideoStat */
export async function tkshopShopByShopIdShopVideoStatGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopShopByShopIdShopVideoStatGetParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResultShopMediaStatVo>(`/api/tkshop/shop/${param0}/shopVideoStat`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取店铺最后同步时间 POST /api/tkshop/shop/getLastSyncTime */
export async function tkshopShopGetLastSyncTimePost(
  body: API.CommonIdsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultMaplong>('/api/tkshop/shop/getLastSyncTime', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 加载tkshop店铺信息 POST /api/tkshop/shop/loadTkshopInfo */
export async function tkshopShopLoadTkshopInfoPost(
  body: API.CommonIdsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListTkshopShopVo>('/api/tkshop/shop/loadTkshopInfo', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 标记同步店铺结束 GET /api/tkshop/shop/markSyncDone */
export async function tkshopShopMarkSyncDoneGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopShopMarkSyncDoneGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/shop/markSyncDone', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** TKshop首页分页查询店铺 POST /api/tkshop/shop/page */
export async function tkshopShopPagePost(
  body: API.PageTkshopRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultShopHealthVo>('/api/tkshop/shop/page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 当前团队所有tk店铺的平台对象 GET /api/tkshop/shop/platforms */
export async function tkshopShopPlatformsGet(options?: { [key: string]: any }) {
  return request<API.WebResultListShopPlatformVo>('/api/tkshop/shop/platforms', {
    method: 'GET',
    ...(options || {}),
  });
}
