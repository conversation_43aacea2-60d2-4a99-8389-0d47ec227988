declare namespace API {
  type AddFriendshipVo = {
    /** 联系方式 */
    contact?: string;
    /** 类型 */
    contactType?: 'email' | 'fbMessenger' | 'line' | 'phone' | 'viber' | 'whatsapp' | 'zalo';
    /** 添加时产生的异常 */
    error?: string;
    /** 手机账号 */
    mobileAccount?: string;
    /** 手机ID */
    mobileId?: number;
    /** 联系方式状态 */
    status?: 'Error' | 'NotFound' | 'Ready' | 'Unknown';
    /** 达人或买家ID */
    targetId?: number;
  };

  type AddSampleRequestPolicyRequest = {
    approved?: boolean;
    conditionList?: SampleRequestConditionVo[];
    logicalCondition?: 'AND' | 'NOT' | 'OR';
    policyDesc?: string;
    policyName?: string;
  };

  type AddShopToPlanLayoutRequest = {
    /** 描述，暂时还用不上 */
    description?: string;
    layoutId?: number;
    shopId?: number;
  };

  type AddTaskDrawerItem = {
    accountId?: number;
    creatorId?: number;
    parameter?: string;
    rpaType?: 'Browser' | 'Extension' | 'IOS' | 'Mobile';
    taskKey?: string;
    taskName?: string;
    taskType?:
      | 'TS_IMChatByFilter'
      | 'TS_IMChatByHandle'
      | 'TS_SampleApprove'
      | 'TS_SendBuyerIMChat'
      | 'TS_SendLine'
      | 'TS_SendWhatsApp'
      | 'TS_SyncBuyerInfo'
      | 'TS_SyncCreator'
      | 'TS_SyncOrders'
      | 'TS_SyncSampleCreator'
      | 'TS_SyncShopInfo'
      | 'TS_SyncVideos'
      | 'TS_TargetPlanByFilter'
      | 'TS_TargetPlanByHandle'
      | 'TS_VideoADCode';
  };

  type AddTaskDrawerRequest = {
    items?: AddTaskDrawerItem[];
  };

  type AddTkshopInteractionRequest = {
    /** 交互的描述 */
    description?: string;
    /** 额外信息，转化成JSON总长度不要超过8000 */
    extraInfo?: Record<string, any>;
    /** 操作流程 */
    flowId?: number;
    /** 达人handle，必填 */
    handle?: string;
    /** 交互类型，必填 */
    interactType?:
      | 'add_friend'
      | 'collaborating'
      | 'has_order'
      | 'im_chat'
      | 'manual_remark'
      | 'sample_request'
      | 'send_email'
      | 'send_facebook'
      | 'send_line'
      | 'send_whatsapp'
      | 'send_zalo'
      | 'target_plan';
    /** 任务ID */
    jobId?: number;
    /** 达人所属区域 */
    region?: string;
    /** 店铺ID */
    shopId?: number;
    targetId?: number;
    /** 流程任务ID */
    taskId?: number;
    timestamp?: number;
  };

  type AddTkshopInteractionsRequest = {
    creators?: HandleRegionVo[];
    /** 交互的描述 */
    description?: string;
    /** 额外信息，转化成JSON总长度不要超过8000 */
    extraInfo?: Record<string, any>;
    /** 操作流程 */
    flowId?: number;
    /** 交互类型，必填 */
    interactType?:
      | 'add_friend'
      | 'collaborating'
      | 'has_order'
      | 'im_chat'
      | 'manual_remark'
      | 'sample_request'
      | 'send_email'
      | 'send_facebook'
      | 'send_line'
      | 'send_whatsapp'
      | 'send_zalo'
      | 'target_plan';
    /** 任务ID */
    jobId?: number;
    /** 店铺ID */
    shopId?: number;
    targetId?: number;
    /** 流程任务ID */
    taskId?: number;
    timestamp?: number;
  };

  type AddTkshopManualRemarkRequest = {
    ids?: number[];
    interactTime?: string;
    remark?: string;
  };

  type ApplyBestPracticeRequest = {
    /** 后续的触发计划，一个跟着一个，但不需要指定parentId */
    follows?: CreateTkshopFollowingPlanRequest[];
    /** 定时计划，不需要再冗余指定planGroupId */
    header?: chuangjianyigetkshopdingshijihua;
    /** 所属的分组 */
    planGroupId?: number;
  };

  type BankPayConfig = {
    accountName?: string;
    accountNumber?: string;
    bankName?: string;
  };

  type BatchSyncBuyerInfoRequest = {
    /** 必须要指定在哪个客户端上执行 */
    deviceId?: string;
    shopBuyers?: Record<string, any>;
  };

  type BatchSyncCreatorRequest = {
    /** 必须要指定在哪个客户端上执行 */
    deviceId?: string;
    /** shopId -> [ghCreatorId, ...] */
    shopCreators?: Record<string, any>;
  };

  type BatchSyncSampleCreatorRequest = {
    advanceSettings?: Record<string, any>;
    /** 必须要指定在哪个客户端上执行 */
    deviceId?: string;
    shopIds?: number[];
  };

  type BatchSyncVideoRequest = {
    advanceSettings?: Record<string, any>;
    /** 必须要指定在哪个客户端上执行 */
    deviceId?: string;
    shopId?: number;
    videoIds?: number[];
  };

  type BatchUpdateMessageConfigRequest = {
    config?: MobileAccountMessageConfig;
    mobileAccountIds?: number[];
  };

  type CalcTkshopPriceResponse = {
    /** 折扣,[0-1] */
    discount?: number;
    /** 打折减掉的金额(如果是打折的话) */
    discountAmount?: number;
    goodsId?: number;
    /** 记录每个item的价格及折扣或赠送信息，<key=goodsId, value=PriceInfo> */
    items?: Record<string, any>;
    /** 订单应付价(减掉了打折等信息) */
    payablePrice?: number;
    /** 赠送金额，目前只出现在购买花瓣 */
    presentAmount?: number;
    /** 订单总成本 */
    totalCost?: number;
    /** 订单总价(原价) */
    totalPrice?: number;
    unitPrice?: number;
  };

  type CheckInvitationVo = {
    planId?: string;
    updateTime?: number;
  };

  type chuangjianyigetkshopdingshijihua = {
    bizScene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
    /** 达人筛选条件，json格式 */
    creatorFilter?: string;
    /** 重复日期，只包含星期信息的表达式(具体参考自动流程计划) */
    cronExpression?: string;
    enabled?: boolean;
    /** 如果是随机执行，用-隔开开始时间和结束时间[start, start-end, start, ...] */
    expressions?: string[];
    jobType?:
      | 'AccountFollow'
      | 'AccountHealthCheck'
      | 'AccountMaintenance'
      | 'AccountMessageCheck'
      | 'KOL_LiveRewards'
      | 'ParentJob'
      | 'SendInvite'
      | 'SendPm'
      | 'SendUserCard'
      | 'ShareVideo'
      | 'SubJob'
      | 'SyncContactToPhone'
      | 'SyncContacts'
      | 'SyncCreator'
      | 'SyncLiveStream'
      | 'SyncPm'
      | 'TS_AddContacts'
      | 'TS_AddFriends'
      | 'TS_DemandPayment'
      | 'TS_IMChatByFilter'
      | 'TS_IMChatByHandle'
      | 'TS_LoginCheck'
      | 'TS_SampleApprove'
      | 'TS_SendBuyerIMChat'
      | 'TS_SendEmail'
      | 'TS_SendFacebook'
      | 'TS_SendLine'
      | 'TS_SendWhatsApp'
      | 'TS_SendZalo'
      | 'TS_SyncBuyerInfo'
      | 'TS_SyncCreator'
      | 'TS_SyncOrders'
      | 'TS_SyncProducts'
      | 'TS_SyncSampleCreator'
      | 'TS_SyncShopInfo'
      | 'TS_SyncVideos'
      | 'TS_TargetPlanByFilter'
      | 'TS_TargetPlanByHandle'
      | 'TS_TargetPlanClear'
      | 'TS_VideoADCode';
    params?: string;
    /** 所属的分组 */
    planGroupId?: number;
  };

  type CommonIdsRequest = {
    ids?: number[];
  };

  type ContactQueryVo = {
    accountId?: number;
    contact?: string;
    contactType?: string;
    /** 制定项为空 */
    empty?: boolean;
    status?: 'Error' | 'NotFound' | 'Ready' | 'Unknown';
  };

  type CreateBuyTkPackOrderResponse = {
    bankAccount?: string;
    bankAccountName?: string;
    bankName?: string;
    bankRemark?: string;
    createTime?: string;
    /** 扣减金额 */
    deductedPrice?: number;
    orderId?: number;
    /** 支付输出的内容 */
    payOutContent?: string;
    /** 支付输出的内容类型 */
    payOutType?: string;
    /** 如果不需要现金支付，该订单状态会直接变成已支付 */
    payStatus?:
      | 'CANCELED'
      | 'Created'
      | 'Locked'
      | 'PAID'
      | 'PartialRefund'
      | 'REFUNDED'
      | 'WAIT_CONFIRM';
    /** 支付方式 */
    payType?: 'AliPay' | 'BalancePay' | 'BankPay' | 'WechatPay';
    /** 需要现金支付的money */
    realPrice?: number;
    salesReduction?: number;
    serialNumber?: string;
    /** 为订单创建的临时团队 */
    team?: TeamDto;
  };

  type CreateBuyTkshopRequest = {
    /** 已经勾选阅读和同意使用协议，没什么用 */
    agreement?: boolean;
    /** 余额抵扣金额 */
    balanceAmount?: number;
    /** 折扣[0-1] */
    discount?: number;
    /** 优惠码 */
    distributionCode?: string;
    distributionInfo?: DistributionInfo;
    /** 购买时长，根据 periodUnit的值 有可能是月，周或天 */
    duration?: number;
    /** 是否立即支付（点稍候支付该属性传false） */
    immediatePay?: boolean;
    payType?: 'AliPay' | 'BalancePay' | 'BankPay' | 'WechatPay';
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    shopCount?: number;
    version?: 'TkshopEnterprise' | 'TkshopStandard';
    /** 代金券抵扣金额，不得大于代金券余额 */
    voucherAmount?: number;
    /** 要使用的代金券id */
    voucherId?: number;
  };

  type CreateOrderResponse = {
    bankAccount?: string;
    bankAccountName?: string;
    bankName?: string;
    bankRemark?: string;
    createTime?: string;
    /** 扣减金额 */
    deductedPrice?: number;
    orderId?: number;
    /** 支付输出的内容 */
    payOutContent?: string;
    /** 支付输出的内容类型 */
    payOutType?: string;
    /** 如果不需要现金支付，该订单状态会直接变成已支付 */
    payStatus?:
      | 'CANCELED'
      | 'Created'
      | 'Locked'
      | 'PAID'
      | 'PartialRefund'
      | 'REFUNDED'
      | 'WAIT_CONFIRM';
    /** 支付方式 */
    payType?: 'AliPay' | 'BalancePay' | 'BankPay' | 'WechatPay';
    /** 需要现金支付的money */
    realPrice?: number;
    salesReduction?: number;
    serialNumber?: string;
  };

  type CreatePlanChainLayoutRequest = {
    description?: string;
    deviceId?: string;
    name?: string;
  };

  type CreateTkshopFollowingPlanRequest = {
    bizScene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
    /** 达人筛选条件，json格式 */
    creatorFilter?: string;
    /** 如果是触发计划，触发的延时时间 */
    delayTime?: number;
    enabled?: boolean;
    jobType?:
      | 'AccountFollow'
      | 'AccountHealthCheck'
      | 'AccountMaintenance'
      | 'AccountMessageCheck'
      | 'KOL_LiveRewards'
      | 'ParentJob'
      | 'SendInvite'
      | 'SendPm'
      | 'SendUserCard'
      | 'ShareVideo'
      | 'SubJob'
      | 'SyncContactToPhone'
      | 'SyncContacts'
      | 'SyncCreator'
      | 'SyncLiveStream'
      | 'SyncPm'
      | 'TS_AddContacts'
      | 'TS_AddFriends'
      | 'TS_DemandPayment'
      | 'TS_IMChatByFilter'
      | 'TS_IMChatByHandle'
      | 'TS_LoginCheck'
      | 'TS_SampleApprove'
      | 'TS_SendBuyerIMChat'
      | 'TS_SendEmail'
      | 'TS_SendFacebook'
      | 'TS_SendLine'
      | 'TS_SendWhatsApp'
      | 'TS_SendZalo'
      | 'TS_SyncBuyerInfo'
      | 'TS_SyncCreator'
      | 'TS_SyncOrders'
      | 'TS_SyncProducts'
      | 'TS_SyncSampleCreator'
      | 'TS_SyncShopInfo'
      | 'TS_SyncVideos'
      | 'TS_TargetPlanByFilter'
      | 'TS_TargetPlanByHandle'
      | 'TS_TargetPlanClear'
      | 'TS_VideoADCode';
    params?: string;
    /** 如果是触发计划，父计划的ID */
    parentId?: number;
    triggerPolicy?: 'Always' | 'OnSuccess';
  };

  type CreatorDetailDocument = {
    alias?: string;
    avatar?: string;
    bio?: string;
    categories?: string[];
    /** 店铺端cid */
    cid?: string;
    contactMap?: Record<string, any>;
    contentType?: 'All' | 'LIVE' | 'Video';
    /** 媒体端creator_id */
    creatorId?: string;
    followerCnt?: number;
    handle?: string;
    region?: string;
    statMap?: Record<string, any>;
    status?: 'Collaborating' | 'HasOrder' | 'NotContacted' | 'Sent';
  };

  type CreatorInteractStatVo = {
    /** 合作中 */
    collaboratingCount?: number;
    /** 站内消息数量 */
    imChatCount?: number;
    /** 定向邀约数量 */
    targetPlanCount?: number;
  };

  type CreatorMediaVo = {
    endTimestamp?: number;
    estCommission?: number;
    gmv?: number;
    itemsSold?: number;
    mediaAvatar?: string;
    mediaId?: string;
    mediaName?: string;
    mediaUrl?: string;
    postTimestamp?: number;
    startTimestamp?: number;
    unit?: string;
  };

  type CreatorProductVo = {
    estCommission?: number;
    gmv?: number;
    itemsSold?: number;
    productAvatar?: string;
    productName?: string;
    productNo?: string;
    unit?: string;
  };

  type CreatorShopDocument = {
    /** 分析模块creator_id */
    analysisCreatorId?: string;
    avatar?: string;
    /** 店铺端cid */
    cid?: string;
    /** 媒体端creator_id */
    creatorId?: string;
    handle?: string;
    lives?: CreatorMediaVo[];
    products?: CreatorProductVo[];
    region?: string;
    videos?: CreatorMediaVo[];
  };

  type CreatorStatRangeVo = {
    dimension?: string;
    from?: number;
    to?: number;
  };

  type CreatorStatVo = {
    unit?: string;
    value?: number;
  };

  type CreatorVideoDocument = {
    /** 评论数 */
    commentCnt?: number;
    /** 媒体端creator_id */
    creatorId?: string;
    /** 时长：秒 */
    duration?: number;
    /** 收藏数 */
    favoriteCnt?: number;
    handle?: string;
    /** 点赞数 */
    likeCnt?: number;
    /** 封面图片URL */
    mediaAvatar?: string;
    /** 视频ID */
    mediaId?: string;
    /** 视频名称 */
    mediaName?: string;
    /** 播放地址 */
    mediaUrl?: string;
    postTimestamp?: number;
    region?: string;
    /** 转发数 */
    retweetCnt?: number;
    /** 播放数 */
    viewCnt?: number;
    /** 观众数 */
    viewerCnt?: number;
  };

  type DemandPaymentRequest = {
    advanceSettings?: Record<string, any>;
    /** 必须要指定在哪个客户端上执行 */
    deviceId?: string;
    shopId?: number;
  };

  type DiscountsVo = {
    /** 赠送数量或折扣百分比或阶梯折扣百分比 */
    amount?: number;
    /** 打折code */
    discountCode?: string;
    /** 打折还是赠送 */
    discountType?: 'Discount' | 'LadderPrice' | 'Present';
    /** 周期或数量单位 */
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    /** 备注 */
    remarks?: string;
    /** 期数或数量 */
    threshold?: number;
  };

  type DistributionCodeDto = {
    amount?: number;
    code?: string;
    createTime?: string;
    description?: string;
    discountId?: number;
    distributionType?: 'Deduction' | 'Discount' | 'Official';
    distributor?: number;
    goodsType?:
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher';
    id?: number;
    limited?: boolean;
    name?: string;
    systemDefault?: boolean;
    usageCount?: number;
    usedCount?: number;
    valid?: boolean;
    validDays?: number;
  };

  type DistributionInfo = {
    code?: DistributionCodeDto;
    deductedPrice?: number;
    drawPrice?: number;
  };

  type DurationPriceVo = {
    duration?: number;
    rate?: number;
  };

  type ExpressionSymbolVo = {
    desc?: string;
    /** 仅支持数字 */
    onlyNumber?: boolean;
    /** 不需要比较，单值 */
    single?: boolean;
    symbol?:
      | 'contains'
      | 'eq'
      | 'ge'
      | 'gt'
      | 'isEmpty'
      | 'le'
      | 'lt'
      | 'ne'
      | 'notContains'
      | 'notEmpty';
  };

  type FBUserProfile = {
    fbid?: string;
  };

  type FilterBasicSyncedRequest = {
    /** 若干天内 */
    days?: number;
    /** 达人列表 */
    handles?: string[];
  };

  type FindTkshopCreatorRequest = {
    alias?: string;
    basicSyncTimeBeforeDays?: number;
    /** 基础信息更新时间from */
    basicSyncTimeFrom?: string;
    basicSyncTimeLastDays?: number;
    /** 基础信息更新时间to */
    basicSyncTimeTo?: string;
    categoryIds?: number[];
    /** 联系方式：全部为空 */
    contactAllEmpty?: boolean;
    /** 联系方式：至少一项不为空 */
    contactNotAllEmpty?: boolean;
    contacts?: ContactQueryVo[];
    /** 包含指定标签。取false时，tagLc只能是OR */
    containsTag?: boolean;
    contentType?: 'All' | 'LIVE' | 'Video';
    createTimeBeforeDays?: number;
    createTimeFrom?: string;
    createTimeLastDays?: number;
    createTimeTo?: string;
    deleting?: boolean;
    filterByFavorite?: boolean;
    followerCntFrom?: number;
    followerCntTo?: number;
    handle?: string;
    /** 勾选已合作的店铺 */
    hasCollaborating?: boolean;
    /** 勾选交互过的店铺 */
    hasInteraction?: boolean;
    hasNewMsg?: boolean;
    hasResponsibleUser?: boolean;
    /** 交互店铺ID */
    interactShopId?: number;
    /** 包含以下沟通记录 */
    interactTypes?: (
      | 'add_friend'
      | 'collaborating'
      | 'has_order'
      | 'im_chat'
      | 'manual_remark'
      | 'sample_request'
      | 'send_email'
      | 'send_facebook'
      | 'send_line'
      | 'send_whatsapp'
      | 'send_zalo'
      | 'target_plan'
    )[];
    /** 沟通记录包含：仅支持对手动记录、发送的即时消息内容进行检索 */
    interactionText?: string;
    lastSyncTimeBeforeDays?: number;
    lastSyncTimeFrom?: string;
    lastSyncTimeLastDays?: number;
    lastSyncTimeTo?: string;
    limit?: number;
    liveStartTimeBeforeDays?: number;
    liveStartTimeFrom?: string;
    liveStartTimeLastDays?: number;
    liveStartTimeTo?: string;
    notStatus?: 'Collaborating' | 'HasOrder' | 'NotContacted' | 'Sent';
    /** 带货商品ID */
    productNo?: string;
    query?: string;
    region?: string;
    regions?: string[];
    responsibleUserId?: number;
    roiFrom?: number;
    roiTo?: number;
    /** 合作店铺ID */
    shopId?: number;
    statRanges?: CreatorStatRangeVo[];
    statusList?: ('Collaborating' | 'HasOrder' | 'NotContacted' | 'Sent')[];
    tagIds?: number[];
    /** 标签模版 */
    tagKeys?: string[];
    /** 标签的逻辑条件，支持OR|AND */
    tagLc?: 'AND' | 'NOT' | 'OR';
    /** 沟通文本代表的沟通类型 */
    textInteractTypes?: (
      | 'add_friend'
      | 'collaborating'
      | 'has_order'
      | 'im_chat'
      | 'manual_remark'
      | 'sample_request'
      | 'send_email'
      | 'send_facebook'
      | 'send_line'
      | 'send_whatsapp'
      | 'send_zalo'
      | 'target_plan'
    )[];
    totalCommissionFrom?: number;
    totalCommissionTo?: number;
    totalGmvFrom?: number;
    totalGmvTo?: number;
    totalItemsSoldFrom?: number;
    totalItemsSoldTo?: number;
    totalProductsFrom?: number;
    totalProductsTo?: number;
    videoPostTimeBeforeDays?: number;
    videoPostTimeFrom?: string;
    videoPostTimeLastDays?: number;
    videoPostTimeTo?: string;
  };

  type FindTkshopGlobalCreatorRequest = {
    alias?: string;
    categoryIds?: number[];
    /** 联系方式：全部为空 */
    contactAllEmpty?: boolean;
    /** 联系方式：至少一项不为空 */
    contactNotAllEmpty?: boolean;
    contacts?: ContactQueryVo[];
    createTimeFrom?: string;
    createTimeTo?: string;
    followerCntFrom?: number;
    followerCntTo?: number;
    handle?: string;
    lastSyncTimeBeforeDays?: number;
    lastSyncTimeFrom?: string;
    lastSyncTimeLastDays?: number;
    lastSyncTimeTo?: string;
    limit?: number;
    query?: string;
    regions?: string[];
    statRanges?: CreatorStatRangeVo[];
  };

  type GhCreatorContactRequest = {
    contactType?: 'email' | 'fbMessenger' | 'line' | 'phone' | 'viber' | 'whatsapp' | 'zalo';
    ids?: number[];
  };

  type GhCreatorHandleRequest = {
    handles?: string[];
  };

  type GhJobDto = {
    /** 什么类型的达人 */
    bizScene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
    clientId?: string;
    /** 该条任务创建时间 */
    createTime?: string;
    /** 创建者id */
    creatorId?: number;
    /** 创建者昵称 */
    creatorName?: string;
    description?: string;
    /** 执行设备 */
    device?: LoginDeviceDto;
    /** 执行设备的名称 */
    deviceName?: string;
    /** 执行结束时间 */
    executeEndTime?: string;
    /** 开始执行时间 */
    executeTime?: string;
    /** 主播id */
    ghCreatorId?: number;
    /** 主播昵称 */
    ghCreatorName?: string;
    ghPlanId?: number;
    /** 公会平台类型，tk、抖音、小红书 */
    ghPlatformType?:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
    ghScheduleType?: 'GhBackstage' | 'Mobile' | 'Normal' | 'Official' | 'Union' | 'Unknown';
    id?: number;
    jobType?:
      | 'AccountFollow'
      | 'AccountHealthCheck'
      | 'AccountMaintenance'
      | 'AccountMessageCheck'
      | 'KOL_LiveRewards'
      | 'ParentJob'
      | 'SendInvite'
      | 'SendPm'
      | 'SendUserCard'
      | 'ShareVideo'
      | 'SubJob'
      | 'SyncContactToPhone'
      | 'SyncContacts'
      | 'SyncCreator'
      | 'SyncLiveStream'
      | 'SyncPm'
      | 'TS_AddContacts'
      | 'TS_AddFriends'
      | 'TS_DemandPayment'
      | 'TS_IMChatByFilter'
      | 'TS_IMChatByHandle'
      | 'TS_LoginCheck'
      | 'TS_SampleApprove'
      | 'TS_SendBuyerIMChat'
      | 'TS_SendEmail'
      | 'TS_SendFacebook'
      | 'TS_SendLine'
      | 'TS_SendWhatsApp'
      | 'TS_SendZalo'
      | 'TS_SyncBuyerInfo'
      | 'TS_SyncCreator'
      | 'TS_SyncOrders'
      | 'TS_SyncProducts'
      | 'TS_SyncSampleCreator'
      | 'TS_SyncShopInfo'
      | 'TS_SyncVideos'
      | 'TS_TargetPlanByFilter'
      | 'TS_TargetPlanByHandle'
      | 'TS_TargetPlanClear'
      | 'TS_VideoADCode';
    /** 任务参数，json格式 */
    params?: string;
    planRunId?: string;
    rpaTaskId?: number;
    rpaType?: 'Browser' | 'Extension' | 'IOS' | 'Mobile';
    /** 用来执行该job的分身 */
    shopId?: number;
    /** 用来执行该job的分身名 */
    shopName?: string;
    /** job状态 */
    status?: 'Cancelled' | 'Failed' | 'Pending' | 'Running' | 'Scheduled' | 'Succeed';
    /** 子任务个数，当任务为信息更新/私信更新时，根据该字段来显示 xxx个主播  */
    subCounts?: number;
    teamId?: number;
  };

  type GhRpaBrowserPolicy = {
    /** 当账号的浏览器已打开时的策略：stop | reopen | reuse */
    caseBrowserOpened?: string;
    /** 流程结束后是否关闭账号浏览器：close | keep | closeRpaBrowser */
    closeBrowserOnEnd?: string;
    /** 是否显示鼠标轨迹 */
    showMouseTrack?: boolean;
    snapshot?: 'Node' | 'Not' | 'OnFail';
  };

  type HandleRegionVo = {
    handle?: string;
    region?: string;
  };

  type HandlesRequest = {
    /** 达人列表 */
    handles?: string[];
  };

  type HasInteractionVo = {
    count?: number;
    handle?: string;
  };

  type IdHandleVo = {
    handle?: string;
    id?: number;
  };

  type ImportGlobalCreatorByIdsRequest = {
    ids?: number[];
    tags?: string[];
  };

  type ImportTkshopGlobalCreatorRequest = {
    alias?: string;
    categoryIds?: number[];
    /** 联系方式：全部为空 */
    contactAllEmpty?: boolean;
    /** 联系方式：至少一项不为空 */
    contactNotAllEmpty?: boolean;
    contacts?: ContactQueryVo[];
    createTimeFrom?: string;
    createTimeTo?: string;
    followerCntFrom?: number;
    followerCntTo?: number;
    handle?: string;
    lastSyncTimeBeforeDays?: number;
    lastSyncTimeFrom?: string;
    lastSyncTimeLastDays?: number;
    lastSyncTimeTo?: string;
    limit?: number;
    pageNum?: number;
    pageSize?: number;
    query?: string;
    regions?: string[];
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
    statRanges?: CreatorStatRangeVo[];
    tags?: string[];
  };

  type InvitationCreatorDocument = {
    creatorId?: string;
    handle?: string;
    region?: string;
  };

  type InvitationDocument = {
    creatorCnt?: number;
    creators?: InvitationCreatorDocument[];
    endTime?: number;
    message?: string;
    name?: string;
    planId?: string;
    productCnt?: number;
    products?: InvitationProductDocument[];
    startTime?: number;
    updateTime?: number;
  };

  type InvitationProductDocument = {
    productAvatar?: string;
    productName?: string;
    productNo?: string;
  };

  type IpPoolDto = {
    allocateStrategy?: 'ByLessTraffic' | 'ByLoad' | 'ByOrder' | 'ByRandom';
    capacity?: number;
    connectTransits?: string;
    createTime?: string;
    creator?: number;
    description?: string;
    domestic?: boolean;
    enableWhitelist?: boolean;
    exclusive?: boolean;
    id?: number;
    lastApiTime?: string;
    lifetime?: number;
    locationId?: number;
    minApiInterval?: number;
    minApiNum?: number;
    name?: string;
    produceFromTransit?: boolean;
    produceStrategy?: 'ProduceOnEmpty' | 'ProduceOnHand' | 'ProduceOnRequest';
    produced?: number;
    provider?: string;
    releaseStrategy?: 'Discard' | 'Reuse';
    teamId?: number;
    transitType?: 'Auto' | 'Direct' | 'Transit';
    transits?: string;
    tunnelTypes?: string;
  };

  type ItemPriceInfo = {
    costPrice?: number;
    /** 当前过期时间 */
    currentValidEndTime?: string;
    discount?: DiscountsVo;
    /** 打折减掉的金额，如果是打折的话 */
    discountAmount?: number;
    goodsId?: number;
    /** 应付价格 */
    payablePrice?: number;
    /** 赠送数量，如果是赠送的话。目前只出现在购买花瓣 */
    presentAmount?: number;
    /** item总价 */
    price?: number;
    /** 续费后到期时间 */
    validEndTime?: string;
  };

  type LadderPriceRange = {
    /** 阶梯折扣百分比（=原价*amount/100） */
    amount?: number;
    /** 超过特定数量 */
    threshold?: number;
  };

  type LadderPriceVo = {
    /** 阶梯报价 */
    price?: number;
    /** 特定的数量 */
    quantity?: number;
  };

  type LoginDeviceDto = {
    appId?: string;
    appVersion?: string;
    clientIp?: string;
    clientLocation?: number;
    cpus?: number;
    createTime?: string;
    deviceId?: string;
    deviceType?: 'App' | 'Browser' | 'Extension' | 'HYRuntime' | 'RpaExecutor';
    domestic?: boolean;
    hostName?: string;
    id?: number;
    ipDataId?: number;
    lastActiveTime?: string;
    lastCity?: string;
    lastLogTime?: string;
    lastRemoteIp?: string;
    lastUserId?: number;
    logUrl?: string;
    mem?: number;
    online?: boolean;
    osName?: string;
    teamId?: number;
    userAgent?: string;
    version?: string;
  };

  type MarkProductLiveRequest = {
    /** Live商品列表 */
    productNos?: string[];
    /** 分身ID */
    shopId?: number;
  };

  type MobileAccountMessageConfig = {
    /** 每批次添加好友的时间间隔，单位分钟 */
    addFriendsInterval?: number;
    /** 每日最多添加的好友数量 */
    maxDayFriends?: number;
    /** 每日最多发送的即时消息数量 */
    maxDayPm?: number;
    /** 单账号每批次最多添加的好友数量 */
    maxTaskFriends?: number;
    /** 每批次最多发送的即时消息数量 */
    maxTaskPm?: number;
    /** 每批次发送即时消息的时间间隔，单位分钟 */
    sendPmInterval?: number;
  };

  type OrderDetailVo = {
    /** 是否为自动订单，例如自动续费订单 */
    automatic?: boolean;
    /** 余额抵扣金额 */
    balanceAmount?: number;
    /** 如果是银行卡支付，则包含银行卡信息 */
    bankPayConfig?: BankPayConfig;
    cashPayType?: 'AliPay' | 'BalancePay' | 'BankPay' | 'WechatPay';
    createTime?: string;
    creatorId?: number;
    discountReason?: string;
    earnedPartner?: number;
    /** 钱款入账的代理商信息 */
    earnedPartnerDto?: PartnerDto;
    id?: number;
    /** 订单锁定时间;订单锁定之后60分钟不支付会被取消掉 */
    lockTime?: string;
    nickname?: string;
    orderType?:
      | 'BuyGiftCard'
      | 'BuyIp'
      | 'BuyPluginPack'
      | 'BuyRpaVoucher'
      | 'BuyTkPack'
      | 'BuyTkshop'
      | 'BuyTraffic'
      | 'CashOut'
      | 'MakeupPriceDifference'
      | 'Merge'
      | 'OrderCancel'
      | 'PartnerBuyVoucher'
      | 'PartnerDraw'
      | 'PartnerRecharge'
      | 'PartnerRechargeCredit'
      | 'PartnerRenewRpaVoucher'
      | 'Present'
      | 'Recharge'
      | 'RechargeCredit'
      | 'Refund'
      | 'RenewIp'
      | 'RenewPluginPack'
      | 'RenewRpaVoucher'
      | 'RenewTkPack'
      | 'RenewTkshop'
      | 'UpgradeTkshop';
    /** 如果父订单不为空说明该订单被选中合并支付了 */
    parentOrderId?: number;
    payStatus?:
      | 'CANCELED'
      | 'Created'
      | 'Locked'
      | 'PAID'
      | 'PartialRefund'
      | 'REFUNDED'
      | 'WAIT_CONFIRM';
    /** 订单应付总额，比如说买一年打8折或者销售改价之后的价格 */
    payablePrice?: number;
    /** 充值赠送金额;只在充值订单有效 */
    presentAmount?: number;
    /** 生产备注 */
    productionRemarks?: string;
    /** 生产状态 */
    productionStatus?:
      | 'Finished'
      | 'NotStart'
      | 'ProduceError'
      | 'Producing'
      | 'ReFunded'
      | 'RefundError'
      | 'Refunding'
      | 'WaitReFund';
    /** 实付金额，即现金支付金额;可开票金额以此为依据 */
    realPrice?: number;
    /** 销售改价折现 */
    salesReduction?: number;
    serialNumber?: string;
    /** 订单总额 */
    totalPrice?: number;
    /** 代金券抵扣金额 */
    voucherAmount?: number;
    /** 代金券卡号 */
    voucherCardNumber?: string;
  };

  type OrderItemDto = {
    count?: number;
    discountReason?: string;
    extraInfo?: string;
    goodsId?: number;
    goodsPeriodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    goodsType?:
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher';
    id?: number;
    itemPrice?: number;
    orderId?: number;
    productionRemarks?: string;
    productionStatus?:
      | 'Finished'
      | 'NotStart'
      | 'ProduceError'
      | 'Producing'
      | 'ReFunded'
      | 'RefundError'
      | 'Refunding'
      | 'WaitReFund';
    resourceId?: number;
    resourceName?: string;
    resourceType?:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    teamId?: number;
  };

  type OrderProductStatVo = {
    amount?: number;
    currency?: string;
    productAvatar?: string;
    productName?: string;
    productNo?: string;
    quantity?: number;
    shopId?: number;
    unit?: string;
  };

  type OrdersPmQueryVo = {
    /** 订单同步时间和播放量更新时间相差大于（小时） */
    abnormalSyncTimeDiff?: number;
    /** 播放量小于 */
    abnormalViewCnt?: number;
    /** 较差区间的开始值 */
    badRate?: number;
    /** 优秀区间的开始值 */
    goodRate?: number;
  };

  type OrderStatusStatVo = {
    amount?: number;
    count?: number;
    currency?: string;
    status?: 'cancellation' | 'completed' | 'fail_delivery' | 'pending' | 'shipped' | 'to_ship';
    unit?: string;
  };

  type PageLiveRequest = {
    creatorId?: number;
    estCommissionFrom?: number;
    estCommissionTo?: number;
    gmvFrom?: number;
    gmvTo?: number;
    /** 达人ID */
    handle?: string;
    /** 视频是否有索样记录 */
    hasSampleRequest?: boolean;
    itemsSoldFrom?: number;
    itemsSoldTo?: number;
    pageNum?: number;
    pageSize?: number;
    productNo?: string;
    /** 标题和备注或MediaId或达人handle前缀 */
    query?: string;
    searchShop?: boolean;
    shopIds?: number[];
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
    /** 视频的索样记录ID是什么 */
    srApplyId?: string;
    startTimeBeforeDays?: number;
    startTimeFrom?: string;
    startTimeLastDays?: number;
    startTimeTo?: string;
  };

  type PageResultSampleRequestShopBriefVo = {
    current?: number;
    list?: SampleRequestShopBriefVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultShopHealthVo = {
    current?: number;
    list?: ShopHealthVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultTkshopBuyerDetailVo = {
    current?: number;
    list?: TkshopBuyerDetailVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultTkshopBuyerInteractionDto = {
    current?: number;
    list?: TkshopBuyerInteractionDto[];
    pageSize?: number;
    total?: number;
  };

  type PageResultTkshopCreatorDetailVo = {
    current?: number;
    list?: TkshopCreatorDetailVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultTkshopCreatorDto = {
    current?: number;
    list?: TkshopCreatorDto[];
    pageSize?: number;
    total?: number;
  };

  type PageResultTkshopCreatorLiveVo = {
    current?: number;
    list?: TkshopCreatorLiveVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultTkshopCreatorProductDto = {
    current?: number;
    list?: TkshopCreatorProductDto[];
    pageSize?: number;
    total?: number;
  };

  type PageResultTkshopCreatorProductVo = {
    current?: number;
    list?: TkshopCreatorProductVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultTkshopCreatorVideoVo = {
    current?: number;
    list?: TkshopCreatorVideoVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultTkshopGlobalCreatorDetailVo = {
    current?: number;
    list?: TkshopGlobalCreatorDetailVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultTkshopInteractionDto = {
    current?: number;
    list?: TkshopInteractionDto[];
    pageSize?: number;
    total?: number;
  };

  type PageResultTkshopLiveVo = {
    current?: number;
    list?: TkshopLiveVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultTkshopOrderVo = {
    current?: number;
    list?: TkshopOrderVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultTkshopProductDto = {
    current?: number;
    list?: TkshopProductDto[];
    pageSize?: number;
    total?: number;
  };

  type PageResultTkshopSampleRequestAuditVo = {
    current?: number;
    list?: TkshopSampleRequestAuditVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultTkshopSampleRequestDetailVo = {
    current?: number;
    list?: TkshopSampleRequestDetailVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultTkshopTaskDrawerVo = {
    current?: number;
    list?: TkshopTaskDrawerVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultTkshopVideoVo = {
    current?: number;
    list?: TkshopVideoVo[];
    pageSize?: number;
    total?: number;
  };

  type PageSampleRequestRequest = {
    applyId?: string;
    creatorId?: number;
    followerCntFrom?: number;
    followerCntTo?: number;
    includeMedia?: boolean;
    needCreatorDetail?: boolean;
    needProductDetail?: boolean;
    /** 排除的状态 */
    notStatus?:
      | 'Cancelled'
      | 'Completed'
      | 'Expired'
      | 'InProgress'
      | 'ReadyToShip'
      | 'Reject'
      | 'Shipped'
      | 'ToReview';
    pageNum?: number;
    pageSize?: number;
    query?: string;
    region?: string;
    shopId?: number;
    shopIds?: number[];
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
    status?:
      | 'Cancelled'
      | 'Completed'
      | 'Expired'
      | 'InProgress'
      | 'ReadyToShip'
      | 'Reject'
      | 'Shipped'
      | 'ToReview';
    statusList?: (
      | 'Cancelled'
      | 'Completed'
      | 'Expired'
      | 'InProgress'
      | 'ReadyToShip'
      | 'Reject'
      | 'Shipped'
      | 'ToReview'
    )[];
    tagIds?: number[];
    /** 标签的逻辑条件，支持OR|AND */
    tagLc?: 'AND' | 'NOT' | 'OR';
  };

  type PageTkshopBuyerRequest = {
    /** 带货达人ID */
    commissionHandle?: string;
    /** 联系方式：全部为空 */
    contactAllEmpty?: boolean;
    /** 联系方式：至少一项不为空 */
    contactNotAllEmpty?: boolean;
    contacts?: ContactQueryVo[];
    /** 包含指定标签。取false时，tagLc只能是OR */
    containsTag?: boolean;
    firstBuyTimeBeforeDays?: number;
    firstBuyTimeFrom?: string;
    firstBuyTimeLastDays?: number;
    firstBuyTimeTo?: string;
    /** 包含以下沟通记录 */
    interactTypes?: (
      | 'add_contact'
      | 'add_friend'
      | 'im_chat'
      | 'manual_remark'
      | 'send_email'
      | 'send_facebook'
      | 'send_line'
      | 'send_whatsapp'
      | 'send_zalo'
    )[];
    /** 沟通记录包含：仅支持对手动记录、发送的即时消息内容进行检索 */
    interactionText?: string;
    lastBuyTimeBeforeDays?: number;
    lastBuyTimeFrom?: string;
    lastBuyTimeLastDays?: number;
    lastBuyTimeTo?: string;
    limit?: number;
    orderAmountFrom?: number;
    orderAmountTo?: number;
    orderCountFrom?: number;
    orderCountTo?: number;
    pageNum?: number;
    pageSize?: number;
    /** 购买的产品ID */
    productNo?: string;
    query?: string;
    regions?: string[];
    shopId?: number;
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
    tagIds?: number[];
    /** 标签模版 */
    tagKeys?: string[];
    /** 标签的逻辑条件，支持OR|AND */
    tagLc?: 'AND' | 'NOT' | 'OR';
    /** 沟通文本代表的沟通类型 */
    textInteractTypes?: (
      | 'add_contact'
      | 'add_friend'
      | 'im_chat'
      | 'manual_remark'
      | 'send_email'
      | 'send_facebook'
      | 'send_line'
      | 'send_whatsapp'
      | 'send_zalo'
    )[];
  };

  type PageTkshopCreatorRequest = {
    alias?: string;
    basicSyncTimeBeforeDays?: number;
    /** 基础信息更新时间from */
    basicSyncTimeFrom?: string;
    basicSyncTimeLastDays?: number;
    /** 基础信息更新时间to */
    basicSyncTimeTo?: string;
    categoryIds?: number[];
    /** 联系方式：全部为空 */
    contactAllEmpty?: boolean;
    /** 联系方式：至少一项不为空 */
    contactNotAllEmpty?: boolean;
    contacts?: ContactQueryVo[];
    /** 包含指定标签。取false时，tagLc只能是OR */
    containsTag?: boolean;
    contentType?: 'All' | 'LIVE' | 'Video';
    createTimeBeforeDays?: number;
    createTimeFrom?: string;
    createTimeLastDays?: number;
    createTimeTo?: string;
    deleting?: boolean;
    filterByFavorite?: boolean;
    followerCntFrom?: number;
    followerCntTo?: number;
    handle?: string;
    /** 勾选已合作的店铺 */
    hasCollaborating?: boolean;
    /** 勾选交互过的店铺 */
    hasInteraction?: boolean;
    hasNewMsg?: boolean;
    hasResponsibleUser?: boolean;
    /** 交互店铺ID */
    interactShopId?: number;
    /** 包含以下沟通记录 */
    interactTypes?: (
      | 'add_friend'
      | 'collaborating'
      | 'has_order'
      | 'im_chat'
      | 'manual_remark'
      | 'sample_request'
      | 'send_email'
      | 'send_facebook'
      | 'send_line'
      | 'send_whatsapp'
      | 'send_zalo'
      | 'target_plan'
    )[];
    /** 沟通记录包含：仅支持对手动记录、发送的即时消息内容进行检索 */
    interactionText?: string;
    lastSyncTimeBeforeDays?: number;
    lastSyncTimeFrom?: string;
    lastSyncTimeLastDays?: number;
    lastSyncTimeTo?: string;
    limit?: number;
    liveStartTimeBeforeDays?: number;
    liveStartTimeFrom?: string;
    liveStartTimeLastDays?: number;
    liveStartTimeTo?: string;
    notStatus?: 'Collaborating' | 'HasOrder' | 'NotContacted' | 'Sent';
    pageNum?: number;
    pageSize?: number;
    /** 带货商品ID */
    productNo?: string;
    query?: string;
    region?: string;
    regions?: string[];
    responsibleUserId?: number;
    roiFrom?: number;
    roiTo?: number;
    /** 合作店铺ID */
    shopId?: number;
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
    statRanges?: CreatorStatRangeVo[];
    statusList?: ('Collaborating' | 'HasOrder' | 'NotContacted' | 'Sent')[];
    tagIds?: number[];
    /** 标签模版 */
    tagKeys?: string[];
    /** 标签的逻辑条件，支持OR|AND */
    tagLc?: 'AND' | 'NOT' | 'OR';
    /** 沟通文本代表的沟通类型 */
    textInteractTypes?: (
      | 'add_friend'
      | 'collaborating'
      | 'has_order'
      | 'im_chat'
      | 'manual_remark'
      | 'sample_request'
      | 'send_email'
      | 'send_facebook'
      | 'send_line'
      | 'send_whatsapp'
      | 'send_zalo'
      | 'target_plan'
    )[];
    totalCommissionFrom?: number;
    totalCommissionTo?: number;
    totalGmvFrom?: number;
    totalGmvTo?: number;
    totalItemsSoldFrom?: number;
    totalItemsSoldTo?: number;
    totalProductsFrom?: number;
    totalProductsTo?: number;
    videoPostTimeBeforeDays?: number;
    videoPostTimeFrom?: string;
    videoPostTimeLastDays?: number;
    videoPostTimeTo?: string;
  };

  type PageTkshopGlobalCreatorRequest = {
    alias?: string;
    categoryIds?: number[];
    /** 联系方式：全部为空 */
    contactAllEmpty?: boolean;
    /** 联系方式：至少一项不为空 */
    contactNotAllEmpty?: boolean;
    contacts?: ContactQueryVo[];
    createTimeFrom?: string;
    createTimeTo?: string;
    followerCntFrom?: number;
    followerCntTo?: number;
    handle?: string;
    lastSyncTimeBeforeDays?: number;
    lastSyncTimeFrom?: string;
    lastSyncTimeLastDays?: number;
    lastSyncTimeTo?: string;
    limit?: number;
    pageNum?: number;
    pageSize?: number;
    query?: string;
    regions?: string[];
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
    statRanges?: CreatorStatRangeVo[];
  };

  type PageTkshopOrderRequest = {
    amountFrom?: number;
    amountTo?: number;
    /** 买家id */
    buyerHandle?: string;
    /** 买家备注 */
    buyerNote?: string;
    /** 带货达人id */
    commissionHandle?: string;
    /** 买家联系方式：全部为空 */
    contactAllEmpty?: boolean;
    /** 买家联系方式：至少一项不为空 */
    contactNotAllEmpty?: boolean;
    /** 买家联系方式信息 */
    contacts?: ContactQueryVo[];
    cosFeeFrom?: number;
    cosFeeTo?: number;
    cosRatioFrom?: number;
    cosRatioTo?: number;
    createTimeBeforeDays?: number;
    /** 创建时间开始 */
    createTimeFrom?: string;
    createTimeLastDays?: number;
    /** 创建时间截止 */
    createTimeTo?: string;
    /** 是否索样订单 */
    freeSample?: boolean;
    /** 是否有带货达人 */
    hasCommissionHandle?: boolean;
    /** 媒体id */
    mediaId?: string;
    /** 选中订单列表 */
    orderIds?: number[];
    /** 商品ID */
    orderNo?: string;
    pageNum?: number;
    pageSize?: number;
    productNo?: string;
    /** 带货来源 */
    promotionType?:
      | 'AffiliateProductPage'
      | 'ExternalTrafficProgram'
      | 'Livestream'
      | 'Showcase'
      | 'Video';
    quantityFrom?: number;
    quantityTo?: number;
    /** 订单ID/备注 */
    query?: string;
    /** 指定店铺 */
    shopIds?: number[];
    sortField?:
      | 'amount'
      | 'cosFee'
      | 'createTime'
      | 'freeSample'
      | 'id'
      | 'quantity'
      | 'shopId'
      | 'status';
    sortOrder?: 'asc' | 'desc';
    /** 订单状态 */
    status?: 'cancellation' | 'completed' | 'fail_delivery' | 'pending' | 'shipped' | 'to_ship';
    /** 订单状态列表 */
    statusList?: (
      | 'cancellation'
      | 'completed'
      | 'fail_delivery'
      | 'pending'
      | 'shipped'
      | 'to_ship'
    )[];
  };

  type PageTkshopRequest = {
    pageNum?: number;
    pageSize?: number;
    /** 站点 */
    platformId?: number;
    /** 名称或备注 */
    query?: string;
    /** 类型 */
    shopType?: 'Global' | 'Local' | 'None';
    sortField?:
      | 'createTime'
      | 'healthRank'
      | 'healthScore'
      | 'healthStatus'
      | 'lastSyncTime'
      | 'name'
      | 'type';
    sortOrder?: 'asc' | 'desc';
  };

  type PageVideoCreatorRequest = {
    pageNum?: number;
    pageSize?: number;
    query?: string;
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
  };

  type PageVideoRequest = {
    adCode?: string;
    autoSync?: boolean;
    commentCntFrom?: number;
    commentCntTo?: number;
    creatorId?: number;
    durationFrom?: number;
    durationTo?: number;
    estCommissionFrom?: number;
    estCommissionTo?: number;
    favoriteCntFrom?: number;
    favoriteCntTo?: number;
    gmvFrom?: number;
    gmvTo?: number;
    /** 达人ID */
    handle?: string;
    /** 视频是否有索样记录 */
    hasSampleRequest?: boolean;
    itemsSoldFrom?: number;
    itemsSoldTo?: number;
    likeCntFrom?: number;
    likeCntTo?: number;
    limit?: number;
    orderCountFrom?: number;
    orderCountTo?: number;
    /** 千播转化率<= */
    ordersPmFrom?: number;
    ordersPmQueryList?: QueryVo[];
    /** 千播转化率>= */
    ordersPmTo?: number;
    ordersPmTypes?: ('abnormal' | 'bad' | 'good' | 'normal')[];
    pageNum?: number;
    pageSize?: number;
    postTimeBeforeDays?: number;
    postTimeFrom?: string;
    postTimeLastDays?: number;
    postTimeTo?: string;
    productNo?: string;
    /** 标题和备注或MediaId或达人handle前缀 */
    query?: string;
    retweetCntFrom?: number;
    retweetCntTo?: number;
    shopIds?: number[];
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
    /** 视频的索样记录ID是什么 */
    srApplyId?: string;
    syncTimeDiff?: number;
    viewCntFrom?: number;
    viewCntTo?: number;
    viewerCntFrom?: number;
    viewerCntTo?: number;
  };

  type PartnerDto = {
    bankAccount?: string;
    bankName?: string;
    bankNo?: string;
    contactName?: string;
    contactPhone?: string;
    createTime?: string;
    fullName?: string;
    id?: number;
    managerId?: number;
    oemSupport?: boolean;
    openapiSupport?: boolean;
    organizedTeamAccountQuota?: number;
    organizedTeamUserQuota?: number;
    password?: string;
    role?: 'Broker' | 'Organizer';
    shortName?: string;
    status?: 'ACTIVE' | 'BLOCK' | 'DELETED' | 'INACTIVE';
    teamId?: number;
    userId?: number;
  };

  type piliangzhixingchoutilixuanzhongderenwu = {
    deviceId?: string;
    drawerJobIds?: number[];
  };

  type PlanGroupChain = {
    /** 一个跟着一个，第一个一定是自动计划，后续的一定是触发计划 */
    plans?: TkshopPlanVo[];
  };

  type ProductNoRequest = {
    productNos?: string[];
  };

  type QueryVo = {
    symbol?: string;
    value?: number;
  };

  type RegionBatchVo = {
    creators?: IdHandleVo[];
    region?: string;
  };

  type ReportGhJobRequest = {
    jobId?: number;
    message?: string;
    rpaTaskId?: number;
    /** 汇报的时候允许指定状态。如果未指定将根据 success 来设置为成功或失败。 Succeed | Failed | Cancelled  */
    status?: 'Cancelled' | 'Failed' | 'Pending' | 'Running' | 'Scheduled' | 'Succeed';
    success?: boolean;
    /** 是否解绑达人和分身，目前只有在公会后台邀约建联时有意义 */
    unbindCreatorShop?: boolean;
  };

  type RpaFlowGroupVo = {
    description?: string;
    id?: number;
    name?: string;
    sortNumber?: number;
    teamId?: number;
  };

  type RpaFlowVo = {
    allowPushUpdate?: boolean;
    /** 是否可读。针对分享流程和市场流程 */
    allowRead?: boolean;
    bizCode?: string;
    configId?: string;
    console?: boolean;
    createTime?: string;
    createType?: 'FileCopy' | 'Manual' | 'Market' | 'MarketCopy' | 'Shared' | 'TkPack' | 'Tkshop';
    creatorId?: number;
    description?: string;
    dirty?: boolean;
    /** 过期时间，仅针对引用市场流程 */
    expireTime?: string;
    /** 是否已过期，仅针对引用市场流程，根据 expireTime 计算得出来的 */
    expired?: boolean;
    extra?: Record<string, any>;
    flowShareCode?: string;
    groups?: RpaFlowGroupVo[];
    id?: number;
    /** 对应的市场模板ID */
    marketId?: number;
    /** 如果是市场流程，显示市场流程的最新版本 */
    marketLatestVersion?: string;
    name?: string;
    nameBrief?: string;
    /** 数字版本号，会从1开始累加 */
    numberVersion?: number;
    platforms?: RpaPlatformVo[];
    publishTime?: string;
    /** 流程类型，分手机和browser */
    rpaType?: 'Browser' | 'Extension' | 'IOS' | 'Mobile';
    sessionInner?: boolean;
    shareFromTeamId?: number;
    shareFromTeamName?: string;
    /** 如果是分享过来的流程，显示被分享的流程最新的版本 */
    shareLatestVersion?: string;
    /** 不为空表示是他人分享的流程 */
    sharedFlowId?: number;
    shopId?: number;
    sortNo?: number;
    status?: 'Draft' | 'Published';
    supportConcurrent?: boolean;
    /** 团队ID; */
    teamId?: number;
    teamName?: string;
    tkFlowId?: number;
    updateTime?: string;
    version?: string;
  };

  type RpaPlatformVo = {
    flowId?: number;
    platformName?: string;
  };

  type SampleRequestConditionVo = {
    filedType?:
      | 'CreatorColumn'
      | 'CreatorContact'
      | 'CreatorProp'
      | 'CreatorStat'
      | 'ProductColumn';
    key?: string;
    symbol?:
      | 'contains'
      | 'eq'
      | 'ge'
      | 'gt'
      | 'isEmpty'
      | 'le'
      | 'lt'
      | 'ne'
      | 'notContains'
      | 'notEmpty';
    value?: Record<string, any>;
  };

  type SampleRequestDocument = {
    applyList?: SampleRequestVo[];
    avatar?: string;
    creatorId?: string;
    followerCnt?: number;
    handle?: string;
    region?: string;
    statMap?: Record<string, any>;
  };

  type SampleRequestMediaDocument = {
    applyId?: string;
    medias?: SampleRequestMediaItem[];
  };

  type SampleRequestMediaItem = {
    commentNum?: number;
    createTimestamp?: number;
    finishTimestamp?: number;
    likeNum?: number;
    mediaAvatar?: string;
    mediaId?: string;
    mediaName?: string;
    mediaUrl?: string;
    promotionType?:
      | 'AffiliateProductPage'
      | 'ExternalTrafficProgram'
      | 'Livestream'
      | 'Showcase'
      | 'Video';
    viewNum?: number;
  };

  type SampleRequestShopBriefVo = {
    count?: number;
    lastSyncTime?: string;
    shop?: ShopBriefVo;
  };

  type SampleRequestShopVo = {
    count?: number;
    lastSyncTime?: string;
    shopId?: number;
  };

  type SampleRequestStatusDocument = {
    creatorId?: string;
    handle?: string;
    statusMap?: Record<string, any>;
  };

  type SampleRequestVo = {
    applyId?: string;
    commissionRate?: number;
    /** 创建时间 */
    createTime?: number;
    /** 过期剩余毫秒 */
    expiredIn?: number;
    groupId?: string;
    productName?: string;
    productNo?: string;
    skuDesc?: string;
    skuId?: string;
    skuImage?: string;
    status?:
      | 'Cancelled'
      | 'Completed'
      | 'Expired'
      | 'InProgress'
      | 'ReadyToShip'
      | 'Reject'
      | 'Shipped'
      | 'ToReview';
  };

  type SendAddContactsRequest = {
    /** 邀约高级设置 */
    advanceSettings?: Record<string, any>;
    bizScene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
    /** 拟加好友的达人列表 */
    creatorIds?: number[];
    /** 所使用的手机id */
    mobileId?: number;
  };

  type SendAddFriendsRequest = {
    /** 邀约高级设置 */
    advanceSettings?: Record<string, any>;
    bizScene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
    /** 通过什么字段查找用于添加联系人的电话号码 */
    contactType?: 'email' | 'fbMessenger' | 'line' | 'phone' | 'viber' | 'whatsapp' | 'zalo';
    /** 拟加好友的达人列表 */
    creatorIds?: number[];
    /** 所使用的手机账号的id。根据相应账号的平台也决定了最终的流程类型 */
    mobileAccountId?: number;
  };

  type SendBuyerIMChatRequest = {
    advanceSettings?: Record<string, any>;
    /** 自定义输入内容，如果不为空，wordIds会被忽略 */
    content?: string;
    /** 必须要指定在哪个客户端上执行 */
    deviceId?: string;
    ghCreatorIds?: number[];
    shopId?: number;
    /** <ghCreatorId, [相应的video数组]> */
    videos?: Record<string, any>;
    wordsIds?: number[];
  };

  type SendEmailRequest = {
    /** 要给哪些达人发送邮件 */
    creatorIds?: number[];
    deviceId?: string;
    /** 电子邮件模板 */
    documentIds?: number[];
    /** 拟使用的邮箱账号 */
    emailId?: number;
  };

  type SendIMChatByFilterRequest = {
    advanceSettings?: Record<string, any>;
    /** 自定义输入内容，如果不为空，wordIds会被忽略 */
    content?: string;
    /** 必须要指定在哪个客户端上执行 */
    deviceId?: string;
    shopId?: number;
    wordsIds?: number[];
  };

  type SendIMChatByHandleRequest = {
    advanceSettings?: Record<string, any>;
    /** 自定义输入内容，如果不为空，wordIds会被忽略 */
    content?: string;
    /** 必须要指定在哪个客户端上执行 */
    deviceId?: string;
    ghCreatorId?: number;
    shopId?: number;
    /** <ghCreatorId, [相应的video数组]> */
    videos?: Record<string, any>;
    wordsIds?: number[];
  };

  type SendInstantMessageRequest = {
    /** 邀约高级设置 */
    advanceSettings?: Record<string, any>;
    bizScene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
    /** 自定义输入内容，如果不为空，wordIds会被忽略 */
    content?: string;
    /** 拟发送的达人列表 */
    creatorIds?: number[];
    /** 所使用的手机账号的id。根据相应账号的平台也决定了最终的流程类型 */
    mobileAccountId?: number;
    wordsIds?: number[];
  };

  type SendLoginCheckRequest = {
    advanceSettings?: Record<string, any>;
    /** 必须要指定在哪个客户端上执行 */
    deviceId?: string;
    shopId?: number;
  };

  type SendTargetPlanByFilterRequest = {
    advanceSettings?: Record<string, any>;
    /** 必须要指定在哪个客户端上执行 */
    deviceId?: string;
    shopId?: number;
    wordsIds?: number[];
  };

  type SendTargetPlanByHandleRequest = {
    advanceSettings?: Record<string, any>;
    /** 必须要指定在哪个客户端上执行 */
    deviceId?: string;
    shopId?: number;
    wordsIds?: number[];
  };

  type SendVideoADCodeRequest = {
    advanceSettings?: Record<string, any>;
    /** 必须要指定在哪个客户端上执行 */
    deviceId?: string;
    ghCreatorIds?: number[];
    shopId?: number;
  };

  type ShopBriefVo = {
    /** 账户账号 */
    account?: string;
    /** 是否允许用户自行安装插件 */
    allowExtension?: boolean;
    /** 会话是否允许监视 */
    allowMonitor?: boolean;
    /** 是否允许跳过敏感操作 */
    allowSkip?: boolean;
    /** 是否自动代填 */
    autoFill?: boolean;
    bookmarkBar?: string;
    coordinateId?: string;
    /** 创建时间 */
    createTime?: string;
    /** 创建者 */
    creatorId?: number;
    deleteTime?: string;
    deleting?: boolean;
    /** 描述 */
    description?: string;
    domainPolicy?: 'Blacklist' | 'None' | 'Whitelist';
    /** 是否独占访问 */
    exclusive?: boolean;
    extension?: 'both' | 'extension' | 'huayang';
    extraProp?: string;
    /** 绑定的指纹Id */
    fingerprintId?: number;
    /** 绑定的指纹模板Id（只有无状态账号才允许绑定指纹模板） */
    fingerprintTemplateId?: number;
    frontUrl?: string;
    googleTranslateSpeed?: boolean;
    homePage?: string;
    homePageSites?: string;
    /** id */
    id?: number;
    imageForbiddenSize?: number;
    intranetEnabled?: boolean;
    ipSwitchCheckInterval?: number;
    ipSwitchStrategy?: 'Abort' | 'Alert' | 'Off';
    lastAccessTime?: string;
    lastAccessUser?: number;
    lastSyncTime?: string;
    loginStatus?: 'Offline' | 'Online' | 'Unknown';
    /** 任务栏分身标记文字 */
    markCode?: string;
    /** 任务栏分身标记背景颜色 */
    markCodeBg?: number;
    monitorPerception?: boolean;
    /** 账户名称 */
    name?: string;
    nameBookmarkEnabled?: boolean;
    operateStatus?: 'shared' | 'sharing' | 'sole' | 'transferring';
    /** 经营品类 */
    operatingCategory?:
      | '医药保健'
      | '图书文具'
      | '宠物用品'
      | '家具建材'
      | '家电电器'
      | '工业用品'
      | '户外运动'
      | '手机数码'
      | '手表眼镜'
      | '护肤美妆'
      | '母婴玩具'
      | '汽车配件'
      | '生活家居'
      | '电商其他'
      | '电脑平板'
      | '艺术珠宝'
      | '花园聚会'
      | '计生情趣'
      | '软件程序'
      | '鞋服箱包'
      | '音乐影视'
      | '食品生鲜'
      | '鲜花绿植';
    parentShopId?: number;
    /** 密码 */
    password?: string;
    /** 平台信息 */
    platform?: ShopPlatformVo;
    /** 平台ID */
    platformId?: number;
    privateAddress?: string;
    privateTitle?: string;
    recordPerception?: boolean;
    recordPolicy?: 'Chosen' | 'Disabled' | 'Forced';
    requireIp?: boolean;
    resourcePolicy?: number;
    securityPolicyEnabled?: boolean;
    securityPolicyUpdateTime?: string;
    sharePolicyId?: string;
    shopDataSize?: number;
    shopNo?: string;
    stateless?: boolean;
    statelessChangeFp?: boolean;
    statelessSyncPolicy?: number;
    syncPolicy?: number;
    /** 团队ID */
    teamId?: number;
    trafficAlertStrategy?: 'Abort' | 'Off';
    trafficAlertThreshold?: number;
    trafficSaving?: boolean;
    type?: 'Global' | 'Local' | 'None';
    webSecurity?: boolean;
  };

  type ShopBuyerBean = {
    advanceSettings?: Record<string, any>;
    ghCreatorIds?: number[];
  };

  type ShopChannelVo = {
    createTime?: string;
    dynamicStrategy?: 'Off' | 'Remain' | 'SwitchOnSession';
    id?: number;
    /** 通道的IP */
    ip?: TeamIpVo;
    ipId?: number;
    /** 通道的IP池 */
    ipPool?: IpPoolDto;
    ippId?: number;
    locationId?: number;
    locationLevel?: 'City' | 'Continent' | 'Country' | 'District' | 'None' | 'Province' | 'Unknown';
    officialChannelId?: number;
    primary?: boolean;
    shopId?: number;
    teamId?: number;
  };

  type ShopCreatorBean = {
    advanceSettings?: Record<string, any>;
    ghCreatorIds?: number[];
  };

  type ShopCreatorStatVo = {
    /** 合作达人 */
    collaboratingCount?: number;
    /** 出单达人 */
    hasOrderCount?: number;
    /** 出单率:出单数/(出单数+合作数） */
    hasOrderRate?: number;
  };

  type ShopDto = {
    /** 账户账号 */
    account?: string;
    /** 是否允许用户自行安装插件 */
    allowExtension?: boolean;
    /** 会话是否允许监视 */
    allowMonitor?: boolean;
    /** 是否允许跳过敏感操作 */
    allowSkip?: boolean;
    /** 是否自动代填 */
    autoFill?: boolean;
    bookmarkBar?: string;
    coordinateId?: string;
    /** 创建时间 */
    createTime?: string;
    /** 创建者 */
    creatorId?: number;
    deleteTime?: string;
    deleting?: boolean;
    /** 描述 */
    description?: string;
    domainPolicy?: 'Blacklist' | 'None' | 'Whitelist';
    /** 是否独占访问 */
    exclusive?: boolean;
    extension?: 'both' | 'extension' | 'huayang';
    extraProp?: string;
    /** 绑定的指纹Id */
    fingerprintId?: number;
    /** 绑定的指纹模板Id（只有无状态账号才允许绑定指纹模板） */
    fingerprintTemplateId?: number;
    frontUrl?: string;
    googleTranslateSpeed?: boolean;
    homePage?: string;
    homePageSites?: string;
    /** id */
    id?: number;
    imageForbiddenSize?: number;
    intranetEnabled?: boolean;
    ipSwitchCheckInterval?: number;
    ipSwitchStrategy?: 'Abort' | 'Alert' | 'Off';
    lastAccessTime?: string;
    lastAccessUser?: number;
    lastSyncTime?: string;
    loginStatus?: 'Offline' | 'Online' | 'Unknown';
    /** 任务栏分身标记文字 */
    markCode?: string;
    /** 任务栏分身标记背景颜色 */
    markCodeBg?: number;
    monitorPerception?: boolean;
    /** 账户名称 */
    name?: string;
    nameBookmarkEnabled?: boolean;
    operateStatus?: 'shared' | 'sharing' | 'sole' | 'transferring';
    /** 经营品类 */
    operatingCategory?:
      | '医药保健'
      | '图书文具'
      | '宠物用品'
      | '家具建材'
      | '家电电器'
      | '工业用品'
      | '户外运动'
      | '手机数码'
      | '手表眼镜'
      | '护肤美妆'
      | '母婴玩具'
      | '汽车配件'
      | '生活家居'
      | '电商其他'
      | '电脑平板'
      | '艺术珠宝'
      | '花园聚会'
      | '计生情趣'
      | '软件程序'
      | '鞋服箱包'
      | '音乐影视'
      | '食品生鲜'
      | '鲜花绿植';
    parentShopId?: number;
    /** 密码 */
    password?: string;
    /** 平台ID */
    platformId?: number;
    privateAddress?: string;
    privateTitle?: string;
    recordPerception?: boolean;
    recordPolicy?: 'Chosen' | 'Disabled' | 'Forced';
    requireIp?: boolean;
    resourcePolicy?: number;
    securityPolicyEnabled?: boolean;
    securityPolicyUpdateTime?: string;
    sharePolicyId?: string;
    shopDataSize?: number;
    shopNo?: string;
    stateless?: boolean;
    statelessChangeFp?: boolean;
    statelessSyncPolicy?: number;
    syncPolicy?: number;
    /** 团队ID */
    teamId?: number;
    trafficAlertStrategy?: 'Abort' | 'Off';
    trafficAlertThreshold?: number;
    trafficSaving?: boolean;
    type?: 'Global' | 'Local' | 'None';
    webSecurity?: boolean;
  };

  type ShopHealthVo = {
    /** 账户账号 */
    account?: string;
    /** 是否允许用户自行安装插件 */
    allowExtension?: boolean;
    /** 会话是否允许监视 */
    allowMonitor?: boolean;
    /** 是否允许跳过敏感操作 */
    allowSkip?: boolean;
    /** 是否自动代填 */
    autoFill?: boolean;
    bookmarkBar?: string;
    /** 通道列表 */
    channels?: ShopChannelVo[];
    coordinateId?: string;
    /** 创建时间 */
    createTime?: string;
    /** 创建者 */
    creatorId?: number;
    deleteTime?: string;
    deleting?: boolean;
    /** 描述 */
    description?: string;
    domainPolicy?: 'Blacklist' | 'None' | 'Whitelist';
    /** 是否独占访问 */
    exclusive?: boolean;
    extension?: 'both' | 'extension' | 'huayang';
    extraProp?: string;
    /** 绑定的指纹Id */
    fingerprintId?: number;
    /** 绑定的指纹模板Id（只有无状态账号才允许绑定指纹模板） */
    fingerprintTemplateId?: number;
    frontUrl?: string;
    googleTranslateSpeed?: boolean;
    healthVo?: TkshopShopVo;
    homePage?: string;
    homePageSites?: string;
    /** id */
    id?: number;
    imageForbiddenSize?: number;
    intranetEnabled?: boolean;
    ipSwitchCheckInterval?: number;
    ipSwitchStrategy?: 'Abort' | 'Alert' | 'Off';
    /** 本地代理配置 */
    lanProxy?: ShopLanProxyDto;
    lastAccessTime?: string;
    lastAccessUser?: number;
    lastSyncTime?: string;
    loginStatus?: 'Offline' | 'Online' | 'Unknown';
    /** 任务栏分身标记文字 */
    markCode?: string;
    /** 任务栏分身标记背景颜色 */
    markCodeBg?: number;
    monitorPerception?: boolean;
    /** 账户名称 */
    name?: string;
    nameBookmarkEnabled?: boolean;
    operateStatus?: 'shared' | 'sharing' | 'sole' | 'transferring';
    /** 经营品类 */
    operatingCategory?:
      | '医药保健'
      | '图书文具'
      | '宠物用品'
      | '家具建材'
      | '家电电器'
      | '工业用品'
      | '户外运动'
      | '手机数码'
      | '手表眼镜'
      | '护肤美妆'
      | '母婴玩具'
      | '汽车配件'
      | '生活家居'
      | '电商其他'
      | '电脑平板'
      | '艺术珠宝'
      | '花园聚会'
      | '计生情趣'
      | '软件程序'
      | '鞋服箱包'
      | '音乐影视'
      | '食品生鲜'
      | '鲜花绿植';
    parentShopId?: number;
    /** 密码 */
    password?: string;
    /** 平台信息 */
    platform?: ShopPlatformVo;
    /** 平台ID */
    platformId?: number;
    privateAddress?: string;
    privateTitle?: string;
    recordPerception?: boolean;
    recordPolicy?: 'Chosen' | 'Disabled' | 'Forced';
    requireIp?: boolean;
    resourcePolicy?: number;
    securityPolicyEnabled?: boolean;
    securityPolicyUpdateTime?: string;
    sharePolicyId?: string;
    shopDataSize?: number;
    shopNo?: string;
    stateless?: boolean;
    statelessChangeFp?: boolean;
    statelessSyncPolicy?: number;
    syncPolicy?: number;
    /** 团队ID */
    teamId?: number;
    trafficAlertStrategy?: 'Abort' | 'Off';
    trafficAlertThreshold?: number;
    trafficSaving?: boolean;
    type?: 'Global' | 'Local' | 'None';
    webSecurity?: boolean;
  };

  type ShopLanProxyDto = {
    enabled?: boolean;
    host?: string;
    hostDomestic?: boolean;
    hostLocationId?: number;
    id?: number;
    latitude?: number;
    locale?: string;
    locationId?: number;
    longitude?: number;
    networkType?: 'UseDirect' | 'UseProxy' | 'UseSystem';
    password?: string;
    port?: number;
    probeOnSession?: boolean;
    proxyType?: string;
    remoteIp?: string;
    sshKey?: string;
    teamId?: number;
    timezone?: string;
    updateTime?: string;
    username?: string;
  };

  type ShopMediaStatVo = {
    /** 出单达人 */
    hasOrderCount?: number;
    /** 出单率:出单数/(出单数+合作数） */
    hasOrderRate?: number;
    /** 合作达人 */
    total?: number;
  };

  type ShopPlatformVo = {
    area?:
      | 'Argentina'
      | 'Australia'
      | 'Austria'
      | 'Belarus'
      | 'Belgium'
      | 'Bolivia'
      | 'Brazil'
      | 'Canada'
      | 'Chile'
      | 'China'
      | 'Colombia'
      | 'Costa_Rica'
      | 'Dominican'
      | 'Ecuador'
      | 'Egypt'
      | 'France'
      | 'Germany'
      | 'Global'
      | 'Guatemala'
      | 'Honduras'
      | 'HongKong'
      | 'India'
      | 'Indonesia'
      | 'Ireland'
      | 'Israel'
      | 'Italy'
      | 'Japan'
      | 'Kazakhstan'
      | 'Korea'
      | 'Malaysia'
      | 'Mexico'
      | 'Netherlands'
      | 'Nicaragua'
      | 'Panama'
      | 'Paraguay'
      | 'Peru'
      | 'Philippines'
      | 'Poland'
      | 'Portuguese'
      | 'Puerto_Rico'
      | 'Russia'
      | 'Salvador'
      | 'Saudi_Arabia'
      | 'Singapore'
      | 'Spain'
      | 'Sweden'
      | 'Switzerland'
      | 'Taiwan'
      | 'Thailand'
      | 'Turkey'
      | 'United_Arab_Emirates'
      | 'United_Kingdom'
      | 'United_States'
      | 'Uruguay'
      | 'Venezuela'
      | 'Vietnam';
    category?: 'IM' | 'Mail' | 'Other' | 'Payment' | 'Shop' | 'SocialMedia';
    frontUrl?: string;
    id?: number;
    loginUrl?: string;
    name?: string;
    typeName?: string;
  };

  type ShopSrStatVo = {
    /** 已完成 */
    completedCount?: number;
    /** 履约率：completedCount/total */
    fulfillmentRate?: number;
    /** 索样总数（不包含待审批+已拒绝已取消等） */
    total?: number;
  };

  type ShopWithPlatformVo = {
    id?: number;
    name?: string;
    operatingCategory?:
      | '医药保健'
      | '图书文具'
      | '宠物用品'
      | '家具建材'
      | '家电电器'
      | '工业用品'
      | '户外运动'
      | '手机数码'
      | '手表眼镜'
      | '护肤美妆'
      | '母婴玩具'
      | '汽车配件'
      | '生活家居'
      | '电商其他'
      | '电脑平板'
      | '艺术珠宝'
      | '花园聚会'
      | '计生情趣'
      | '软件程序'
      | '鞋服箱包'
      | '音乐影视'
      | '食品生鲜'
      | '鲜花绿植';
    parentShopId?: number;
    platformArea?:
      | 'Argentina'
      | 'Australia'
      | 'Austria'
      | 'Belarus'
      | 'Belgium'
      | 'Bolivia'
      | 'Brazil'
      | 'Canada'
      | 'Chile'
      | 'China'
      | 'Colombia'
      | 'Costa_Rica'
      | 'Dominican'
      | 'Ecuador'
      | 'Egypt'
      | 'France'
      | 'Germany'
      | 'Global'
      | 'Guatemala'
      | 'Honduras'
      | 'HongKong'
      | 'India'
      | 'Indonesia'
      | 'Ireland'
      | 'Israel'
      | 'Italy'
      | 'Japan'
      | 'Kazakhstan'
      | 'Korea'
      | 'Malaysia'
      | 'Mexico'
      | 'Netherlands'
      | 'Nicaragua'
      | 'Panama'
      | 'Paraguay'
      | 'Peru'
      | 'Philippines'
      | 'Poland'
      | 'Portuguese'
      | 'Puerto_Rico'
      | 'Russia'
      | 'Salvador'
      | 'Saudi_Arabia'
      | 'Singapore'
      | 'Spain'
      | 'Sweden'
      | 'Switzerland'
      | 'Taiwan'
      | 'Thailand'
      | 'Turkey'
      | 'United_Arab_Emirates'
      | 'United_Kingdom'
      | 'United_States'
      | 'Uruguay'
      | 'Venezuela'
      | 'Vietnam';
    platformId?: number;
    platformName?: string;
    recordPolicy?: 'Chosen' | 'Disabled' | 'Forced';
    securityPolicyEnabled?: boolean;
    teamId?: number;
    type?: 'Global' | 'Local' | 'None';
  };

  type SyncAffiliateNoDataRequest = {
    orderNos?: string[];
    shopId?: number;
  };

  type SyncAffiliateOrderRequest = {
    orders?: TkshopAffiliateOrderDocument[];
    region?: string;
    shopId?: number;
  };

  type SyncBuyerInfoRequest = {
    advanceSettings?: Record<string, any>;
    /** 必须要指定在哪个客户端上执行 */
    deviceId?: string;
    ghCreatorIds?: number[];
    shopId?: number;
  };

  type SyncBuyerRequest = {
    buyers?: TkshopBuyerDocument[];
  };

  type SyncCreatorDetailRequest = {
    creators?: CreatorDetailDocument[];
    sourceId?: number;
    tags?: string[];
  };

  type SyncCreatorShopRequest = {
    creators?: CreatorShopDocument[];
    /** 当前分身ID */
    shopId?: number;
    sourceId?: number;
  };

  type SyncCreatorVideoRequest = {
    videos?: CreatorVideoDocument[];
  };

  type SyncOrdersRequest = {
    advanceSettings?: Record<string, any>;
    /** 必须要指定在哪个客户端上执行 */
    deviceId?: string;
    shopIds?: number[];
  };

  type SyncProductRequest = {
    /** 产品列表 */
    products?: TkProductDocument[];
    /** 分身ID */
    shopId?: number;
  };

  type SyncProductsRequest = {
    advanceSettings?: Record<string, any>;
    /** 必须要指定在哪个客户端上执行 */
    deviceId?: string;
    shopId?: number;
  };

  type SyncSampleCreatorRequest = {
    advanceSettings?: Record<string, any>;
    /** 必须要指定在哪个客户端上执行 */
    deviceId?: string;
    shopId?: number;
  };

  type SyncSampleRequestMediaRequest = {
    requests?: SampleRequestMediaDocument[];
    shopId?: number;
  };

  type SyncSampleRequestRequest = {
    requests?: SampleRequestDocument[];
    shopId?: number;
  };

  type SyncSampleRequestStatusRequest = {
    requests?: SampleRequestStatusDocument[];
    shopId?: number;
  };

  type SyncShopInfoRequest = {
    advanceSettings?: Record<string, any>;
    /** 必须要指定在哪个客户端上执行 */
    deviceId?: string;
    shopId?: number;
    /** 请使用 shopId ，当shopId不为空时，shopIds会被忽略，当shopId为空时，为了向前兼容，shopIds只读取第一个（我们现在也没有shopIds传多个的场景） */
    shopIds?: number[];
  };

  type SyncVideoAdCodeRequest = {
    /** 投流码 */
    adCode?: string;
    /** 达人昵称 */
    alias?: string;
    /** 达人头像 */
    avatar?: string;
    /** 达人bio */
    bio?: string;
    /** 达人handle */
    handle?: string;
    /** 封面图片URL */
    mediaAvatar?: string;
    /** 视频ID */
    mediaId?: string;
    /** 视频名称 */
    mediaName?: string;
    /** 播放地址 */
    mediaUrl?: string;
    postTimestamp?: number;
    products?: VideoAdCodeProductVo[];
    /** 区域 */
    region?: string;
    /** 花漾店铺ID */
    shopId?: number;
  };

  type TagDto = {
    bizCode?: string;
    color?: number;
    createTime?: string;
    expireTime?: string;
    id?: number;
    resourceType?:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    system?: boolean;
    tag?: string;
    teamId?: number;
  };

  type TargetPlanClearRequest = {
    advanceSettings?: Record<string, any>;
    /** 必须要指定在哪个客户端上执行 */
    deviceId?: string;
    shopId?: number;
  };

  type TeamDto = {
    avatar?: string;
    createTime?: string;
    creatorId?: number;
    deleteTime?: string;
    domesticCloudEnabled?: boolean;
    id?: number;
    invalidTime?: string;
    inviteCode?: number;
    name?: string;
    overseaCloudEnabled?: boolean;
    paid?: boolean;
    partnerId?: number;
    payTime?: string;
    repurchaseTime?: string;
    repurchased?: boolean;
    status?: 'Blocked' | 'Deleted' | 'Pending' | 'Ready';
    teamType?: 'crs' | 'gh' | 'krShop' | 'normal' | 'partner' | 'plugin' | 'tk' | 'tkshop';
    tenantId?: number;
    testing?: boolean;
    validateTime?: string;
    validated?: boolean;
    verified?: boolean;
  };

  type TeamIpVo = {
    autoRenew?: boolean;
    cloudProvider?: string;
    cloudRegion?: string;
    createTime?: string;
    creatorId?: number;
    description?: string;
    directDownTraffic?: number;
    directUpTraffic?: number;
    domestic?: boolean;
    downTraffic?: number;
    dynamic?: boolean;
    eipId?: number;
    enableWhitelist?: boolean;
    /** 过期状态 */
    expireStatus?: 'Expired' | 'Expiring' | 'Normal';
    forbiddenLongLatitude?: boolean;
    gatewayId?: number;
    goodsId?: number;
    goodsType?:
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher';
    id?: number;
    importType?: 'Platform' | 'User';
    invalidTime?: string;
    ip?: string;
    lastProbeTime?: string;
    latitude?: number;
    locale?: string;
    locationId?: number;
    longitude?: number;
    name?: string;
    networkType?: 'cloudIdc' | 'mobile' | 'proxyIdc' | 'residential' | 'unknown' | 'unknownIdc';
    operateStatus?: 'shared' | 'sharing' | 'sole' | 'transferring';
    originalTeam?: number;
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    pipeType?: 'None' | 'Proxy' | 'Tunnel' | 'TunnelFailToProxy';
    preferTransit?: number;
    probeError?: string;
    /** 供应商名称 */
    providerName?: string;
    realIp?: string;
    refreshUrl?: string;
    remoteLogin?: boolean;
    renewPrice?: number;
    source?: string;
    speedLimit?: number;
    status?: 'Available' | 'Pending' | 'Unavailable';
    sticky?: boolean;
    teamId?: number;
    testingTime?: number;
    timezone?: string;
    traffic?: number;
    trafficCurrency?: 'CREDIT' | 'RMB' | 'USD';
    trafficPrice?: number;
    trafficUnlimited?: boolean;
    transitType?: 'Auto' | 'Direct' | 'Transit';
    tunnelTypes?: string;
    upTraffic?: number;
    valid?: boolean;
    validEndDate?: string;
    vpsId?: number;
  };

  type TeamQuotaVo = {
    /** 团队免费配额，null表示使用官方默认，-1表示无限 */
    freeQuota?: number;
    /** 阶梯价格 */
    ladderPrices?: LadderPriceRange[];
    /** 官方单价（花瓣/天），0表示免费使用 */
    price?: number;
    /** （最大）配额(-1表示不限制） */
    quota?: number;
    /** 配额描述 */
    quotaDesc?: string;
    /** 配额名称 */
    quotaName?:
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota';
  };

  type TeamTkshopConfig = {
    /** 是否过期提醒 */
    expireRemind?: boolean;
    /** 千次播放转换率参数 */
    ordersPmQueryVo?: OrdersPmQueryVo;
    /** 提前n天提醒 */
    remainBeforeDays?: number;
  };

  type TkProductDocument = {
    /** 产品名称 */
    name?: string;
    /** 产品ID */
    no?: string;
    planEnabled?: boolean;
    planRemark?: string;
    /** 单价 */
    price?: number;
    /** 价格单位 */
    priceUnit?: string;
    /** 库存 */
    quantity?: number;
    /** 状态 */
    status?: 'Deactivated' | 'Deleted' | 'Draft' | 'Live' | 'Reviewing' | 'Suspended';
    /** 产品图片 */
    thumbUrl?: string;
    /** 更新时间戳 */
    updateTime?: number;
  };

  type TkshopAddFriendshipRequest = {
    friendships?: AddFriendshipVo[];
  };

  type TkshopAffiliateOrderDocument = {
    completeTime?: number;
    /** 这个订单的创建时间 */
    createTimestamp?: number;
    /** 使用的是哪种货币，What your buyer paid那里可以看到 */
    currency?: string;
    deliveryTime?: number;
    /** 订单条目 */
    items?: TkshopAffiliateOrderItemDocument[];
    orderNo?: string;
    settlementTime?: number;
  };

  type TkshopAffiliateOrderItemDocument = {
    adsCosFee?: number;
    adsCosRatio?: number;
    cosFee?: number;
    cosRatio?: number;
    creatorNickname?: string;
    /** 提成达人handle */
    creatorUsername?: string;
    /** 带货视频ID */
    mediaId?: string;
    /** 产品图片 */
    productAvatar?: string;
    productName?: string;
    productNo?: string;
    promotionType?:
      | 'AffiliateProductPage'
      | 'ExternalTrafficProgram'
      | 'Livestream'
      | 'Showcase'
      | 'Video';
    /** 买了几个 */
    quantity?: number;
    skuName?: string;
    /** 价格单位 */
    unit?: string;
    /** 产品单价 */
    unitPrice?: number;
  };

  type TkshopAutoTagRequest = {
    handles?: string[];
    tag?: string;
    tagKey?: string;
  };

  type tkshopBuyerByHandleGetParams = {
    /** handle */
    handle: string;
  };

  type tkshopBuyerByIdContactPutParams = {
    /** id */
    id: number;
  };

  type tkshopBuyerByIdGetParams = {
    /** id */
    id: number;
  };

  type tkshopBuyerByIdOneContactPutParams = {
    /** id */
    id: number;
  };

  type tkshopBuyerByIdRemarkPutParams = {
    /** id */
    id: number;
    /** remark */
    remark?: string;
  };

  type tkshopBuyerCountTodayInteractionsGetParams = {
    /** shopId */
    shopId: number;
    /** interactType */
    interactType:
      | 'add_contact'
      | 'add_friend'
      | 'im_chat'
      | 'manual_remark'
      | 'send_email'
      | 'send_facebook'
      | 'send_line'
      | 'send_whatsapp'
      | 'send_zalo';
  };

  type TkshopBuyerDetailVo = {
    alias?: string;
    avatar?: string;
    contactMap?: Record<string, any>;
    contactUrl?: string;
    createTime?: string;
    creatorId?: string;
    firstBuyTime?: string;
    /** 好友关系 */
    friendshipList?: TkshopContactFriendshipVo[];
    handle?: string;
    id?: number;
    lastBuyTime?: string;
    lastSyncTime?: string;
    orderAmount?: number;
    orderCount?: number;
    region?: string;
    remark?: string;
    responsibleUserId?: number;
    shopIds?: number[];
    status?: 'NotContacted' | 'Sent';
    tags?: TagDto[];
    teamId?: number;
    unit?: string;
  };

  type TkshopBuyerDocument = {
    /** 用户的头像地址 */
    avatar?: string;
    /** 给买家发消息的链接 */
    contactUrl?: string;
    creatorId?: string;
    handle?: string;
    /** 手机号 */
    phone?: string;
    region?: string;
  };

  type TkshopBuyerDto = {
    alias?: string;
    avatar?: string;
    contactUrl?: string;
    createTime?: string;
    creatorId?: string;
    firstBuyTime?: string;
    handle?: string;
    id?: number;
    lastBuyTime?: string;
    lastSyncTime?: string;
    orderAmount?: number;
    orderCount?: number;
    region?: string;
    remark?: string;
    responsibleUserId?: number;
    status?: 'NotContacted' | 'Sent';
    teamId?: number;
    unit?: string;
  };

  type TkshopBuyerInteractionDto = {
    buyerId?: number;
    description?: string;
    extraInfo?: string;
    flowId?: number;
    id?: number;
    interactTime?: string;
    interactType?:
      | 'add_contact'
      | 'add_friend'
      | 'im_chat'
      | 'manual_remark'
      | 'send_email'
      | 'send_facebook'
      | 'send_line'
      | 'send_whatsapp'
      | 'send_zalo';
    interactionId?: string;
    jobId?: number;
    shopId?: number;
    shopName?: string;
    status?: string;
    targetId?: number;
    taskId?: number;
    teamId?: number;
    updateTime?: string;
    userId?: number;
  };

  type tkshopBuyerInteractionsGetParams = {
    buyerId?: number;
    pageNum?: number;
    pageSize?: number;
    shopIds?: number[];
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
  };

  type tkshopBuyerLastInteractionGetParams = {
    /** buyerId */
    buyerId: number;
    /** interactType */
    interactType?: string;
  };

  type TkshopCategoryDto = {
    category?: string;
    id?: number;
  };

  type TkshopCheckInvitationCreatorRequest = {
    creatorIds?: string[];
    productNos?: string[];
    shopId?: string;
  };

  type TkshopCheckInvitationRequest = {
    invitations?: CheckInvitationVo[];
    shopId?: number;
  };

  type TkshopConfig = {
    enterprise?: TkshopVersionConfig;
    operationTeamId?: number;
    standard?: TkshopVersionConfig;
    trackVideoDays?: number;
  };

  type TkshopContactFriendshipVo = {
    accountId?: number;
    contactType?: string;
    createTime?: string;
    error?: string;
    mobileId?: number;
    mobileName?: string;
    status?: 'Error' | 'NotFound' | 'Ready' | 'Unknown';
    username?: string;
  };

  type TkshopContactVo = {
    contactMap?: Record<string, any>;
    handle?: string;
  };

  type tkshopCountTodayInteractionsGetParams = {
    /** shopId */
    shopId: number;
    /** interactType */
    interactType:
      | 'add_friend'
      | 'collaborating'
      | 'has_order'
      | 'im_chat'
      | 'manual_remark'
      | 'sample_request'
      | 'send_email'
      | 'send_facebook'
      | 'send_line'
      | 'send_whatsapp'
      | 'send_zalo'
      | 'target_plan';
  };

  type tkshopCreateByTrialCodeGetParams = {
    /** code */
    code: string;
  };

  type TkshopCreatorAchievementResult = {
    lives?: TkshopCreatorLiveDto[];
    products?: TkshopCreatorProductDto[];
    shop?: TkshopCreatorShopDto;
    videos?: TkshopCreatorVideoDto[];
  };

  type TkshopCreatorAchievementStat = {
    commission?: number;
    gmv?: number;
    itemsSold?: number;
    lives?: number;
    orderCount?: number;
    products?: number;
    shopCount?: number;
    status?: 'Collaborating' | 'HasOrder' | 'NotContacted' | 'Sent';
    unit?: string;
    videos?: number;
  };

  type TkshopCreatorAllocateRequest = {
    ids?: number[];
    responsibleUserId?: number;
  };

  type tkshopCreatorByIdAchievementsGetParams = {
    /** id */
    id: number;
    /** shopId */
    shopId: number;
  };

  type tkshopCreatorByIdAchievementShopGetParams = {
    /** id */
    id: number;
    /** shopId */
    shopId: number;
  };

  type tkshopCreatorByIdAchievementShopsGetParams = {
    /** id */
    id: number;
  };

  type tkshopCreatorByIdAchievementStatGetParams = {
    /** id */
    id: number;
  };

  type tkshopCreatorByIdContactPutParams = {
    /** id */
    id: number;
  };

  type tkshopCreatorByIdGetParams = {
    /** id */
    id: number;
  };

  type tkshopCreatorByIdInteractShopsGetParams = {
    /** id */
    id: number;
  };

  type tkshopCreatorByIdLivesGetParams = {
    /** id */
    id: number;
    pageNum?: number;
    pageSize?: number;
    shopId?: number;
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
  };

  type tkshopCreatorByIdOneContactPutParams = {
    /** id */
    id: number;
  };

  type tkshopCreatorByIdProductsGetParams = {
    /** id */
    id: number;
    pageNum?: number;
    pageSize?: number;
    shopId?: number;
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
  };

  type tkshopCreatorByIdRemarkPutParams = {
    /** id */
    id: number;
    /** remark */
    remark?: string;
  };

  type tkshopCreatorByIdStatusAndRemarkPutParams = {
    /** id */
    id: number;
    /** status */
    status: 'Collaborating' | 'HasOrder' | 'NotContacted' | 'Sent';
    /** remark */
    remark?: string;
  };

  type tkshopCreatorByIdVideosGetParams = {
    /** id */
    id: number;
    pageNum?: number;
    pageSize?: number;
    shopId?: number;
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
  };

  type TkshopCreatorContactDetailVo = {
    address?: string;
    alias?: string;
    analysisCreatorId?: string;
    avatar?: string;
    basicSyncTime?: string;
    bio?: string;
    categories?: string;
    cid?: string;
    contactMap?: Record<string, any>;
    contentType?: 'All' | 'LIVE' | 'Video';
    createTime?: string;
    creatorId?: string;
    deleteRemark?: string;
    deleteTime?: string;
    deleting?: boolean;
    followerCnt?: number;
    /** 好友关系 */
    friendshipList?: TkshopContactFriendshipVo[];
    gmv?: number;
    handle?: string;
    hasNewMsg?: boolean;
    id?: number;
    itemsSold?: number;
    lastSyncTime?: string;
    orderCount?: number;
    region?: string;
    remark?: string;
    responsibleUserId?: number;
    roi?: number;
    source?: number;
    status?: 'Collaborating' | 'HasOrder' | 'NotContacted' | 'Sent';
    teamId?: number;
    totalCommission?: number;
    totalGmv?: number;
    totalItemsSold?: number;
    totalProducts?: number;
    unit?: string;
  };

  type tkshopCreatorDeleteByQueryPostParams = {
    /** force */
    force?: boolean;
    /** deleteRemark */
    deleteRemark?: string;
  };

  type tkshopCreatorDeletePostParams = {
    /** force */
    force?: boolean;
    /** deleteRemark */
    deleteRemark?: string;
  };

  type TkshopCreatorDetailVo = {
    /** 合作店铺 */
    achievedShopIds?: number[];
    address?: string;
    alias?: string;
    analysisCreatorId?: string;
    avatar?: string;
    basicSyncTime?: string;
    bio?: string;
    categories?: string;
    categoryList?: TkshopCategoryDto[];
    cid?: string;
    contactMap?: Record<string, any>;
    contentType?: 'All' | 'LIVE' | 'Video';
    createTime?: string;
    creatorId?: string;
    /** 达人的ROI */
    creatorRoi?: TkshopCreatorRoiDto;
    deleteRemark?: string;
    deleteTime?: string;
    deleting?: boolean;
    followerCnt?: number;
    /** 好友关系 */
    friendshipList?: TkshopContactFriendshipVo[];
    gmv?: number;
    handle?: string;
    hasNewMsg?: boolean;
    id?: number;
    itemsSold?: number;
    lastSyncTime?: string;
    orderCount?: number;
    region?: string;
    remark?: string;
    /** 认领人 */
    responsibleUser?: UserDto;
    responsibleUserId?: number;
    roi?: number;
    source?: number;
    /** 统计数据 */
    statList?: TkshopCreatorStatVo[];
    status?: 'Collaborating' | 'HasOrder' | 'NotContacted' | 'Sent';
    /** 标签 */
    tags?: TagDto[];
    teamId?: number;
    totalCommission?: number;
    totalGmv?: number;
    totalItemsSold?: number;
    totalProducts?: number;
    unit?: string;
  };

  type TkshopCreatorDto = {
    address?: string;
    alias?: string;
    analysisCreatorId?: string;
    avatar?: string;
    basicSyncTime?: string;
    bio?: string;
    categories?: string;
    cid?: string;
    contentType?: 'All' | 'LIVE' | 'Video';
    createTime?: string;
    creatorId?: string;
    deleteRemark?: string;
    deleteTime?: string;
    deleting?: boolean;
    followerCnt?: number;
    gmv?: number;
    handle?: string;
    hasNewMsg?: boolean;
    id?: number;
    itemsSold?: number;
    lastSyncTime?: string;
    orderCount?: number;
    region?: string;
    remark?: string;
    responsibleUserId?: number;
    roi?: number;
    source?: number;
    status?: 'Collaborating' | 'HasOrder' | 'NotContacted' | 'Sent';
    teamId?: number;
    totalCommission?: number;
    totalGmv?: number;
    totalItemsSold?: number;
    totalProducts?: number;
    unit?: string;
  };

  type TkshopCreatorExpiredAutoTags = {
    add_friend?: string;
    expiredDays?: number;
    im_chat?: string;
    sample_approve_pass?: string;
    sample_approve_reject?: string;
    send_email?: string;
    send_msg?: string;
    sync_creator?: string;
    target_plan?: string;
  };

  type TkshopCreatorFiledVo = {
    /** 字段描述（可能为null） */
    desc?: string;
    filedType?:
      | 'CreatorColumn'
      | 'CreatorContact'
      | 'CreatorProp'
      | 'CreatorStat'
      | 'ProductColumn';
    /** 方案必须包含 */
    force?: boolean;
    /** 字段Key */
    key?: string;
    /** 字段名称 */
    label?: string;
    path?: string;
    policySupported?: boolean;
    sortable?: boolean;
    valueType?: 'number' | 'string';
  };

  type tkshopCreatorGetAutoTagValueGetParams = {
    /** tagKey */
    tagKey: string;
  };

  type TkshopCreatorLiveDto = {
    creatorId?: number;
    endTime?: string;
    estCommission?: number;
    gmv?: number;
    id?: number;
    itemsSold?: number;
    mediaAvatar?: string;
    mediaId?: string;
    mediaName?: string;
    mediaUrl?: string;
    shopId?: number;
    startTime?: string;
    teamId?: number;
    unit?: string;
  };

  type TkshopCreatorLiveVo = {
    creatorId?: number;
    endTime?: string;
    estCommission?: number;
    gmv?: number;
    id?: number;
    itemsSold?: number;
    live?: TkshopLiveDto;
    mediaAvatar?: string;
    mediaId?: string;
    mediaName?: string;
    mediaUrl?: string;
    products?: TkshopLiveProductDto[];
    shopId?: number;
    startTime?: string;
    teamId?: number;
    unit?: string;
  };

  type TkshopCreatorProductDto = {
    creatorId?: number;
    estCommission?: number;
    gmv?: number;
    id?: number;
    itemsSold?: number;
    productAvatar?: string;
    productName?: string;
    productNo?: string;
    shopId?: number;
    teamId?: number;
    unit?: string;
  };

  type TkshopCreatorProductVo = {
    address?: string;
    alias?: string;
    analysisCreatorId?: string;
    avatar?: string;
    basicSyncTime?: string;
    bio?: string;
    categories?: string;
    cid?: string;
    contentType?: 'All' | 'LIVE' | 'Video';
    createTime?: string;
    creatorId?: string;
    deleteRemark?: string;
    deleteTime?: string;
    deleting?: boolean;
    followerCnt?: number;
    gmv?: number;
    handle?: string;
    hasNewMsg?: boolean;
    id?: number;
    itemsSold?: number;
    lastSyncTime?: string;
    orderCount?: number;
    products?: TkshopVideoProductDto[];
    region?: string;
    remark?: string;
    responsibleUserId?: number;
    roi?: number;
    source?: number;
    status?: 'Collaborating' | 'HasOrder' | 'NotContacted' | 'Sent';
    teamId?: number;
    totalCommission?: number;
    totalGmv?: number;
    totalItemsSold?: number;
    totalProducts?: number;
    unit?: string;
  };

  type tkshopCreatorResponsibleUserListGetParams = {
    /** platformType */
    platformType?:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
  };

  type TkshopCreatorRoiDto = {
    adsCommissionCost?: number;
    adsCost?: number;
    commissionCost?: number;
    creatorId?: number;
    gmv?: number;
    id?: number;
    inputAmount?: number;
    itemSold?: number;
    lives?: number;
    outputAmount?: number;
    products?: number;
    roi?: number;
    sampleRequest?: number;
    sampleRequestCost?: number;
    shops?: number;
    teamId?: number;
    unit?: string;
    updateTime?: string;
    videos?: number;
  };

  type tkshopCreatorScrapFBProfileGetParams = {
    /** messenger */
    messenger: string;
  };

  type TkshopCreatorShopDto = {
    commission?: number;
    createTime?: string;
    creatorId?: number;
    gmv?: number;
    id?: number;
    itemsSold?: number;
    lastSyncTime?: string;
    lives?: number;
    orderCount?: number;
    products?: number;
    roi?: number;
    shopId?: number;
    status?: 'Collaborating' | 'HasOrder' | 'NotContacted' | 'Sent';
    teamId?: number;
    unit?: string;
    videos?: number;
  };

  type TkshopCreatorSrDetailVo = {
    /** 合作店铺 */
    achievedShopIds?: number[];
    address?: string;
    alias?: string;
    analysisCreatorId?: string;
    avatar?: string;
    basicSyncTime?: string;
    bio?: string;
    categories?: string;
    categoryList?: TkshopCategoryDto[];
    cid?: string;
    contactMap?: Record<string, any>;
    contentType?: 'All' | 'LIVE' | 'Video';
    createTime?: string;
    creatorId?: string;
    /** 达人的ROI */
    creatorRoi?: TkshopCreatorRoiDto;
    deleteRemark?: string;
    deleteTime?: string;
    deleting?: boolean;
    /** 是否关注 */
    favorite?: boolean;
    followerCnt?: number;
    /** 好友关系 */
    friendshipList?: TkshopContactFriendshipVo[];
    gmv?: number;
    handle?: string;
    hasNewMsg?: boolean;
    id?: number;
    itemsSold?: number;
    lastSyncTime?: string;
    orderCount?: number;
    region?: string;
    remark?: string;
    /** 认领人 */
    responsibleUser?: UserDto;
    responsibleUserId?: number;
    roi?: number;
    source?: number;
    /** 统计数据 */
    statList?: TkshopCreatorStatVo[];
    status?: 'Collaborating' | 'HasOrder' | 'NotContacted' | 'Sent';
    /** 标签 */
    tags?: TagDto[];
    teamId?: number;
    totalCommission?: number;
    totalGmv?: number;
    totalItemsSold?: number;
    totalProducts?: number;
    unit?: string;
  };

  type TkshopCreatorStatVo = {
    dimension?: string;
    unit?: string;
    value?: number;
  };

  type TkshopCreatorVideoDto = {
    creatorId?: number;
    estCommission?: number;
    gmv?: number;
    id?: number;
    itemsSold?: number;
    mediaAvatar?: string;
    mediaId?: string;
    mediaName?: string;
    mediaUrl?: string;
    postTime?: string;
    shopId?: number;
    teamId?: number;
    unit?: string;
  };

  type TkshopCreatorVideoVo = {
    creatorId?: number;
    estCommission?: number;
    gmv?: number;
    id?: number;
    itemsSold?: number;
    mediaAvatar?: string;
    mediaId?: string;
    mediaName?: string;
    mediaUrl?: string;
    postTime?: string;
    products?: TkshopVideoProductDto[];
    shopId?: number;
    teamId?: number;
    unit?: string;
    video?: TkshopVideoDto;
  };

  type tkshopDowngradePutParams = {
    /** confirm */
    confirm: boolean;
  };

  type tkshopFindBuyerLastOrderNoGetParams = {
    /** buyerId */
    buyerId: number;
    /** shopId */
    shopId: number;
  };

  type tkshopFindBuyerOrdersGetParams = {
    /** buyerId */
    buyerId: number;
    /** sortField */
    sortField?: string;
    /** sortOrder */
    sortOrder?: 'asc' | 'desc';
  };

  type TkshopGlobalCreatorDetailVo = {
    address?: string;
    alias?: string;
    avatar?: string;
    basicSyncTime?: string;
    bio?: string;
    categories?: string;
    categoryList?: TkshopCategoryDto[];
    cid?: string;
    /** 联系信息 */
    contactMap?: Record<string, any>;
    contentType?: 'All' | 'LIVE' | 'Video';
    createTime?: string;
    creatorId?: string;
    followerCnt?: number;
    gmv?: number;
    handle?: string;
    id?: number;
    itemsSold?: number;
    lastSyncTime?: string;
    region?: string;
    remark?: string;
    source?: number;
    /** 统计数据 */
    statList?: TkshopCreatorStatVo[];
  };

  type TkshopHasInteractionRequest = {
    handlers?: string[];
    interactTimeFrom?: number;
    interactTimeTo?: number;
    interactType?:
      | 'add_friend'
      | 'collaborating'
      | 'has_order'
      | 'im_chat'
      | 'manual_remark'
      | 'sample_request'
      | 'send_email'
      | 'send_facebook'
      | 'send_line'
      | 'send_whatsapp'
      | 'send_zalo'
      | 'target_plan';
    shopId?: number;
  };

  type TkshopHealthMetricDto = {
    id?: number;
    name?:
      | 'ses_fulfillment'
      | 'ses_metric_FDR_PC'
      | 'ses_metric_complaint_rate'
      | 'ses_metric_im_response_rate'
      | 'ses_metric_neg_review_rate'
      | 'ses_metric_quality_rr_rate'
      | 'ses_product'
      | 'ses_service'
      | 'ses_ses_metric_seller_fault_cancel';
    rank?: number;
    rawScore?: string;
    score?: number;
    shopId?: number;
    teamId?: number;
    type?: string;
  };

  type TkshopHealthMetricVo = {
    name?: string;
    rank?: number;
    rawScore?: string;
    score?: number;
    type?: string;
  };

  type TkshopInteractionDto = {
    creatorId?: number;
    description?: string;
    extraInfo?: string;
    flowId?: number;
    id?: number;
    interactTime?: string;
    interactType?:
      | 'add_friend'
      | 'collaborating'
      | 'has_order'
      | 'im_chat'
      | 'manual_remark'
      | 'sample_request'
      | 'send_email'
      | 'send_facebook'
      | 'send_line'
      | 'send_whatsapp'
      | 'send_zalo'
      | 'target_plan';
    interactionId?: string;
    jobId?: number;
    shopId?: number;
    shopName?: string;
    status?: string;
    targetId?: number;
    taskId?: number;
    teamId?: number;
    updateTime?: string;
    userId?: number;
  };

  type tkshopInteractionsGetParams = {
    creatorId?: number;
    pageNum?: number;
    pageSize?: number;
    shopIds?: number[];
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
  };

  type tkshopJobsTaskFetchJobGetParams = {
    /** rpaTaskId */
    rpaTaskId: number;
  };

  type tkshopLastInteractionGetParams = {
    /** creatorId */
    creatorId: number;
    /** interactType */
    interactType?: string;
  };

  type tkshopLiveDetailGetParams = {
    /** creatorId */
    creatorId: number;
    /** mediaId */
    mediaId: string;
  };

  type TkshopLiveDto = {
    commentCnt?: number;
    creatorId?: number;
    endTime?: string;
    estCommission?: number;
    gmv?: number;
    id?: number;
    itemsSold?: number;
    likeCnt?: number;
    mediaAvatar?: string;
    mediaId?: string;
    mediaName?: string;
    mediaUrl?: string;
    orderCount?: number;
    remark?: string;
    startTime?: string;
    teamId?: number;
    unit?: string;
    viewCnt?: number;
  };

  type TkshopLiveProductDto = {
    creatorId?: number;
    id?: number;
    mediaId?: string;
    productAvatar?: string;
    productName?: string;
    productNo?: string;
    shopId?: number;
    teamId?: number;
  };

  type tkshopLiveShopsGetParams = {
    /** query */
    query?: string;
  };

  type TkshopLiveVo = {
    commentCnt?: number;
    creator?: TkshopCreatorDto;
    creatorId?: number;
    endTime?: string;
    estCommission?: number;
    gmv?: number;
    id?: number;
    itemsSold?: number;
    likeCnt?: number;
    mediaAvatar?: string;
    mediaId?: string;
    mediaName?: string;
    mediaUrl?: string;
    orderCount?: number;
    products?: TkshopLiveProductDto[];
    remark?: string;
    sampleRequestApplyIds?: string[];
    shopIds?: number[];
    startTime?: string;
    teamId?: number;
    unit?: string;
    viewCnt?: number;
  };

  type tkshopMobileAccountMessageConfigGetParams = {
    /** mobileAccountId */
    mobileAccountId: number;
  };

  type tkshopOrderCalcProductPostParams = {
    /** currency1 */
    currency1?: string;
    /** currency2 */
    currency2?: string;
  };

  type TkshopOrderCountVo = {
    /** 自然流量订单数量 */
    otherCount?: number;
    /** 带货订单数量 */
    promotionCount?: number;
    /** 索样订单数量 */
    sampleCount?: number;
    /** 店铺数 */
    shopCount?: number;
    /** 总量 */
    total?: number;
  };

  type TkshopOrderDocument = {
    /** 该订单付款总额 */
    amount?: number;
    /** 买家信息 */
    buyer?: TkshopBuyerDocument;
    /** 买家备注 */
    buyerNote?: string;
    /** 这个订单的创建时间 */
    createTimestamp?: number;
    /** 使用的是哪种货币，What your buyer paid那里可以看到 */
    currency?: string;
    /** 是否索样订单 */
    freeSample?: boolean;
    /** 订单条目 */
    items?: TkshopOrderItemDocument[];
    /** 对应订单页面的Location字段 */
    location?: string;
    orderNo?: string;
    phone?: string;
    shipAddress?: string;
    /** 订单状态 */
    status?: 'cancellation' | 'completed' | 'fail_delivery' | 'pending' | 'shipped' | 'to_ship';
  };

  type tkshopOrderHasNoPhoneListGetParams = {
    /** shopId */
    shopId: number;
  };

  type tkshopOrderHasPhoneListGetParams = {
    /** shopId */
    shopId: number;
  };

  type TkshopOrderHasPhoneVo = {
    hasPhone?: boolean;
    orderNo?: string;
    phone?: string;
  };

  type TkshopOrderItemDocument = {
    /** 提成达人handle */
    commissionHandle?: string;
    /** 产品图片 */
    productAvatar?: string;
    productName?: string;
    productNo?: string;
    /** 买了几个 */
    quantity?: number;
    skuDesc?: string;
    skuId?: string;
    skuName?: string;
    /** 价格单位 */
    unit?: string;
    /** 产品单价 */
    unitPrice?: number;
  };

  type TkshopOrderItemVo = {
    adsCosFee?: number;
    adsCosRatio?: number;
    affiliateSyncTime?: string;
    amount?: number;
    commissionCreatorId?: number;
    commissionHandle?: string;
    cosFee?: number;
    cosRatio?: number;
    id?: number;
    mediaId?: string;
    mediaName?: string;
    orderId?: number;
    productAvatar?: string;
    productName?: string;
    productNo?: string;
    promotionType?:
      | 'AffiliateProductPage'
      | 'ExternalTrafficProgram'
      | 'Livestream'
      | 'Showcase'
      | 'Video';
    quantity?: number;
    skuDesc?: string;
    skuId?: string;
    skuName?: string;
    teamId?: number;
    unit?: string;
    unitPrice?: number;
  };

  type tkshopOrderMarkSyncDoneGetParams = {
    /** shopId */
    shopId: number;
  };

  type tkshopOrderStatusStatOnShopByShopIdPostParams = {
    /** shopId */
    shopId: number;
    /** currency */
    currency?: string;
  };

  type tkshopOrderStatusStatPostParams = {
    /** currency1 */
    currency1?: string;
    /** currency2 */
    currency2?: string;
  };

  type TkshopOrderStatVo = {
    orderCount?: number;
    totalAmount?: number;
    totalCosFee?: number;
    totalQuantity?: number;
  };

  type tkshopOrderUnfinishedAffiliateOrdersGetParams = {
    /** shopId */
    shopId: number;
    /** limit */
    limit: number;
  };

  type tkshopOrderUnfinishedListGetParams = {
    /** shopId */
    shopId: number;
  };

  type TkshopOrderVo = {
    amount?: number;
    buyer?: TkshopBuyerDetailVo;
    buyerHandle?: string;
    buyerId?: number;
    buyerNote?: string;
    completeTime?: string;
    contactUrl?: string;
    cosFee?: number;
    createTime?: string;
    currency?: string;
    deliveryTime?: string;
    freeSample?: boolean;
    hasCommissionHandle?: boolean;
    id?: number;
    items?: TkshopOrderItemVo[];
    lastSyncTime?: string;
    location?: string;
    orderNo?: string;
    phone?: string;
    quantity?: number;
    settlementTime?: string;
    shipAddress?: string;
    shop?: ShopWithPlatformVo;
    shopId?: number;
    status?: 'cancellation' | 'completed' | 'fail_delivery' | 'pending' | 'shipped' | 'to_ship';
    teamId?: number;
  };

  type TkshopPlanChainGroupVo = {
    createTime?: string;
    creatorId?: number;
    description?: string;
    id?: number;
    layoutId?: number;
    /** shopId对应的分身，如果为空说明分身可能被删除了 */
    shop?: ShopBriefVo;
    /** 计划的店铺对当前用户是不授权了（仅当查询当前用户自己的计划时才会有返回值） */
    shopAuthorized?: boolean;
    shopId?: number;
    teamId?: number;
  };

  type TkshopPlanChainLayoutVo = {
    createTime?: string;
    creatorId?: number;
    description?: string;
    /** 如果对应的设备已经删除了该字段为空 */
    device?: LoginDeviceDto;
    deviceId?: string;
    enabled?: boolean;
    id?: number;
    name?: string;
    /** 计划个数 */
    planCount?: number;
    /** 店铺列表 */
    shops?: ShopBriefVo[];
    teamId?: number;
  };

  type tkshopPlanDeleteGroupDeleteParams = {
    /** groupId */
    groupId: number;
  };

  type tkshopPlanDeleteLayoutDeleteParams = {
    /** layoutId */
    layoutId: number;
  };

  type tkshopPlanDeletePlanDeleteParams = {
    /** planId */
    planId: number;
  };

  type tkshopPlanExecuteAllPlansInGroupPutParams = {
    /** groupId */
    groupId: number;
  };

  type tkshopPlanExecuteAllPlansInLayoutPutParams = {
    /** layoutId */
    layoutId: number;
  };

  type tkshopPlanGetLayoutGetParams = {
    /** layoutId */
    layoutId: number;
  };

  type tkshopPlanLoadGroupChainsGetParams = {
    /** groupId */
    groupId: number;
  };

  type tkshopPlanLoadLayoutGroupsGetParams = {
    /** layoutId */
    layoutId: number;
  };

  type tkshopPlanLoadLayoutsGetParams = {
    /** 只有当用户角色为超管和BOSS的时候才有意义 */
    all?: boolean;
  };

  type tkshopPlanRunPlanPutParams = {
    /** planId */
    planId: number;
  };

  type tkshopPlanToggleLayoutEnabledPutParams = {
    /** layoutId */
    layoutId: number;
    /** enabled */
    enabled: boolean;
  };

  type tkshopPlanTogglePlanEnabledPutParams = {
    /** planId */
    planId: number;
    /** enabled */
    enabled: boolean;
  };

  type tkshopPlanUpdateLayoutNamePutParams = {
    /** layoutId */
    layoutId: number;
    /** name */
    name: string;
  };

  type TkshopPlanVo = {
    bizScene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
    createTime?: string;
    /** 达人筛选条件，json格式 */
    creatorFilter?: string;
    /** 重复日期，只包含星期信息的表达式(具体参考自动流程计划) */
    cronExpression?: string;
    /** 如果是触发计划，触发的延时时间 */
    delayTime?: number;
    enabled?: boolean;
    /** 如果是随机执行，用-隔开开始时间和结束时间[start, start-end, start, ...] */
    expressions?: string[];
    id?: number;
    jobType?:
      | 'AccountFollow'
      | 'AccountHealthCheck'
      | 'AccountMaintenance'
      | 'AccountMessageCheck'
      | 'KOL_LiveRewards'
      | 'ParentJob'
      | 'SendInvite'
      | 'SendPm'
      | 'SendUserCard'
      | 'ShareVideo'
      | 'SubJob'
      | 'SyncContactToPhone'
      | 'SyncContacts'
      | 'SyncCreator'
      | 'SyncLiveStream'
      | 'SyncPm'
      | 'TS_AddContacts'
      | 'TS_AddFriends'
      | 'TS_DemandPayment'
      | 'TS_IMChatByFilter'
      | 'TS_IMChatByHandle'
      | 'TS_LoginCheck'
      | 'TS_SampleApprove'
      | 'TS_SendBuyerIMChat'
      | 'TS_SendEmail'
      | 'TS_SendFacebook'
      | 'TS_SendLine'
      | 'TS_SendWhatsApp'
      | 'TS_SendZalo'
      | 'TS_SyncBuyerInfo'
      | 'TS_SyncCreator'
      | 'TS_SyncOrders'
      | 'TS_SyncProducts'
      | 'TS_SyncSampleCreator'
      | 'TS_SyncShopInfo'
      | 'TS_SyncVideos'
      | 'TS_TargetPlanByFilter'
      | 'TS_TargetPlanByHandle'
      | 'TS_TargetPlanClear'
      | 'TS_VideoADCode';
    name?: string;
    /** 下次执行时间 */
    nextFireTime?: string;
    params?: string;
    /** 如果是触发计划，父计划的ID */
    parentId?: number;
    planGroupId?: number;
    planType?: 'Team' | 'User';
    teamId?: number;
    /** 触发条件 触发策略 */
    triggerPolicy?: 'Always' | 'OnSuccess';
    /** 计划的触发类型 */
    triggerType?: 'Following' | 'Timing';
    userId?: number;
  };

  type tkshopProductByIdRemarkPutParams = {
    /** id */
    id: number;
    /** remark */
    remark?: string;
  };

  type tkshopProductByNoGetParams = {
    /** shopId */
    shopId: number;
    /** productNo */
    productNo: string;
  };

  type TkshopProductDto = {
    createTime?: string;
    id?: number;
    itemsSold?: number;
    lastSyncTime?: string;
    planEnabled?: boolean;
    planRemark?: string;
    price?: number;
    priceUnit?: string;
    productAvatar?: string;
    productName?: string;
    productNo?: string;
    quantity?: number;
    regions?: string;
    remark?: string;
    shopId?: number;
    status?: 'Deactivated' | 'Deleted' | 'Draft' | 'Live' | 'Reviewing' | 'Suspended';
    teamId?: number;
    updateTime?: string;
  };

  type tkshopProductPageGetParams = {
    itemsSoldFrom?: number;
    itemsSoldTo?: number;
    name?: string;
    pageNum?: number;
    pageSize?: number;
    /** 所属站点 */
    platformId?: number;
    priceFrom?: number;
    priceTo?: number;
    productNo?: string;
    quantityFrom?: number;
    quantityTo?: number;
    query?: string;
    shopId?: number;
    shopIds?: number[];
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
    status?: 'Deactivated' | 'Deleted' | 'Draft' | 'Live' | 'Reviewing' | 'Suspended';
    updateTimeFrom?: string;
    updateTimeTo?: string;
  };

  type TkshopReportHealthRequest = {
    metrics?: TkshopHealthMetricVo[];
    rank?: number;
    score?: number;
    status?: 'NotRestricted' | 'Restricted' | 'ShopClose';
  };

  type TkshopSampleRequestAuditVo = {
    applyId?: string;
    applyTime?: string;
    /** 同意或拒绝或不匹配（=null） */
    approved?: boolean;
    avatar?: string;
    commissionRate?: number;
    contactMap?: Record<string, any>;
    creator?: TkshopCreatorSrDetailVo;
    creatorId?: string;
    expiredTime?: string;
    groupId?: string;
    handle?: string;
    id?: number;
    lastSyncTime?: string;
    lives?: TkshopLiveDto[];
    /** 原因 */
    message?: string;
    /** 任务抽屉是否已经处理 */
    pendingApproved?: boolean;
    pendingDrawer?: TkshopTaskDrawerDto;
    pendingDrawerId?: number;
    product?: TkshopProductDto;
    productName?: string;
    productNo?: string;
    reviewTime?: string;
    shopId?: number;
    skuDesc?: string;
    skuId?: string;
    skuImage?: string;
    status?:
      | 'Cancelled'
      | 'Completed'
      | 'Expired'
      | 'InProgress'
      | 'ReadyToShip'
      | 'Reject'
      | 'Shipped'
      | 'ToReview';
    tcId?: number;
    teamId?: number;
    videos?: TkshopVideoDto[];
  };

  type tkshopSampleRequestCompletedApplyIdsGetParams = {
    /** days */
    days?: number;
    /** shopId */
    shopId: number;
  };

  type TkshopSampleRequestDetailVo = {
    applyId?: string;
    applyTime?: string;
    avatar?: string;
    commissionRate?: number;
    contactMap?: Record<string, any>;
    creator?: TkshopCreatorSrDetailVo;
    creatorId?: string;
    expiredTime?: string;
    groupId?: string;
    handle?: string;
    id?: number;
    lastSyncTime?: string;
    lives?: TkshopLiveDto[];
    /** 任务抽屉是否已经处理 */
    pendingApproved?: boolean;
    pendingDrawer?: TkshopTaskDrawerDto;
    pendingDrawerId?: number;
    product?: TkshopProductDto;
    productName?: string;
    productNo?: string;
    reviewTime?: string;
    shopId?: number;
    skuDesc?: string;
    skuId?: string;
    skuImage?: string;
    status?:
      | 'Cancelled'
      | 'Completed'
      | 'Expired'
      | 'InProgress'
      | 'ReadyToShip'
      | 'Reject'
      | 'Shipped'
      | 'ToReview';
    tcId?: number;
    teamId?: number;
    videos?: TkshopVideoDto[];
  };

  type tkshopSampleRequestPageGetParams = {
    applyId?: string;
    creatorId?: number;
    followerCntFrom?: number;
    followerCntTo?: number;
    includeMedia?: boolean;
    needCreatorDetail?: boolean;
    needProductDetail?: boolean;
    /** 排除的状态 */
    notStatus?:
      | 'Cancelled'
      | 'Completed'
      | 'Expired'
      | 'InProgress'
      | 'ReadyToShip'
      | 'Reject'
      | 'Shipped'
      | 'ToReview';
    pageNum?: number;
    pageSize?: number;
    query?: string;
    region?: string;
    shopId?: number;
    shopIds?: number[];
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
    status?:
      | 'Cancelled'
      | 'Completed'
      | 'Expired'
      | 'InProgress'
      | 'ReadyToShip'
      | 'Reject'
      | 'Shipped'
      | 'ToReview';
    statusList?:
      | 'Cancelled'
      | 'Completed'
      | 'Expired'
      | 'InProgress'
      | 'ReadyToShip'
      | 'Reject'
      | 'Shipped'
      | 'ToReview';
    tagIds?: number[];
    /** 标签的逻辑条件，支持OR|AND */
    tagLc?: 'AND' | 'NOT' | 'OR';
  };

  type tkshopSampleRequestPageShopsToReviewGetParams = {
    pageNum?: number;
    pageSize?: number;
    /** 可取值：lastSyncTime,count */
    sortField?: string;
    sortOrder?: 'ASC' | 'DESC';
  };

  type tkshopSampleRequestPageWithPolicyGetParams = {
    applyId?: string;
    creatorId?: number;
    followerCntFrom?: number;
    followerCntTo?: number;
    includeMedia?: boolean;
    needCreatorDetail?: boolean;
    needProductDetail?: boolean;
    /** 排除的状态 */
    notStatus?:
      | 'Cancelled'
      | 'Completed'
      | 'Expired'
      | 'InProgress'
      | 'ReadyToShip'
      | 'Reject'
      | 'Shipped'
      | 'ToReview';
    pageNum?: number;
    pageSize?: number;
    policyId?: number;
    query?: string;
    region?: string;
    shopId?: number;
    shopIds?: number[];
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
    status?:
      | 'Cancelled'
      | 'Completed'
      | 'Expired'
      | 'InProgress'
      | 'ReadyToShip'
      | 'Reject'
      | 'Shipped'
      | 'ToReview';
    statusList?:
      | 'Cancelled'
      | 'Completed'
      | 'Expired'
      | 'InProgress'
      | 'ReadyToShip'
      | 'Reject'
      | 'Shipped'
      | 'ToReview';
    tagIds?: number[];
    /** 标签的逻辑条件，支持OR|AND */
    tagLc?: 'AND' | 'NOT' | 'OR';
  };

  type tkshopSampleRequestPolicyByIdDeleteParams = {
    /** id */
    id: number;
  };

  type tkshopSampleRequestPolicyByIdPutParams = {
    /** id */
    id: number;
  };

  type TkshopSampleRequestPolicyVo = {
    approved?: boolean;
    conditionList?: SampleRequestConditionVo[];
    conditions?: string;
    createTime?: string;
    id?: number;
    logicalCondition?: 'AND' | 'NOT' | 'OR';
    policyDesc?: string;
    policyName?: string;
    teamId?: number;
  };

  type tkshopSearchProfileByIdDeleteParams = {
    /** id */
    id: number;
  };

  type TkshopSearchProfileDto = {
    createTime?: string;
    id?: number;
    name?: string;
    params?: string;
    profileType?:
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TkshopDeletingCreator'
      | 'TkshopFavoriteCreator'
      | 'TkshopGlobalCreator'
      | 'TkshopLive'
      | 'TkshopOrder'
      | 'TkshopProduct'
      | 'TkshopVideo';
    teamId?: number;
    userId?: number;
  };

  type tkshopSearchProfileListGetParams = {
    /** profileType */
    profileType:
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TkshopDeletingCreator'
      | 'TkshopFavoriteCreator'
      | 'TkshopGlobalCreator'
      | 'TkshopLive'
      | 'TkshopOrder'
      | 'TkshopProduct'
      | 'TkshopVideo';
  };

  type tkshopSearchProfilePostParams = {
    /** name */
    name: string;
    /** profileType */
    profileType:
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TkshopDeletingCreator'
      | 'TkshopFavoriteCreator'
      | 'TkshopGlobalCreator'
      | 'TkshopLive'
      | 'TkshopOrder'
      | 'TkshopProduct'
      | 'TkshopVideo';
  };

  type tkshopSettingsGetTSDeviceBrowserConcurrentGetParams = {
    /** deviceId */
    deviceId: string;
  };

  type tkshopSettingsSetTSDeviceBrowserConcurrentPutParams = {
    /** deviceId */
    deviceId: string;
    /** concurrent */
    concurrent: number;
  };

  type TkshopShopBuyerInfo = {
    buyerIds?: number[];
    shopId?: number;
  };

  type TkshopShopBuyerResult = {
    shopBuyersList?: TkshopShopBuyerInfo[];
  };

  type tkshopShopByIdReportTkShopNoPutParams = {
    /** id */
    id: number;
    /** coordinateId */
    coordinateId: string;
    /** shopNo */
    shopNo: string;
  };

  type tkshopShopByShopIdCreatorInteractStatGetParams = {
    /** shopId */
    shopId: number;
    /** days */
    days: number;
  };

  type tkshopShopByShopIdCreatorStatGetParams = {
    /** shopId */
    shopId: number;
  };

  type tkshopShopByShopIdHasOrderCreatorGetParams = {
    /** shopId */
    shopId: number;
    /** days */
    days: number;
    /** pageNum */
    pageNum: number;
    /** pageSize */
    pageSize: number;
  };

  type tkshopShopByShopIdHealthDetailGetParams = {
    /** shopId */
    shopId: number;
  };

  type tkshopShopByShopIdLoginStatusPutParams = {
    /** shopId */
    shopId: number;
    /** loginStatus */
    loginStatus: 'Offline' | 'Online' | 'Unknown';
  };

  type tkshopShopByShopIdReportHealthPutParams = {
    /** shopId */
    shopId: number;
  };

  type tkshopShopByShopIdShopLiveStatGetParams = {
    /** shopId */
    shopId: number;
  };

  type tkshopShopByShopIdShopSrStatGetParams = {
    /** shopId */
    shopId: number;
  };

  type tkshopShopByShopIdShopVideoStatGetParams = {
    /** shopId */
    shopId: number;
  };

  type tkshopShopMarkSyncDoneGetParams = {
    /** shopId */
    shopId: number;
  };

  type TkshopShopVo = {
    firstOrderSyncTime?: string;
    healthRank?: number;
    healthScore?: number;
    healthStatus?: 'NotRestricted' | 'Restricted' | 'ShopClose';
    id?: number;
    lastOrderSyncTime?: string;
    metrics?: TkshopHealthMetricDto[];
    teamId?: number;
  };

  type TkshopSyncInvitationRequest = {
    invitations?: InvitationDocument[];
    shopId?: number;
  };

  type TkshopSyncOrderRequest = {
    orders?: TkshopOrderDocument[];
    region?: string;
    shopId?: number;
  };

  type TkshopSystemStatusVo = {
    /** RPA流程 */
    flows?: RpaFlowVo[];
    /** 购买套餐的订单 */
    order?: OrderDetailVo;
    /** 购买套餐的订单条目 */
    orderItem?: OrderItemDto;
    /** 已用配额 */
    shopCount?: number;
    /** 创建的账号 */
    shops?: ShopDto[];
    /** 创建的团队信息 */
    team?: TeamDto;
    /** 团队配额信息 */
    teamQuotas?: TeamQuotaVo[];
    /** 过期时间相关 */
    tkshopTeam?: TkshopTeamDto;
    /** 成员数目 */
    userCount?: number;
  };

  type tkshopTaskDrawerByIdDeleteParams = {
    /** id */
    id: number;
  };

  type TkshopTaskDrawerDto = {
    accountId?: number;
    createTime?: string;
    creatorId?: number;
    deviceId?: number;
    id?: number;
    parameter?: string;
    rpaType?: 'Browser' | 'Extension' | 'IOS' | 'Mobile';
    taskKey?: string;
    taskName?: string;
    taskType?:
      | 'TS_IMChatByFilter'
      | 'TS_IMChatByHandle'
      | 'TS_SampleApprove'
      | 'TS_SendBuyerIMChat'
      | 'TS_SendLine'
      | 'TS_SendWhatsApp'
      | 'TS_SyncBuyerInfo'
      | 'TS_SyncCreator'
      | 'TS_SyncOrders'
      | 'TS_SyncSampleCreator'
      | 'TS_SyncShopInfo'
      | 'TS_SyncVideos'
      | 'TS_TargetPlanByFilter'
      | 'TS_TargetPlanByHandle'
      | 'TS_VideoADCode';
    teamId?: number;
    userId?: number;
  };

  type tkshopTaskDrawersGetParams = {
    pageNum?: number;
    pageSize?: number;
  };

  type TkshopTaskDrawerVo = {
    accountId?: number;
    createTime?: string;
    creatorId?: number;
    deviceId?: number;
    /** 关联分身的插件类型 */
    extensionType?: 'both' | 'extension' | 'huayang';
    id?: number;
    parameter?: string;
    rpaType?: 'Browser' | 'Extension' | 'IOS' | 'Mobile';
    taskKey?: string;
    taskName?: string;
    taskType?:
      | 'TS_IMChatByFilter'
      | 'TS_IMChatByHandle'
      | 'TS_SampleApprove'
      | 'TS_SendBuyerIMChat'
      | 'TS_SendLine'
      | 'TS_SendWhatsApp'
      | 'TS_SyncBuyerInfo'
      | 'TS_SyncCreator'
      | 'TS_SyncOrders'
      | 'TS_SyncSampleCreator'
      | 'TS_SyncShopInfo'
      | 'TS_SyncVideos'
      | 'TS_TargetPlanByFilter'
      | 'TS_TargetPlanByHandle'
      | 'TS_VideoADCode';
    teamId?: number;
    userId?: number;
  };

  type TkshopTeamDto = {
    allocateCreatorEnabled?: boolean;
    autoRenew?: boolean;
    buyerChatQuota?: number;
    createTime?: string;
    customPrice?: boolean;
    duration?: number;
    extraParams?: string;
    goodsId?: number;
    id?: number;
    imChatQuota?: number;
    invalidateTime?: string;
    listPrice?: number;
    mobileQuota?: number;
    name?: string;
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    realPrice?: number;
    renewPending?: boolean;
    renewPrice?: number;
    shopQuota?: number;
    syncVideoQuota?: number;
    targetPlanQuota?: number;
    userQuota?: number;
    valid?: boolean;
    validEndDate?: string;
    version?: 'TkshopEnterprise' | 'TkshopStandard';
  };

  type TkshopTrialCodeDto = {
    code?: string;
    createTime?: string;
    creatorId?: number;
    id?: number;
    partnerId?: number;
    remark?: string;
    shopQuota?: number;
    userQuota?: number;
    valid?: boolean;
    validSeconds?: number;
    version?: 'TkshopEnterprise' | 'TkshopStandard';
  };

  type tkshopTrialCodeGetParams = {
    /** code */
    code: string;
  };

  type tkshopUpdateAllocateCreatorEnabledPutParams = {
    /** enabled */
    enabled: boolean;
  };

  type TkshopVersionConfig = {
    basePrice?: number;
    baseQuota?: Record<string, any>;
    /** 批量私信买家数量 */
    buyerChatQuota?: number;
    /** 基准赠送花瓣 */
    creditBase?: number;
    /** 每个分身赠送花瓣 */
    creditPerShop?: number;
    durationPrices?: DurationPriceVo[];
    /** 自有IP允许使用接入点 */
    enableTransit?: boolean;
    /** 每日从公海达人库导入到团队的数量 */
    globalCreatorImportQuota?: number;
    /** 批量私信达人数量 */
    imChatQuota?: number;
    ladderPrices?: LadderPriceVo[];
    /** 关注带货视频的数量 */
    syncVideoQuota?: number;
    /** 批量邀约达人数量 */
    targetPlanQuota?: number;
  };

  type tkshopVideoAvatarGetParams = {
    /** creatorId */
    creatorId: number;
    /** mediaId */
    mediaId: string;
  };

  type tkshopVideoByIdAdCodeGetParams = {
    /** id */
    id: number;
    /** adCode */
    adCode: string;
  };

  type tkshopVideoByIdScrapAvatarGetParams = {
    /** id */
    id: number;
  };

  type tkshopVideoByIdScrapTest1GetParams = {
    /** id */
    id: number;
  };

  type tkshopVideoByIdStatListGetParams = {
    /** id */
    id: number;
  };

  type tkshopVideoDetailGetParams = {
    /** creatorId */
    creatorId: number;
    /** mediaId */
    mediaId: string;
  };

  type TkshopVideoDto = {
    adCode?: string;
    autoSync?: boolean;
    commentCnt?: number;
    createTime?: string;
    creatorId?: number;
    duration?: number;
    estCommission?: number;
    favoriteCnt?: number;
    gmv?: number;
    id?: number;
    itemsSold?: number;
    lastSyncTime?: string;
    likeCnt?: number;
    markSyncTime?: string;
    mediaAvatar?: string;
    mediaId?: string;
    mediaName?: string;
    mediaUrl?: string;
    orderCount?: number;
    ordersPm?: number;
    postTime?: string;
    remark?: string;
    retweetCnt?: number;
    teamId?: number;
    unit?: string;
    viewCnt?: number;
    viewerCnt?: number;
  };

  type tkshopVideoMarkSyncPostParams = {
    /** force */
    force?: boolean;
  };

  type TkshopVideoProductDto = {
    creatorId?: number;
    id?: number;
    mediaId?: string;
    productAvatar?: string;
    productName?: string;
    productNo?: string;
    shopId?: number;
    teamId?: number;
  };

  type tkshopVideoPutOssUrlGetParams = {
    /** mediaId */
    mediaId: string;
    /** ext */
    ext?: string;
  };

  type tkshopVideoScrapTest2GetParams = {
    /** mediaId */
    mediaId: string;
  };

  type tkshopVideoShopsGetParams = {
    /** query */
    query?: string;
  };

  type TkshopVideoStatDto = {
    commentCnt?: number;
    day?: string;
    favoriteCnt?: number;
    id?: number;
    likeCnt?: number;
    mediaId?: string;
    retweetCnt?: number;
    viewCnt?: number;
  };

  type tkshopVideoUrlGetParams = {
    /** mediaId */
    mediaId: string;
    /** refresh */
    refresh?: boolean;
  };

  type TkshopVideoVo = {
    adCode?: string;
    autoSync?: boolean;
    commentCnt?: number;
    createTime?: string;
    creator?: TkshopCreatorDto;
    creatorId?: number;
    duration?: number;
    estCommission?: number;
    favoriteCnt?: number;
    gmv?: number;
    id?: number;
    itemsSold?: number;
    lastOrderSyncTime?: string;
    lastSyncTime?: string;
    likeCnt?: number;
    markSyncTime?: string;
    mediaAvatar?: string;
    mediaId?: string;
    mediaName?: string;
    mediaUrl?: string;
    orderCount?: number;
    ordersPm?: number;
    ordersPmType?: 'abnormal' | 'bad' | 'good' | 'normal';
    postTime?: string;
    products?: TkshopVideoProductDto[];
    remark?: string;
    retweetCnt?: number;
    sampleRequestApplyIds?: string[];
    shopIds?: number[];
    teamId?: number;
    unit?: string;
    viewCnt?: number;
    viewerCnt?: number;
  };

  type TKVideo = {
    author?: TKVideoAuthor;
    cover?: TKVideoCover;
    createTime?: string;
    data_size?: number;
    desc?: string;
    height?: number;
    statistics?: TKVideoStatistics;
    url_list?: string[];
    width?: number;
  };

  type TKVideoAuthor = {
    handle?: string;
    nickname?: string;
    uid?: string;
    unique_id?: string;
  };

  type TKVideoCover = {
    height?: number;
    url_list?: string[];
    width?: number;
  };

  type TKVideoStatistics = {
    aweme_id?: string;
    collect_count?: number;
    comment_count?: number;
    digg_count?: number;
    download_count?: number;
    forward_count?: number;
    lose_comment_count?: number;
    lose_count?: number;
    play_count?: number;
    repost_count?: number;
    share_count?: number;
    whatsapp_share_count?: number;
  };

  type TSSyncCreatorPolicy = {
    /** 达人基础信息更新每批更新数量 */
    maxShopTaskSyncCreator?: number;
    /** 达人基础信息更新每批次间隔 */
    shopTaskSyncCreatorInterval?: number;
  };

  type UpdateContactRequest = {
    contactMap?: Record<string, any>;
  };

  type UpdateLayoutDeviceRequest = {
    deviceId?: string;
    layoutId?: number;
  };

  type UpdateOneContactRequest = {
    /** 联系方式，传空时表示未注册 */
    contact?: string;
    /** 联系方式类型 */
    contactType?: 'email' | 'fbMessenger' | 'line' | 'phone' | 'viber' | 'whatsapp' | 'zalo';
    error?: string;
    /** 手机账号ID */
    mobileAccountId?: number;
    /** 手机ID */
    mobileId?: number;
    /** 好友关系状态 */
    status?: 'Error' | 'NotFound' | 'Ready' | 'Unknown';
  };

  type UpdatePlanRequest = {
    /** 达人筛选条件，json格式 */
    creatorFilter?: string;
    /** 重复日期，只包含星期信息的表达式(具体参考自动流程计划) */
    cronExpression?: string;
    /** 如果是触发计划，触发的延时时间 */
    delayTime?: number;
    /** 如果是随机执行，用-隔开开始时间和结束时间[start, start-end, start, ...] */
    expressions?: string[];
    id?: number;
    params?: string;
    /** 如果是触发计划，触发条件/触发策略 */
    triggerPolicy?: 'Always' | 'OnSuccess';
  };

  type UpdateRegionRequest = {
    ids?: number[];
    region?: string;
  };

  type UpdateRemarkRequest = {
    ids?: number[];
    remark?: string;
  };

  type UpdateStatusRequest = {
    /** 达人列表 */
    handles?: string[];
    status?: 'Collaborating' | 'HasOrder' | 'NotContacted' | 'Sent';
  };

  type UserDto = {
    avatar?: string;
    createTime?: string;
    gender?: 'FEMALE' | 'MALE' | 'UNSPECIFIC';
    id?: number;
    nickname?: string;
    partnerId?: number;
    residentCity?: string;
    signature?: string;
    status?: 'ACTIVE' | 'BLOCK' | 'DELETED' | 'INACTIVE';
    tenant?: number;
    userType?: 'NORMAL' | 'PARTNER' | 'SHADOW';
  };

  type VideoAdCodeProductVo = {
    /** 商品名称 */
    productName?: string;
    /** 商品ID */
    productNo?: string;
    /** TK店铺ID */
    shopNo?: string;
  };

  type VideoBriefVo = {
    creatorId?: number;
    handle?: string;
    id?: number;
    mediaId?: string;
  };

  type WebResult = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultCalcTkshopPriceResponse = {
    code?: number;
    data?: CalcTkshopPriceResponse;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultCreateBuyTkPackOrderResponse = {
    code?: number;
    data?: CreateBuyTkPackOrderResponse;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultCreateOrderResponse = {
    code?: number;
    data?: CreateOrderResponse;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultCreatorInteractStatVo = {
    code?: number;
    data?: CreatorInteractStatVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultFBUserProfile = {
    code?: number;
    data?: FBUserProfile;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultGhJobDto = {
    code?: number;
    data?: GhJobDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultGhRpaBrowserPolicy = {
    code?: number;
    data?: GhRpaBrowserPolicy;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultint = {
    code?: number;
    data?: number;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListExpressionSymbolVo = {
    code?: number;
    data?: ExpressionSymbolVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListHasInteractionVo = {
    code?: number;
    data?: HasInteractionVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListlong = {
    code?: number;
    data?: number[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListOrderProductStatVo = {
    code?: number;
    data?: OrderProductStatVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListOrderStatusStatVo = {
    code?: number;
    data?: OrderStatusStatVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListPlanGroupChain = {
    code?: number;
    data?: PlanGroupChain[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListRegionBatchVo = {
    code?: number;
    data?: RegionBatchVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListSampleRequestShopVo = {
    code?: number;
    data?: SampleRequestShopVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListShopBriefVo = {
    code?: number;
    data?: ShopBriefVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListShopPlatformVo = {
    code?: number;
    data?: ShopPlatformVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListstring = {
    code?: number;
    data?: string[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTkshopCategoryDto = {
    code?: number;
    data?: TkshopCategoryDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTkshopContactVo = {
    code?: number;
    data?: TkshopContactVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTkshopCreatorContactDetailVo = {
    code?: number;
    data?: TkshopCreatorContactDetailVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTkshopCreatorFiledVo = {
    code?: number;
    data?: TkshopCreatorFiledVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTkshopOrderHasPhoneVo = {
    code?: number;
    data?: TkshopOrderHasPhoneVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTkshopOrderVo = {
    code?: number;
    data?: TkshopOrderVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTkshopPlanChainGroupVo = {
    code?: number;
    data?: TkshopPlanChainGroupVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTkshopPlanChainLayoutVo = {
    code?: number;
    data?: TkshopPlanChainLayoutVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTkshopSampleRequestPolicyVo = {
    code?: number;
    data?: TkshopSampleRequestPolicyVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTkshopSearchProfileDto = {
    code?: number;
    data?: TkshopSearchProfileDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTkshopShopVo = {
    code?: number;
    data?: TkshopShopVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTkshopTaskDrawerDto = {
    code?: number;
    data?: TkshopTaskDrawerDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTkshopVideoStatDto = {
    code?: number;
    data?: TkshopVideoStatDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListVideoBriefVo = {
    code?: number;
    data?: VideoBriefVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultlong = {
    code?: number;
    data?: number;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultMaplong = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultMaplong = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultMapstring = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultMobileAccountMessageConfig = {
    code?: number;
    data?: MobileAccountMessageConfig;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultSampleRequestShopBriefVo = {
    code?: number;
    data?: PageResultSampleRequestShopBriefVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultShopHealthVo = {
    code?: number;
    data?: PageResultShopHealthVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultTkshopBuyerDetailVo = {
    code?: number;
    data?: PageResultTkshopBuyerDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultTkshopBuyerInteractionDto = {
    code?: number;
    data?: PageResultTkshopBuyerInteractionDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultTkshopCreatorDetailVo = {
    code?: number;
    data?: PageResultTkshopCreatorDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultTkshopCreatorDto = {
    code?: number;
    data?: PageResultTkshopCreatorDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultTkshopCreatorLiveVo = {
    code?: number;
    data?: PageResultTkshopCreatorLiveVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultTkshopCreatorProductDto = {
    code?: number;
    data?: PageResultTkshopCreatorProductDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultTkshopCreatorProductVo = {
    code?: number;
    data?: PageResultTkshopCreatorProductVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultTkshopCreatorVideoVo = {
    code?: number;
    data?: PageResultTkshopCreatorVideoVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultTkshopGlobalCreatorDetailVo = {
    code?: number;
    data?: PageResultTkshopGlobalCreatorDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultTkshopInteractionDto = {
    code?: number;
    data?: PageResultTkshopInteractionDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultTkshopLiveVo = {
    code?: number;
    data?: PageResultTkshopLiveVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultTkshopOrderVo = {
    code?: number;
    data?: PageResultTkshopOrderVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultTkshopProductDto = {
    code?: number;
    data?: PageResultTkshopProductDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultTkshopSampleRequestAuditVo = {
    code?: number;
    data?: PageResultTkshopSampleRequestAuditVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultTkshopSampleRequestDetailVo = {
    code?: number;
    data?: PageResultTkshopSampleRequestDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultTkshopTaskDrawerVo = {
    code?: number;
    data?: PageResultTkshopTaskDrawerVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultTkshopVideoVo = {
    code?: number;
    data?: PageResultTkshopVideoVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultShopCreatorStatVo = {
    code?: number;
    data?: ShopCreatorStatVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultShopHealthVo = {
    code?: number;
    data?: ShopHealthVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultShopMediaStatVo = {
    code?: number;
    data?: ShopMediaStatVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultShopSrStatVo = {
    code?: number;
    data?: ShopSrStatVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultstring = {
    code?: number;
    data?: string;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTeamDto = {
    code?: number;
    data?: TeamDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTeamTkshopConfig = {
    code?: number;
    data?: TeamTkshopConfig;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkshopBuyerDetailVo = {
    code?: number;
    data?: TkshopBuyerDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkshopBuyerDto = {
    code?: number;
    data?: TkshopBuyerDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkshopBuyerInteractionDto = {
    code?: number;
    data?: TkshopBuyerInteractionDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkshopConfig = {
    code?: number;
    data?: TkshopConfig;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkshopCreatorAchievementResult = {
    code?: number;
    data?: TkshopCreatorAchievementResult;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkshopCreatorAchievementStat = {
    code?: number;
    data?: TkshopCreatorAchievementStat;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkshopCreatorDetailVo = {
    code?: number;
    data?: TkshopCreatorDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkshopCreatorExpiredAutoTags = {
    code?: number;
    data?: TkshopCreatorExpiredAutoTags;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkshopCreatorShopDto = {
    code?: number;
    data?: TkshopCreatorShopDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkshopInteractionDto = {
    code?: number;
    data?: TkshopInteractionDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkshopLiveVo = {
    code?: number;
    data?: TkshopLiveVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkshopOrderCountVo = {
    code?: number;
    data?: TkshopOrderCountVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkshopOrderStatVo = {
    code?: number;
    data?: TkshopOrderStatVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkshopPlanChainLayoutVo = {
    code?: number;
    data?: TkshopPlanChainLayoutVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkshopProductDto = {
    code?: number;
    data?: TkshopProductDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkshopSampleRequestPolicyVo = {
    code?: number;
    data?: TkshopSampleRequestPolicyVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkshopSearchProfileDto = {
    code?: number;
    data?: TkshopSearchProfileDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkshopShopBuyerResult = {
    code?: number;
    data?: TkshopShopBuyerResult;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkshopSystemStatusVo = {
    code?: number;
    data?: TkshopSystemStatusVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkshopTrialCodeDto = {
    code?: number;
    data?: TkshopTrialCodeDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkshopVideoVo = {
    code?: number;
    data?: TkshopVideoVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTKVideo = {
    code?: number;
    data?: TKVideo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTSSyncCreatorPolicy = {
    code?: number;
    data?: TSSyncCreatorPolicy;
    message?: string;
    requestId?: string;
    success?: boolean;
  };
}
