import I18N from '@/i18n';
import {
  Button,
  ConfigProvider,
  DatePicker,
  Form,
  Input,
  Layout,
  Space,
  Tabs,
  Typography,
} from 'antd';
import styles from './index.less';
import classNames from 'classnames';
import navStyles from '@/style/nav.less';
import colors from '@/style/color.less';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { scrollProTableOptionFn } from '@/mixins/table';
import { dateFormat, getTeamIdFromUrl, trimValues } from '@/utils/utils';
import {
  ghJobsJobDetailGet,
  ghJobsListJobsHistoryGet,
} from '@/services/api-TKGHAPI/GhJobController';
import { useOrder } from '@/components/Sort/SortDropdown';
import SortTitle from '@/components/Sort/SortTitle';
import { TaskJobStatus } from '@/pages/Task/constants';
import IconFontIcon from '@/components/Common/IconFontIcon';
import Placeholder from '@/components/Common/Placeholder';
import { TkShopCreatorById } from '@/components/Common/TkShopCreator';
import { useEffect, useMemo, useRef, useState } from 'react';
import DMModal from '@/components/Common/Modal/DMModal';
import { GhostModalCaller } from '@/mixins/modal';
import DMFormItem, { DMFormItemContext } from '@/components/Common/DMFormItem';
import MiddleSpin from '@/components/Common/MiddleSpin';
import { ShopNodeById } from '@/components/Common/ShopNode';
import GhJobTypeSelector from '@/components/Common/Selector/GhJobTypeSelector';
import GhJobStatusSelector from '@/components/Common/Selector/GhJobStatusSelector';
import _ from 'lodash';
import JobShopsSelector from '@/components/Common/Selector/JobShopsSelector';
import MobileAccountNodeById from '@/components/Common/MobileAccountNode';
import { TikTokAccountTypeNode } from '@/pages/TikTok/components/TikTokAccountType';
import 'moment/locale/zh-cn';
import zhCN from 'antd/lib/locale-provider/zh_CN';
import { GlobalHeaderAction, openClient } from '@/utils/pageUtils';
import { useRequest } from '@@/plugin-request/request';
import { rpaTaskByRpaTaskIdGet } from '@/services/api-RPAAPI/RpaTaskController';
import CopyableText from '@/components/Common/CopyableText';
import MobileSelector from '@/components/Common/Selector/MobileSelector';
import { User } from '@/components/Common/UserAvatarAndName';
import {
  getTaskShelfJobIcon,
  TaskShelfJobType,
} from '@/pages/TikTok/Live/components/TaskShelfModal';
import { getTaskCreatorColumn, getTaskParamsColumn } from '@/pages/Task/utils';
import MobileDeviceById from '@/components/Common/MobileDevice';
import { TkShopBuyerById } from '@/components/Common/TkShopBuyer';
import buttonStyles from '@/style/button.less';
import ColoursIcon from '@/components/Common/ColoursIcon';
import HelpLink from '@/components/HelpLink';
import { StyledTableWrapper } from '@/style/styled';
import { Device } from '@/pages/Setting/components/SelectDeviceModal';

export const TaskDetail = (props: { job: API.GhJobDto; alive: boolean }) => {
  const { job, alive } = props;
  const { id: jobId, rpaTaskId } = job;
  const { data, loading } = useRequest(() => {
    return ghJobsJobDetailGet({
      jobId,
      alive,
    });
  });
  const { data: task, run: getTask } = useRequest(
    (id: number) => {
      return rpaTaskByRpaTaskIdGet({
        rpaTaskId: id,
      });
    },
    {
      manual: true,
    },
  );
  useEffect(() => {
    if (rpaTaskId) {
      getTask(rpaTaskId);
    }
  }, [getTask, rpaTaskId]);

  const [visible, changeVisible] = useState(true);
  const [activeKey, changeActiveKey] = useState('base');

  const creatorCountNode = useMemo(() => {
    const { creators, bizScene } = data || {};
    if (creators?.length) {
      if (bizScene === 'ShopBuyer') {
        if (creators.length === 1) {
          return <TkShopBuyerById id={creators[0].id!} />;
        }
        return creators.length?.toLocaleString() + I18N.t('个买家');
      }
      if (creators.length === 1) {
        return <TkShopCreatorById id={creators[0].id!} />;
      }
      return creators.length?.toLocaleString() + I18N.t('个达人');
    }
    return false;
  }, [data]);
  const shopList = useMemo(() => {
    const { shops, rpaType, jobType } = data || {};
    if (shops?.length) {
      if (jobType === 'TS_AddContacts') {
        return shops.map((i) => {
          return <MobileDeviceById id={i.id!} key={i.id!} />;
        });
      }
      if (rpaType === 'Mobile') {
        return shops.map((i) => {
          return <MobileAccountNodeById id={i.id!} key={i.id!} />;
        });
      }
      return shops.map((i) => {
        return <ShopNodeById id={i.id} key={i.id} />;
      });
    }

    return <Placeholder />;
  }, [data]);

  const content = useMemo(() => {
    if (loading || !data) {
      return <MiddleSpin />;
    }
    const tab = I18N.t('执行体列表');
    return (
      <Tabs
        tabPosition={'left'}
        className={styles.detailTabs}
        activeKey={activeKey}
        onChange={changeActiveKey}
      >
        <Tabs.TabPane key={'base'} tab={I18N.t('基本信息')}>
          <DMFormItemContext.Provider value={{}}>
            <DMFormItem label={I18N.t('任务类型')}>{TaskShelfJobType[data.jobType!]}</DMFormItem>
            <DMFormItem label={I18N.t('流程定义')}>
              {task?.flowName ? <CopyableText>{task?.flowName}</CopyableText> : <Placeholder />}
            </DMFormItem>
            <DMFormItem label={I18N.t('任务ID')}>
              {task?.id ? <CopyableText>{task?.id}</CopyableText> : <Placeholder />}
            </DMFormItem>
            <DMFormItem label={I18N.t('发起时间')}>
              {dateFormat(new Date(data.createTime!))}
            </DMFormItem>
            <DMFormItem label={I18N.t('执行时间')}>
              {data.executeTime ? dateFormat(new Date(data.executeTime!)) : <Placeholder />}
            </DMFormItem>
            <DMFormItem label={I18N.t('结束时间')}>
              {data.executeTime ? dateFormat(new Date(data.executeEndTime!)) : <Placeholder />}
            </DMFormItem>
            <DMFormItem label={I18N.t('状态')}>{TaskJobStatus[data.status!]}</DMFormItem>
          </DMFormItemContext.Provider>
        </Tabs.TabPane>
        {creatorCountNode && (
          <Tabs.TabPane key={'creator_list'} tab={I18N.t('达人列表')}>
            <div
              style={{
                border: '1px solid #ddd',
                height: '100%',
                padding: 8,
                overflow: 'auto',
                display: 'flex',
                gap: '8px',
                flexWrap: 'wrap',
                alignItems: 'flex-start',
                alignContent: 'flex-start',
              }}
            >
              {creatorCountNode}
            </div>
          </Tabs.TabPane>
        )}
        <Tabs.TabPane key={'device_list'} tab={tab}>
          <div
            style={{
              border: '1px solid #ddd',
              height: '100%',
              padding: 8,
              overflow: 'auto',
              display: 'flex',
              gap: '8px',
              flexWrap: 'wrap',
              alignItems: 'flex-start',
              alignContent: 'flex-start',
            }}
          >
            {shopList}
          </div>
        </Tabs.TabPane>
      </Tabs>
    );
  }, [loading, data, activeKey, task?.flowName, task?.id, creatorCountNode, shopList]);
  const footer = useMemo(() => {
    if (rpaTaskId) {
      return (
        <Space>
          <Button
            className={buttonStyles.successBtn}
            onClick={() => {
              openClient({
                action: 'openUrl',
                method: 'post',
                url: `/team/${getTeamIdFromUrl()}/rpa/task/${rpaTaskId}`,
              });
            }}
          >
            {I18N.t('查看任务详情')}
          </Button>
          <Button
            type={'primary'}
            onClick={() => {
              changeVisible(false);
            }}
          >
            {I18N.t('关闭')}
          </Button>
        </Space>
      );
    }
    return (
      <Button
        type={'primary'}
        onClick={() => {
          changeVisible(false);
        }}
      >
        {I18N.t('关闭')}
      </Button>
    );
  }, [rpaTaskId]);
  return (
    <DMModal
      title={I18N.t('任务详情')}
      open={visible}
      width={720}
      bodyStyle={{
        padding: 0,
        height: 470,
        overflow: 'hidden',
      }}
      footer={footer}
      onCancel={() => {
        changeVisible(false);
      }}
    >
      {content}
    </DMModal>
  );
};

const TaskHistory = () => {
  const { order, changeOrder } = useOrder(
    {
      key: 'create_time',
      ascend: false,
    },
    'ts_shop_task_history_v20241028',
  );
  const ghPlatformType = 'tkshop';

  const [form] = Form.useForm();
  const actionRef = useRef<ActionType>();
  const { run: onChange } = useRequest(
    (reset?: boolean) => {
      if (reset) {
        return actionRef.current?.reloadAndRest?.();
      } else {
        return actionRef.current?.reload?.();
      }
    },
    {
      manual: true,
    },
  );
  const loaded = useRef(false);
  const [tableLoading, setTableLoading] = useState(true); // 新增，默认true

  const columns: ProColumns<API.GhJobDto>[] = useMemo(() => {
    return [
      {
        title: I18N.t('任务类型'),
        dataIndex: 'jobType',
        render(_text, record) {
          const { jobType } = record;
          const text = TaskShelfJobType[jobType!];
          return (
            <Typography.Link
              onClick={() => {
                GhostModalCaller(<TaskDetail job={record} alive={false} />);
              }}
            >
              <Space align={'center'} size={4}>
                {getTaskShelfJobIcon(jobType)}
                <span>{text}</span>
              </Space>
            </Typography.Link>
          );
        },
        width: 260,
      },
      {
        title: I18N.t('目标达人'),
        dataIndex: 'creator',
        width: 160,
        render(_text, record) {
          return getTaskCreatorColumn(record);
        },
      },
      {
        title: I18N.t('主要参数'),
        width: 240,
        dataIndex: 'params',
        ellipsis: true,
        renderText(_text, record) {
          return getTaskParamsColumn(record, 'GhJobDto');
        },
      },
      {
        title: I18N.t('分身/手机账号'),
        width: 140,
        dataIndex: 'shopId',
        ellipsis: true,
        render(_text, record) {
          const { rpaType, shopId, shopName, ghCreatorName, jobType } = record;
          if (!shopId) {
            return <Placeholder />;
          }
          let ghScheduleType = record.ghScheduleType;
          if (rpaType === 'Mobile') {
            ghScheduleType = 'Mobile';
          }
          if (ghScheduleType === 'Unknown' || !ghScheduleType) {
            ghScheduleType = 'Normal';
          }
          if (jobType === 'TS_AddContacts') {
            return <MobileDeviceById id={shopId} />;
          }
          return (
            <TikTokAccountTypeNode
              mode={'iconText'}
              handle={ghCreatorName!}
              id={shopId}
              name={shopName}
              type={ghScheduleType}
            />
          );
        },
      },
      {
        title: I18N.t('执行设备'),
        dataIndex: 'deviceName',
        ellipsis: true,
        width: 140,
        render(dom, record) {
          const { device, deviceName } = record;
          if (device) {
            return <Device data={device} />;
          }
          if (deviceName) {
            return (
              <Typography.Text ellipsis={{ tooltip: deviceName }}>{deviceName}</Typography.Text>
            );
          }
          return <Placeholder />;
        },
      },
      {
        title: I18N.t('状态'),
        width: 60,
        dataIndex: 'status',
        valueEnum: TaskJobStatus,
        render(_text, record) {
          const { status } = record;
          return (
            <Typography.Text type={status === 'Succeed' ? 'success' : 'danger'}>
              {_text}
            </Typography.Text>
          );
        },
      },
      {
        title: I18N.t('结果汇总'),
        dataIndex: 'description',
        ellipsis: true,
      },
      {
        width: 120,
        title: I18N.t('创建者'),
        dataIndex: 'creator',
        render(_text, record) {
          const { creatorId } = record;
          if (creatorId) {
            return <User id={creatorId} />;
          }
          return I18N.t('系统自动计划');
        },
      },
      {
        title: (
          <SortTitle
            order={order}
            label={I18N.t('创建时间')}
            onSort={changeOrder}
            orderKey={'create_time'}
          />
        ),

        width: 130,
        dataIndex: 'create_time',
        render(_text, record) {
          const { createTime } = record;
          return createTime ? dateFormat(new Date(createTime!), 'MM-DD HH:mm:ss') : <Placeholder />;
        },
      },
      {
        title: (
          <SortTitle
            label={I18N.t('执行结束时间')}
            order={order}
            onSort={changeOrder}
            orderKey={'execute_end_time'}
          />
        ),

        width: 130,
        dataIndex: 'execute_end_time',
        render(_text, record) {
          const { executeEndTime } = record;
          return executeEndTime ? (
            dateFormat(new Date(executeEndTime!), 'MM-DD HH:mm:ss')
          ) : (
            <Placeholder />
          );
        },
      },
    ];
  }, [changeOrder, order]);
  return (
    <>
      <GlobalHeaderAction.Emit>
        <Button
          ghost
          style={{ background: 'none' }}
          icon={<IconFontIcon iconName={'shuaxin_24'} />}
          onClick={() => {
            onChange();
          }}
        >
          {I18N.t('刷新')}
        </Button>
      </GlobalHeaderAction.Emit>
      <Layout.Content className={styles.taskHistoryListContainer}>
        <div
          className={classNames('header', styles.header)}
          style={{ pointerEvents: tableLoading ? 'none' : 'auto' }}
        >
          <div className={navStyles.navs}>
            {/*<div className={'nav-item'}>*/}
            {/*  <div className="link" title={'历史任务'}>*/}
            {/*    <span className={'text'}>历史任务</span>*/}
            {/*  </div>*/}
            {/*</div>*/}
          </div>
          <Form
            form={form}
            layout={'inline'}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              flexWrap: 'nowrap',
              paddingLeft: 8,
            }}
          >
            <ConfigProvider locale={zhCN}>
              <Form.Item name={'_range'} style={{ marginRight: 0 }} label={I18N.t('创建时间')}>
                <DatePicker.RangePicker
                  style={{
                    width: 230,
                  }}
                  onChange={() => {
                    onChange(true);
                  }}
                  placeholder={[I18N.t('开始时间'), I18N.t('结束时间')]}
                />
              </Form.Item>
            </ConfigProvider>
            <Form.Item name={'mobileId'} noStyle>
              <MobileSelector
                showPlaceholderOption={false}
                placeholder={
                  <Typography.Text type={'secondary'}>{I18N.t('全部手机')}</Typography.Text>
                }
                style={{ width: 150 }}
                onChange={() => {
                  form.setFieldValue('shopId', undefined);
                  onChange(true);
                }}
              />
            </Form.Item>
            <Form.Item name={'shopId'} noStyle>
              <JobShopsSelector
                history
                showPlaceholderOption={false}
                rpaType={'Browser'}
                style={{ width: 150 }}
                onChange={() => {
                  onChange(true);
                }}
              />
            </Form.Item>
            <Form.Item noStyle name={'status'}>
              <GhJobStatusSelector
                refer={'History'}
                style={{ width: 150 }}
                onChange={() => {
                  onChange(true);
                }}
              />
            </Form.Item>
            <Form.Item noStyle name={'jobType'}>
              <GhJobTypeSelector
                style={{ width: 150 }}
                onChange={() => {
                  onChange(true);
                }}
              />
            </Form.Item>

            <Form.Item noStyle name={'creator'}>
              <Input.Search
                allowClear
                placeholder={I18N.t('请输入达人ID')}
                style={{ width: 160 }}
                onSearch={(val) => {
                  form.setFieldValue('creator', val);
                  onChange(true);
                }}
              />
            </Form.Item>
          </Form>
        </div>
        <StyledTableWrapper className={'main'}>
          <ConfigProvider
            renderEmpty={() => {
              if (loaded.current) {
                return (
                  <div>
                    <div style={{ display: 'inline-flex', gap: 16, width: 600 }}>
                      <span style={{ flex: '0 0 72px' }}>
                        <ColoursIcon size={72} className={'renwu_24'} />
                      </span>
                      <div
                        style={{
                          display: 'inline-flex',
                          flexDirection: 'column',
                          gap: 16,
                          textAlign: 'left',
                        }}
                      >
                        <Typography.Text style={{ fontSize: 16 }}>
                          {I18N.t('历史任务')}
                        </Typography.Text>
                        <Typography.Text type={'secondary'}>
                          {I18N.t(
                            '任务池中的任务在执行完毕后，会存储到“历史任务”中，您可以在此处查看每一笔任务的执行细节',
                          )}
                        </Typography.Text>
                        <Typography.Text type={'secondary'}>
                          {I18N.t('更进一步信息，请您阅读 {{link}} 一文', {
                            link: (
                              <HelpLink href={'/tkshop2/rpaway#pool'}>
                                {I18N.t('任务池与历史任务')}
                              </HelpLink>
                            ),
                          })}
                        </Typography.Text>
                      </div>
                    </div>
                  </div>
                );
              }
              return false;
            }}
          >
            <ProTable<API.GhJobDto>
              params={{ order }}
              actionRef={actionRef}
              request={async (params) => {
                setTableLoading(true);
                try {
                  const { order: _order, current, pageSize } = params;
                  const { shopId, mobileAccountId, _range, jobType, ...rest } = trimValues(
                    form.getFieldsValue(),
                  );
                  let _shopId;
                  if (shopId && mobileAccountId) {
                    _shopId = new Date().getTime();
                  } else if (mobileAccountId) {
                    _shopId = mobileAccountId;
                  } else if (shopId) {
                    _shopId = shopId;
                  }
                  const res = await ghJobsListJobsHistoryGet({
                    pageSize,
                    pageNum: current,
                    createFrom: _range?.[0]?.format('YYYY-MM-DD 00:00:00'),
                    createTo: _range?.[1]?.format('YYYY-MM-DD 23:59:59'),
                    userEvent: true,
                    orderBy: `${_order.key} ${_order.ascend ? 'asc' : 'desc'}`,
                    shopId: _shopId,
                    ...rest,
                    ghPlatformType,
                    jobType,
                  });
                  loaded.current = true;
                  return {
                    total: res.data?.total,
                    data: res.data?.list,
                  };
                } finally {
                  setTableLoading(false);
                }
              }}
              columns={columns}
              {...scrollProTableOptionFn({
                pageId: 'task-list-history',
                scroll: {
                  x: _.sumBy(columns, (item) => {
                    let width;
                    if (item.width) {
                      width = parseInt(item.width);
                    }
                    return isNaN(width) ? 120 : width;
                  }),
                },
                footer: () => {
                  return (
                    <Space>
                      <IconFontIcon iconName="info_24" style={{ color: colors.primaryColor }} />
                      <Typography.Text type="secondary">
                        {I18N.t('只保留最近30天的历史任务')}
                      </Typography.Text>
                    </Space>
                  );
                },
              })}
            />
          </ConfigProvider>
        </StyledTableWrapper>
      </Layout.Content>
    </>
  );
};
export default TaskHistory;
