import constants from '@/constants';
import { shopByShopIdGet } from '@/services/api-ShopAPI/ShopController';
import { Button, Col, Row, Space, Tooltip, Typography } from 'antd';
import type { CSSProperties } from 'react';
import { useMemo } from 'react';
import { useState } from 'react';
import { useRequest, history } from 'umi';
import openShopBrowser, {
  deleteShops,
  getShopArea,
  getShopExtensionType,
  getShopHealthStatus,
  getShopLastSyncTime,
  getShopScore,
  getShopType,
  TkShopDetailVo,
  triggerShopTask,
} from './utils';
import CountryIcon from '@/components/Common/CountryIcon';
import LabelRow, { LabelRowContext } from '@/components/Common/LabelRow';
import { ResponsiveOverflow } from '@/components/Common/MoreDropdown';
import IconFontIcon from '@/components/Common/IconFontIcon';
import { getTaskShelfJobIcon } from '@/pages/TikTok/Live/components/TaskShelfModal';
import InviteByFilterModal from '@/pages/TikTok/components/InviteByFilterModal';
import { GhostModalCaller } from '@/mixins/modal';
import SendMsgByFilterModal from '@/pages/TikTok/components/SendMsgByFilterModal';
import I18N from '@/i18n';
import DemandPaymentModal from '@/pages/TikTok/components/DemandPaymentModal';
import { FunctionCodeComponent } from '@/components/Common/FunctionButton';
import Functions from '@/constants/Functions';
import styled from 'styled-components';
import Placeholder from '@/components/Common/Placeholder';
import ShopIcon from '../assets/shop-icon.png';
import { tkshopShopByShopIdHealthDetailGet } from '@/services/api-TKShopAPI/TkshopShopController';

export const StyledDiv = styled.div<{ add?: boolean }>`
  height: 410px;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 16px;
  vertical-align: top;
  border: 1px solid #dddddd;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.4s;
  &:hover {
    box-shadow: 0 9px 28px 8px rgba(0, 0, 0, 0.05), 0 6px 16px 0 rgba(0, 0, 0, 0.08),
      0 3px 6px -4px rgba(0, 0, 0, 0.12);
    transform: translateY(-4px);
    .action-corner {
      display: block;
    }
    :global {
      .shop-name {
        padding-right: 60px;
      }
    }
  }
  ${(props) =>
    props.add &&
    `
      gap: 16px;
      align-items: center;
      justify-content: center;
      color: #0f7cf4;
      border-style: dashed;
    `}

  .shop-header {
    display: flex;
    flex-wrap: nowrap;
    gap: 10px;
    align-items: flex-start;
    margin-bottom: 8px;
    padding-bottom: 12px;
    overflow: hidden;
    border-bottom: 1px solid #dddddd;
  }

  .action-row {
    margin-top: 8px;
    text-align: center;
    :global {
      .ant-btn-group {
        display: flex;
        .ant-btn {
          flex: 1;
          &.ant-dropdown-trigger {
            flex: 0 0 auto;
            padding: 5px 2px;
          }
        }
      }
    }
  }
  .action-corner {
    position: absolute;
    top: 5px;
    right: 5px;
    display: none;
    background-color: white;
    > a {
      display: inline-block;
      padding: 5px;
    }
  }
`;

const ShopCard = (props: {
  shop: TkShopDetailVo;
  onUpdate: (reset?: boolean) => void;
  onSetting: (id: number) => void;
  style?: CSSProperties;
}) => {
  const { shop, onUpdate, onSetting, style } = props;
  const [shopDetail, setShopDetail] = useState(shop);
  const id = useMemo(() => shopDetail.id!, [shopDetail]);
  const { run: refresh, loading } = useRequest(
    () => {
      return tkshopShopByShopIdHealthDetailGet({
        shopId: id,
      }).then((res) => {
        setShopDetail(res?.data);
      });
    },
    {
      manual: true,
    },
  );

  return (
    <StyledDiv style={style}>
      <div className={'shop-header'}>
        <span style={{ flex: '0 0 64px' }}>
          <img style={{ width: 64, height: 64, display: 'block' }} src={ShopIcon} />
        </span>
        <div
          style={{
            flex: 1,
            overflow: 'hidden',
            display: 'flex',
            flexDirection: 'column',
            gap: 12,
          }}
        >
          <div className={'shop-name'}>
            <Typography.Text ellipsis={{ tooltip: shopDetail.name }}>
              {shopDetail.name}
            </Typography.Text>
          </div>
          <div>
            {shopDetail.description ? (
              <Typography.Paragraph
                ellipsis={{ rows: 2 }}
                style={{ marginBottom: 0, color: '#999', fontSize: 12 }}
              >
                {shopDetail.description}
              </Typography.Paragraph>
            ) : (
              <Typography.Text style={{ color: '#999', fontSize: 12 }}>
                备注：
                <Placeholder />
              </Typography.Text>
            )}
          </div>
        </div>
      </div>
      <main style={{ flex: 1, overflow: 'hidden' }}>
        <LabelRowContext.Provider value={{ labelWidth: 74 }}>
          <Row gutter={[12, 12]}>
            <Col span={24}>
              <LabelRow label="店铺类型">{getShopType(shopDetail)}</LabelRow>
            </Col>
            <Col span={24}>
              <LabelRow label="所属站点">{getShopArea(shopDetail)}</LabelRow>
            </Col>
            <Col span={24}>
              <LabelRow label="浏览器">{getShopExtensionType(shopDetail)}</LabelRow>
            </Col>
            <Col span={24}>
              <LabelRow label="健康状态">{getShopHealthStatus(shopDetail)}</LabelRow>
            </Col>
            <Col span={24}>
              <LabelRow label="店铺得分">{getShopScore(shopDetail)}</LabelRow>
            </Col>
            <Col span={24}>
              <LabelRow label="最近更新">{getShopLastSyncTime(shopDetail)}</LabelRow>
            </Col>
          </Row>
        </LabelRowContext.Provider>
      </main>
      <div className={'action-row'} onClick={(e) => e.stopPropagation()}>
        <ResponsiveOverflow
          itemWidth={110}
          data={[
            {
              node: (_props) => {
                return (
                  <Button
                    icon={<IconFontIcon iconName="fangwendianpu_24" />}
                    onClick={() => {
                      openShopBrowser(shopDetail);
                    }}
                    {..._props}
                  >
                    <span>访问店铺</span>
                  </Button>
                );
              },
              key: 'visit',
            },
            {
              node: (_props) => {
                return (
                  <Button
                    icon={getTaskShelfJobIcon('TS_SyncShopInfo', { iconType: 'iconfont' })}
                    onClick={() => {
                      triggerShopTask([shopDetail], 'TS_SyncShopInfo');
                    }}
                    {..._props}
                  >
                    <span>信息同步</span>
                  </Button>
                );
              },
              key: 'sync',
            },
            {
              key: 'invite',
              node: (_props) => {
                return (
                  <Button
                    icon={getTaskShelfJobIcon('TS_TargetPlanByFilter', { iconType: 'iconfont' })}
                    onClick={() => {
                      GhostModalCaller(<InviteByFilterModal shop={shopDetail} />);
                    }}
                    {..._props}
                  >
                    <span>定向邀约</span>
                  </Button>
                );
              },
            },
            {
              key: 'site_message',
              node: (_props) => {
                return (
                  <Button
                    icon={getTaskShelfJobIcon('TS_IMChatByFilter', { iconType: 'iconfont' })}
                    onClick={() => {
                      GhostModalCaller(<SendMsgByFilterModal shop={shopDetail} />);
                    }}
                    {..._props}
                  >
                    <span>站内消息</span>
                  </Button>
                );
              },
            },
            {
              key: 'sample',
              node: (_props) => {
                return (
                  <Button
                    icon={getTaskShelfJobIcon('TS_SampleApprove', { iconType: 'iconfont' })}
                    onClick={() => {
                      history.push(`/team/${shopDetail.teamId!}/sample/${shopDetail.id!}`);
                    }}
                    {..._props}
                  >
                    <span>{I18N.t('索样审批')}</span>
                  </Button>
                );
              },
            },
            {
              key: 'demandPayment',
              node: (_props) => {
                return (
                  <Button
                    icon={<IconFontIcon iconName="xuyaoxufei_24" />}
                    onClick={() => {
                      GhostModalCaller(
                        <DemandPaymentModal shop={shopDetail} />,
                        'TS_DemandPayment',
                      );
                    }}
                    {..._props}
                  >
                    <span>{I18N.t('未付款订单催付')}</span>
                  </Button>
                );
              },
            },
            {
              key: 'targetPlanClear',
              node: (_props) => {
                return (
                  <Button
                    icon={<IconFontIcon iconName="qingchu_24" />}
                    onClick={() => {
                      triggerShopTask([shopDetail], 'TS_TargetPlanClear');
                    }}
                    {..._props}
                  >
                    <span>{I18N.t('清理定向邀约计划')}</span>
                  </Button>
                );
              },
            },
          ]}
        />
      </div>
      <div className={'action-corner'}>
        <Tooltip title="刷新店铺状态">
          <Typography.Link
            onClick={() => {
              if (!loading) {
                refresh();
              }
            }}
          >
            <IconFontIcon iconName="shuaxin_24" spin={loading} />
          </Typography.Link>
        </Tooltip>
        <Tooltip title="编辑店铺">
          <FunctionCodeComponent
            code={Functions.SHOP_CONFIG}
            as={'a'}
            onClick={() => {
              onSetting(id);
            }}
          >
            <IconFontIcon iconName="shezhi_24" />
          </FunctionCodeComponent>
        </Tooltip>
        <Tooltip title="删除店铺">
          <FunctionCodeComponent
            code={Functions.SHOP_IMPORT_DELETE}
            as={'a'}
            onClick={() => {
              deleteShops([id], () => {
                onUpdate(true);
              });
            }}
          >
            <IconFontIcon iconName="Trash_24" />
          </FunctionCodeComponent>
        </Tooltip>
      </div>
    </StyledDiv>
  );
};
export default ShopCard;
