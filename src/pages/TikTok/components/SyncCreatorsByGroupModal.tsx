import type { GhostModalWrapperComponentProps } from '@/mixins/modal';
import { useMemo, useRef, useState } from 'react';
import { Button, Checkbox, Col, Form, Row, Select, Space, Typography } from 'antd';
import { useAddTask } from '../Live/components/TaskShelfModal';
import { useTaskPoolAddAnimation } from '@/hooks/interactions';
import { showFunctionCodeAlert, useAuthJudgeCallback } from '@/components/Common/FunctionButton';
import { useRequest } from 'umi';
import { trimValues } from '@/utils/utils';
import Functions from '@/constants/Functions';
import { tkshopJobsBatchSyncCreatorsPost } from '@/services/api-TKShopAPI/TkShopJobController';
import I18N from '@/i18n';
import { ProFormText } from '@ant-design/pro-form';
import TkAreaConstants from '@/constants/TkAreaConstants';
import { CountryIcon } from '@/components/IpLocation';
import DMFormItem, { DMFormItemContext } from '@/components/Common/DMFormItem';
import ShopFormField from '@/pages/RpaFlows/components/items/ShopFormField';
import DMModal from '@/components/Common/Modal/DMModal';
import { SelectDeviceField } from '@/pages/RpaFlows/components/SelectDeviceModal';
import {
  getExtensionForSelectDevice,
  getShopIdForSelectDevice,
} from '@/pages/Shop/components/utils';
import buttonStyles from '@/style/button.less';

export type FetchContactValue = 'default' | 'ignore' | 'force';

const SyncCreatorsByGroupModal = (
  props: GhostModalWrapperComponentProps & {
    group: _.Dictionary<API.TkshopCreatorDetailVo[]>;
    fetch_contact?: FetchContactValue;
  },
) => {
  const { group, fetch_contact = 'default', modalProps } = props;
  const [open, setOpen] = useState(true);
  const [form] = Form.useForm();
  const { play } = useTaskPoolAddAnimation();
  const { run: add } = useAddTask();
  const add_to_task = useRef(false);
  const hasAuth = useAuthJudgeCallback();

  const { run: submit, loading } = useRequest(
    async (e) => {
      add_to_task.current = false;
      const { device, _regions, ...values } = trimValues(await form.validateFields());
      if (!hasAuth([Functions.RPA_RUN, Functions.RPA_LIST])) {
        showFunctionCodeAlert();
      } else {
        const entries = Object.entries(values);
        for (let i = 0; i < entries.length; i++) {
          const [region, value] = entries[i];
          const { enabled, fetch_contact, shop, creators } = value;
          const shopCreators = {};
          if (enabled) {
            shopCreators[shop.id] = {
              ghCreatorIds: creators.map((item) => item.id!),
              advanceSettings: {
                fetch_contact,
              },
            };
            await tkshopJobsBatchSyncCreatorsPost({
              shopCreators,
              deviceId: device.deviceId!,
            });
          }
        }
        play(e, 'TS_SyncCreator');
        setOpen(false);
      }
    },
    {
      manual: true,
    },
  );
  const { run: addToTask, loading: adding } = useRequest(
    async (e: any) => {
      add_to_task.current = true;
      const { device, _regions, ...values } = trimValues(await form.validateFields());
      const items: API.AddTaskDrawerItem[] = [];
      const entries = Object.entries(values);
      entries.forEach(([region, value]) => {
        const { enabled, fetch_contact, shop, creators } = value;
        if (enabled) {
          creators.forEach((item) => {
            items.push({
              rpaType: 'Browser',
              accountId: shop.id!,
              taskType: 'TS_SyncCreator',
              creatorId: item.id!,
              parameter: JSON.stringify({
                fetch_contact,
              }),
            });
          });
        }
      });
      await add(
        {
          items,
        },
        e,
      );
      setOpen(false);
    },
    {
      manual: true,
    },
  );
  const footer = useMemo(() => {
    return (
      <Space>
        <Button onClick={addToTask} loading={adding} className={buttonStyles.successBtn}>
          {I18N.t('放到任务抽屉')}
        </Button>
        <Button onClick={submit} loading={loading} type={'primary'}>
          {I18N.t('立即执行')}
        </Button>
        <Button
          type={'default'}
          onClick={() => {
            setOpen(false);
          }}
        >
          {I18N.t('取消')}
        </Button>
      </Space>
    );
  }, [addToTask, adding, loading, submit]);
  const list = useMemo(() => {
    const _nodes: any[] = [];
    Object.entries(group).forEach(([region, creators]) => {
      _nodes.push(
        <div key={region}>
          <ProFormText
            hidden
            name={region}
            initialValue={{ enabled: true, creators, fetch_contact }}
          />
          <Form.Item shouldUpdate noStyle>
            {(f) => {
              const checked = f.getFieldValue([region, 'enabled']);
              const area = TkAreaConstants.areas[region];
              const label = TkAreaConstants.areaLabels[region];

              return (
                <div>
                  <Form.Item valuePropName={'checked'} name={[region, 'enabled']}>
                    <Checkbox>
                      <Space>
                        <CountryIcon size={16} country={area} />
                        <span>{label}</span>
                        <Typography.Text style={{ color: '#999' }}>
                          （共选中 {creators?.length?.toLocaleString()} 个达人）
                        </Typography.Text>
                      </Space>
                    </Checkbox>
                  </Form.Item>

                  <Row gutter={[16, 16]} style={{ paddingLeft: 24 }}>
                    <Col span={12}>
                      <DMFormItem
                        label={'店铺'}
                        style={{ marginBottom: 0 }}
                        name={[region, 'shop']}
                        validateFirst
                        rules={[
                          {
                            validator(rule, val) {
                              if (checked && !val) {
                                return Promise.reject(new Error(I18N.t(`请选择店铺`)));
                              }
                              return Promise.resolve();
                            },
                          },
                        ]}
                      >
                        <ShopFormField area={area} />
                      </DMFormItem>
                    </Col>
                    <Col span={12}>
                      <DMFormItem
                        label={'联系方式'}
                        style={{ marginBottom: 0 }}
                        name={[region, 'fetch_contact']}
                        rules={[{ required: checked, message: I18N.t(`联系方式更新策略`) }]}
                      >
                        <Select
                          dropdownMatchSelectWidth={false}
                          options={[
                            {
                              label: I18N.t('所有联系方式都为空时才更新'),
                              value: 'default',
                            },
                            {
                              label: I18N.t('强制更新'),
                              value: 'force',
                            },
                            {
                              label: I18N.t('不更新'),
                              value: 'ignore',
                            },
                          ]}
                        />
                      </DMFormItem>
                    </Col>
                  </Row>
                </div>
              );
            }}
          </Form.Item>
        </div>,
      );
    });
    return _nodes;
  }, [fetch_contact, group]);
  const size = useMemo(() => {
    let count = 0;
    Object.values(group).forEach((creators) => {
      count += creators.length;
    });
    return count;
  }, [group]);
  return (
    <DMModal
      width={640}
      headless
      footer={footer}
      bodyStyle={{
        paddingBottom: 0,
      }}
      open={open}
      onCancel={() => {
        setOpen(false);
      }}
      {...modalProps}
    >
      <div>
        <div
          style={{
            marginBottom: 16,
          }}
        >
          <Typography.Text type={'secondary'}>
            {I18N.t(`对选中的 ${size?.toLocaleString()} 个达人进行基础信息更新：`)}
          </Typography.Text>
        </div>
        <Form
          form={form}
          requiredMark={false}
          onValuesChange={() => {
            form.validateFields(['_regions']);
          }}
        >
          <DMFormItemContext.Provider value={{ labelWidth: 80 }}>
            <Form.Item
              name={'_regions'}
              rules={[
                {
                  validator() {
                    const { _regions, device, ...values } = form.getFieldsValue();
                    if (!Object.values(values).some(({ enabled }) => !!enabled)) {
                      return Promise.reject(new Error(I18N.t('请选择要更新的达人')));
                    }
                    // 拿到所有的region 底下的shop.extension 看是否互斥
                    const shops = Object.values(values)
                      .filter((item) => item?.enabled)
                      .map((item) => {
                        return item?.shop;
                      })
                      .filter(Boolean);
                    const extensionShops = shops.filter((shop) => {
                      return shop.extension === 'extension';
                    });
                    if (extensionShops.length > 1) {
                      return Promise.reject(new Error(I18N.t('不支持选择多个插件店铺')));
                    }
                    const hasHuaYang = shops.some((shop) => {
                      return shop.extension !== 'extension';
                    });
                    if (extensionShops.length && hasHuaYang) {
                      return Promise.reject(
                        new Error(I18N.t('不支持同时选择插件店铺和花漾TK店铺')),
                      );
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            >
              <div style={{ display: 'flex', flexDirection: 'column', gap: 24 }}>{list}</div>
            </Form.Item>
            <Form.Item shouldUpdate noStyle>
              {(f) => {
                const { _regions, device, ...values } = f.getFieldsValue();
                // 拿到所有的region 底下的shop.extension 看是否互斥
                const shops = Object.values(values)
                  .filter((item) => item?.enabled)
                  .map((item) => {
                    return item?.shop;
                  })
                  .filter(Boolean);
                return (
                  <DMFormItem
                    style={{ paddingLeft: 24 }}
                    label={I18N.t('运行设备')}
                    name={'device'}
                    rules={[
                      {
                        validator(_rule, value) {
                          if (value || add_to_task.current) {
                            return Promise.resolve();
                          }
                          return Promise.reject(I18N.t('请指定运行设备'));
                        },
                      },
                    ]}
                  >
                    <SelectDeviceField
                      disabled={!shops.length}
                      shopId={shops.length === 1 ? getShopIdForSelectDevice(shops[0]) : undefined}
                      extension={getExtensionForSelectDevice(shops)}
                    />
                  </DMFormItem>
                );
              }}
            </Form.Item>
          </DMFormItemContext.Provider>
        </Form>
      </div>
    </DMModal>
  );
};
export default SyncCreatorsByGroupModal;
