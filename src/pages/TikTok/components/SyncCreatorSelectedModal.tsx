import { useCallback, useMemo, useRef, useState } from 'react';
import DMModal from '@/components/Common/Modal/DMModal';
import I18N from '@/i18n';
import { Button, Form, Space, Typography } from 'antd';
import DMFormItem, { DMFormItemContext } from '@/components/Common/DMFormItem';
import SelectedTargetFormItem from '@/pages/TikTok/components/SelectedTargetFormItem';
import { useRequest } from '@@/plugin-request/request';
import TkAreaConstants from '@/constants/TkAreaConstants';
import {
  getTaskShelfJobIcon,
  TaskShelfJobType,
  useAddTask,
} from '@/pages/TikTok/Live/components/TaskShelfModal';
import { trimValues } from '@/utils/utils';
import type { GhostModalWrapperComponentProps } from '@/mixins/modal';
import { GhostModalCaller } from '@/mixins/modal';
import { useTaskPoolAddAnimation } from '@/hooks/interactions';
import { tkshopJobsBatchSyncCreatorsPost } from '@/services/api-TKShopAPI/TkShopJobController';
import type _ from 'lodash';
import buttonStyles from '@/style/button.less';
import { SelectDeviceField } from '@/pages/RpaFlows/components/SelectDeviceModal';
import { SyncCreatorFetchContactFormItem } from '@/pages/Schedule/components/util';
import DMConfirm from '@/components/Common/DMConfirm';
import FlowReadmeAsidePanel, {
  FlowReadmeAsidePanelWidth,
} from '@/components/Common/MarkdownView/FlowReadmeAsidePanel';
import { showFunctionCodeAlert, useAuthJudgeCallback } from '@/components/Common/FunctionButton';
import Functions from '@/constants/Functions';
import ShopFormField from '@/pages/RpaFlows/components/items/ShopFormField';
import {
  getExtensionForSelectDevice,
  getShopIdForSelectDevice,
} from '@/pages/Shop/components/utils';
import type { FetchContactValue } from '@/pages/TikTok/components/SyncCreatorsByGroupModal';
import SyncCreatorsByGroupModal from '@/pages/TikTok/components/SyncCreatorsByGroupModal';

const SyncCreatorSelectedModal = (
  props: GhostModalWrapperComponentProps & {
    selected: API.TkshopCreatorDetailVo[];
    region?: string;
    fetch_contact?: FetchContactValue;
    refer?: 'list' | 'detail';
  },
) => {
  const {
    selected,
    fetch_contact = 'default',
    refer = 'detail',
    region: _region,
    modalProps,
  } = props;
  const [visible, setVisible] = useState(true);
  const [form] = Form.useForm();
  const { play } = useTaskPoolAddAnimation();
  const region = useMemo(() => {
    if (_region) {
      return _region;
    }
    return selected[0].region!;
  }, [_region, selected]);
  const add_to_task = useRef(false);
  const { run: add } = useAddTask();
  const hasAuth = useAuthJudgeCallback();
  const { run: addToTask, loading: adding } = useRequest(
    async (e: any) => {
      add_to_task.current = true;
      const { shop, ...rest } = await form.validateFields();
      await add(
        {
          items: selected.map((item) => {
            return {
              rpaType: 'Browser',
              accountId: shop.id!,
              taskType: 'TS_SyncCreator',
              creatorId: item.id!,
              parameter: JSON.stringify(trimValues(rest)),
            };
          }),
        },
        e,
      ).then(() => {
        setVisible(false);
      });
    },
    {
      manual: true,
    },
  );
  const { run: submit, loading } = useRequest(
    async (e: any) => {
      add_to_task.current = false;
      const { device, shop, fetch_contact } = trimValues(await form.validateFields());
      if (!hasAuth([Functions.RPA_LIST, Functions.RPA_RUN])) {
        showFunctionCodeAlert();
      } else {
        await tkshopJobsBatchSyncCreatorsPost({
          shopCreators: {
            [shop.id]: {
              ghCreatorIds: selected.map((item) => {
                return item.id!;
              }),
              advanceSettings: {
                fetch_contact,
              },
            },
          },
          deviceId: device.deviceId!,
        });
        play(e, 'TS_SyncCreator');
        setVisible(false);
      }
    },
    {
      manual: true,
    },
  );
  const footer = useMemo(() => {
    if (refer === 'detail') {
      return (
        <Space>
          <Button onClick={submit} loading={loading}>
            {I18N.t('立即执行')}
          </Button>
          <Button onClick={addToTask} loading={adding} type={'primary'}>
            {I18N.t('放到任务抽屉')}
          </Button>
          <Button
            type={'default'}
            onClick={() => {
              setVisible(false);
            }}
          >
            {I18N.t('取消')}
          </Button>
        </Space>
      );
    }
    return (
      <Space>
        <Button onClick={addToTask} loading={adding} className={buttonStyles.successBtn}>
          {I18N.t('放到任务抽屉')}
        </Button>
        <Button onClick={submit} loading={loading} type={'primary'}>
          {I18N.t('立即执行')}
        </Button>
        <Button
          type={'default'}
          onClick={() => {
            setVisible(false);
          }}
        >
          {I18N.t('取消')}
        </Button>
      </Space>
    );
  }, [addToTask, adding, loading, refer, submit]);

  return (
    <DMModal
      title={I18N.t('达人信息更新')}
      bodyStyle={{ paddingBottom: 0 }}
      width={640 + FlowReadmeAsidePanelWidth}
      footer={footer}
      open={visible}
      asideWrapperStyle={{
        flex: `0 0 ${FlowReadmeAsidePanelWidth}px`,
        height: 491,
      }}
      asidePanel={<FlowReadmeAsidePanel bizCode={'tkshop.TS_SyncCreator'} />}
      onCancel={() => {
        setVisible(false);
      }}
      {...modalProps}
    >
      <DMFormItemContext.Provider value={{ disableLabelMuted: false, labelWidth: 100 }}>
        <Form
          requiredMark={false}
          form={form}
          style={{ display: 'flex', flexDirection: 'column', overflow: 'hidden' }}
        >
          <div style={{ flex: 1, overflow: 'hidden' }}>
            <SelectedTargetFormItem data={selected} />
            <DMFormItem label={I18N.t('任务类型')}>
              <Space align={'center'} size={4}>
                <Typography.Text>{getTaskShelfJobIcon('TS_SyncCreator')}</Typography.Text>
                <span>{TaskShelfJobType.TS_SyncCreator}</span>
              </Space>
            </DMFormItem>
            <SyncCreatorFetchContactFormItem initialValue={fetch_contact} />
            <DMFormItem
              label={I18N.t('店铺')}
              name={'shop'}
              rules={[{ required: true, message: I18N.t('请选择店铺') }]}
            >
              <ShopFormField area={TkAreaConstants.areas[region!]} />
            </DMFormItem>
            <Form.Item noStyle shouldUpdate>
              {(f) => {
                return (
                  <DMFormItem
                    label={I18N.t('运行设备')}
                    name={'device'}
                    rules={[
                      {
                        validator(_rule, value) {
                          if (value || add_to_task.current) {
                            return Promise.resolve();
                          }
                          return Promise.reject(I18N.t('请指定运行设备'));
                        },
                      },
                    ]}
                  >
                    <SelectDeviceField
                      shopId={getShopIdForSelectDevice(f.getFieldValue('shop'))}
                      extension={getExtensionForSelectDevice(f.getFieldValue('shop'))}
                    />
                  </DMFormItem>
                );
              }}
            </Form.Item>
          </div>
        </Form>
      </DMFormItemContext.Provider>
    </DMModal>
  );
};
export default SyncCreatorSelectedModal;

type Props = {
  group: _.Dictionary<API.TkshopCreatorDetailVo[]>;
  fetch_contact?: FetchContactValue;
};
export function useSyncCreatorsModal() {
  return useCallback((args: Props) => {
    const { group, fetch_contact } = args;
    let count = 0;
    Object.values(group).forEach((creators) => {
      count += creators.length;
    });
    if (count === 0) {
      DMConfirm({
        title: I18N.t('达人没有所属国家'),
        type: 'info',
      });
      return;
    }
    const areas = Object.keys(group);
    if (areas.length === 1) {
      const area = areas[0];
      GhostModalCaller(
        <SyncCreatorSelectedModal
          refer={'list'}
          region={areas[0]}
          fetch_contact={fetch_contact}
          selected={group[area]}
        />,
      );
    } else {
      GhostModalCaller(<SyncCreatorsByGroupModal fetch_contact={fetch_contact} group={group} />);
    }
  }, []);
}
