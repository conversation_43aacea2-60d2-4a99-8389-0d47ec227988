import I18N from '@/i18n';
import {
  Button,
  ConfigProvider,
  DatePicker,
  Form,
  Input,
  Layout,
  message,
  Space,
  Tooltip,
  Typography,
} from 'antd';
import styles from './index.less';
import classNames from 'classnames';
import navStyles from '@/style/nav.less';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { scrollProTableOptionFn } from '@/mixins/table';
import { dateFormat, trimValues } from '@/utils/utils';
import {
  ghJobsBatchCancelJobsDelete,
  ghJobsCancelAllJobsDelete,
  ghJobsCancelJobDelete,
  ghJobsListJobsPendingGet,
} from '@/services/api-TKGHAPI/GhJobController';
import { useOrder } from '@/components/Sort/SortDropdown';
import SortTitle from '@/components/Sort/SortTitle';
import { TaskJobStatus } from '@/pages/Task/constants';
import Placeholder from '@/components/Common/Placeholder';
import IconFontIcon from '@/components/Common/IconFontIcon';
import { GhostModalCaller } from '@/mixins/modal';
import { useCallback, useMemo, useRef, useState } from 'react';
import { TaskDetail } from '@/pages/TaskHistory';
import DMConfirm from '@/components/Common/DMConfirm';
import GhJobTypeSelector from '@/components/Common/Selector/GhJobTypeSelector';
import GhJobStatusSelector from '@/components/Common/Selector/GhJobStatusSelector';
import _ from 'lodash';
import JobShopsSelector from '@/components/Common/Selector/JobShopsSelector';
import { TikTokAccountTypeNode } from '@/pages/TikTok/components/TikTokAccountType';
import zhCN from 'antd/lib/locale-provider/zh_CN';
import { useRequest } from '@@/plugin-request/request';
import { GlobalHeaderAction } from '@/utils/pageUtils';
import ColoursIcon from '@/components/Common/ColoursIcon';
import MobileSelector from '@/components/Common/Selector/MobileSelector';
import HelpLink from '@/components/HelpLink';
import { User } from '@/components/Common/UserAvatarAndName';
import useCurrentRole from '@/hooks/useCurrentRole';
import EventEmitter from 'events';
import {
  getTaskShelfJobIcon,
  TaskShelfJobType,
} from '@/pages/TikTok/Live/components/TaskShelfModal';
import { getTaskCreatorColumn, getTaskParamsColumn } from '@/pages/Task/utils';
import MobileDeviceById from '@/components/Common/MobileDevice';
import InfoTip from '@/components/Tips/InfoTip';
import { StyledTableWrapper } from '@/style/styled';
import { Device } from '@/pages/Setting/components/SelectDeviceModal';

const eventEmitter = new EventEmitter();
export function triggerTaskCountRefresh() {
  eventEmitter.emit('refresh');
}
export function offTaskCountRefresh(fn: (...args: any[]) => void) {
  eventEmitter.removeListener('refresh', fn);
}
export function onTaskCountRefresh(fn: (...args: any[]) => void) {
  eventEmitter.addListener('refresh', fn);
}
const Task = () => {
  const { order, changeOrder } = useOrder(
    {
      key: 'create_time',
      ascend: false,
    },
    'tkshop_task_v20250121',
  );
  const actionRef = useRef<ActionType>();
  const [selected, changeSelected] = useState<[]>([]);
  const [form] = Form.useForm();
  const userEvent = useRef(true);
  const timer = useRef<any>();
  const loaded = useRef(false);
  const { run: onChange } = useRequest(
    (reset?: boolean) => {
      userEvent.current = true;
      clearTimeout(timer.current);
      if (reset) {
        return actionRef.current?.reloadAndRest?.();
      } else {
        return actionRef.current?.reload?.();
      }
    },
    {
      manual: true,
    },
  );
  const ghPlatformType = 'tkshop';
  const [tableLoading, setTableLoading] = useState(true); // 新增，默认true
  const showConfirmInfo = useCallback(() => {
    DMConfirm({
      width: 700,
      type: 'info',
      title: I18N.t('关于任务池的调度机制'),
      content: (
        <div style={{ display: 'flex', flexDirection: 'column', gap: 12, fontSize: 14 }}>
          <div>
            一个店铺对应着一个浏览器分身，同一台电脑上，一个店铺同时只能执行一个流程（其它的流程需要排队）
          </div>
          <div>手机也是同样的道理，同一部手机，同时只能执行一个流程（其它的流程也需要排队）</div>
          <div>
            因此，当您对同一个店铺或者同一部手机，同时创建了很多个流程，这些流程会按照优先级与先后顺序，依次排队执行，这就是您为什么看到有些状态为“调度中”的流程，迟迟不能执行的原因，此时，请保持耐心，等待流程的依次执行
          </div>
          <div>
            <HelpLink href={'/tkshop2/rpaway#pool'} />
          </div>
        </div>
      ),
    });
  }, []);
  const columns: ProColumns<API.GhJobDto>[] = useMemo(() => {
    return [
      {
        title: I18N.t('任务类型'),
        dataIndex: 'jobType',
        render(_text, record) {
          const { jobType } = record;
          const text = TaskShelfJobType[jobType!];
          return (
            <Typography.Link
              onClick={() => {
                GhostModalCaller(<TaskDetail job={record} alive />);
              }}
            >
              <Space align={'center'} size={4}>
                {getTaskShelfJobIcon(jobType)}
                <span>{text}</span>
              </Space>
            </Typography.Link>
          );
        },
        width: 234,
      },
      {
        title: I18N.t('目标达人'),
        dataIndex: 'creator',
        width: 160,
        render(_text, record) {
          return getTaskCreatorColumn(record);
        },
      },
      {
        title: I18N.t('主要参数'),
        dataIndex: 'params',
        width: 240,
        ellipsis: true,
        renderText(_text, record) {
          return getTaskParamsColumn(record, 'GhJobDto');
        },
      },
      {
        title: I18N.t('分身/手机账号'),
        width: 140,
        dataIndex: 'shopId',
        ellipsis: true,
        render(_text, record) {
          const { rpaType, shopId, shopName, ghCreatorName, jobType } = record;
          if (!shopId) {
            return <Placeholder />;
          }
          let ghScheduleType = record.ghScheduleType;
          if (rpaType === 'Mobile') {
            ghScheduleType = 'Mobile';
          }
          if (ghScheduleType === 'Unknown' || !ghScheduleType) {
            ghScheduleType = 'Normal';
          }
          if (jobType === 'TS_AddContacts') {
            return <MobileDeviceById id={shopId} />;
          }
          return (
            <TikTokAccountTypeNode
              name={shopName}
              handle={ghCreatorName!}
              mode={'iconText'}
              id={shopId}
              type={ghScheduleType}
            />
          );
        },
      },
      {
        title: I18N.t('设备'),
        dataIndex: 'deviceName',
        ellipsis: true,
        width: 140,
        render(dom, record) {
          const { device, deviceName } = record;
          if (device) {
            return <Device data={device} />;
          }
          if (deviceName) {
            return (
              <Typography.Text ellipsis={{ tooltip: deviceName }}>{deviceName}</Typography.Text>
            );
          }
          return <Placeholder />;
        },
      },
      {
        title: I18N.t('状态'),
        width: 90,
        dataIndex: 'status',
        render(_text, record) {
          const { status } = record;
          return (
            <Tooltip
              title={
                status === 'Pending' ? (
                  <div>
                    <div>为什么流程不能立即执行？</div>
                    <Typography.Link onClick={showConfirmInfo}>了解更多</Typography.Link>
                  </div>
                ) : null
              }
            >
              <Space align={'center'} size={4}>
                <Typography.Link>{<IconFontIcon iconName={'loading_24'} spin />}</Typography.Link>
                <span>{TaskJobStatus[status!]}</span>
              </Space>
            </Tooltip>
          );
        },
      },
      {
        title: I18N.t('描述'),
        dataIndex: 'description',
        ellipsis: true,
        renderText(_text, record) {
          const { description, status } = record;
          if (status !== 'Pending') {
            return <Placeholder />;
          }
          return description || I18N.t('等待调度');
        },
      },
      {
        width: 120,
        title: I18N.t('创建者'),
        dataIndex: 'creator',
        render(_text, record) {
          const { creatorId } = record;
          if (creatorId) {
            return <User id={creatorId} />;
          }
          return I18N.t('系统自动计划');
        },
      },
      {
        width: 130,
        title: (
          <SortTitle
            label={I18N.t('创建时间')}
            order={order}
            onSort={changeOrder}
            orderKey={'create_time'}
          />
        ),
        dataIndex: 'create_time',
        render(_text, record) {
          const { createTime } = record;
          return createTime ? dateFormat(new Date(createTime!), 'MM-DD HH:mm:ss') : <Placeholder />;
        },
      },

      {
        width: 60,
        title: I18N.t('操作'),
        valueType: 'option',
        render(_text, record) {
          return (
            <Typography.Link
              type={'danger'}
              onClick={() => {
                DMConfirm({
                  title: I18N.t('确定要取消当前任务吗'),
                  onOk() {
                    ghJobsCancelJobDelete({
                      jobId: record.id!,
                    }).then(() => {
                      changeSelected((prev) => {
                        const newState = [...prev];
                        const index = newState.findIndex((i) => i === record.id);
                        if (index === -1) {
                          return prev;
                        }
                        newState.splice(index, 1);
                        return newState;
                      });
                      message.success(I18N.t('任务取消成功'));
                      onChange();
                    });
                  },
                });
              }}
            >
              {I18N.t('取消')}
            </Typography.Link>
          );
        },
      },
    ];
  }, [changeOrder, onChange, order]);
  const { isSpecialRole } = useCurrentRole();
  return (
    <>
      <GlobalHeaderAction.Emit>
        <Button
          ghost
          style={{ background: 'none' }}
          icon={<IconFontIcon iconName={'shuaxin_24'} />}
          onClick={() => {
            onChange();
          }}
        >
          {I18N.t('刷新')}
        </Button>
      </GlobalHeaderAction.Emit>
      <Layout.Content className={styles.taskListContainer}>
        <div
          className={classNames('header', styles.header)}
          style={{ pointerEvents: tableLoading ? 'none' : 'auto' }}
        >
          <div className={navStyles.navs}>
            {/*<div className={'nav-item'}>*/}
            {/*  <div className="link" title={'任务池'}>*/}
            {/*    <span className={'text'}>任务池</span>*/}
            {/*  </div>*/}
            {/*</div>*/}
          </div>
          <Form
            form={form}
            layout={'inline'}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              flexWrap: 'nowrap',
              paddingLeft: 8,
            }}
          >
            <ConfigProvider locale={zhCN}>
              <Form.Item name={'_range'} style={{ marginRight: 0 }} label={I18N.t('创建时间')}>
                <DatePicker.RangePicker
                  style={{
                    width: 230,
                  }}
                  onChange={() => {
                    onChange(true);
                  }}
                  placeholder={[I18N.t('开始时间'), I18N.t('结束时间')]}
                />
              </Form.Item>
            </ConfigProvider>
            <Form.Item name={'mobileId'} noStyle>
              <MobileSelector
                showPlaceholderOption={false}
                placeholder={
                  <Typography.Text type={'secondary'}>{I18N.t('全部手机')}</Typography.Text>
                }
                style={{ width: 150 }}
                onChange={() => {
                  form.setFieldValue('shopId', undefined);
                  onChange(true);
                }}
              />
            </Form.Item>
            <Form.Item name={'shopId'} noStyle>
              <JobShopsSelector
                rpaType={'Browser'}
                showPlaceholderOption={false}
                history={false}
                style={{ width: 150 }}
                onChange={() => {
                  onChange(true);
                }}
              />
            </Form.Item>
            <Form.Item noStyle name={'status'}>
              <GhJobStatusSelector
                refer={'Pending'}
                style={{ width: 150 }}
                onChange={() => {
                  onChange(true);
                }}
              />
            </Form.Item>
            <Form.Item name={'jobType'} noStyle>
              <GhJobTypeSelector
                style={{ width: 150 }}
                onChange={() => {
                  onChange(true);
                }}
              />
            </Form.Item>
            <Form.Item noStyle name={'creator'}>
              <Input.Search
                allowClear
                placeholder={I18N.t('请输入达人ID')}
                style={{ width: 160 }}
                onSearch={(val) => {
                  form.setFieldValue('creator', val);
                  onChange(true);
                }}
              />
            </Form.Item>
          </Form>
        </div>
        <StyledTableWrapper className={'main'}>
          <ConfigProvider
            renderEmpty={() => {
              if (loaded.current) {
                return (
                  <div>
                    <div style={{ display: 'inline-flex', gap: 16, width: 600 }}>
                      <span style={{ flex: '0 0 72px' }}>
                        <ColoursIcon size={72} className={'renwuchi_24'} />
                      </span>
                      <div
                        style={{
                          display: 'inline-flex',
                          flexDirection: 'column',
                          gap: 16,
                          textAlign: 'left',
                        }}
                      >
                        <Typography.Text style={{ fontSize: 16 }}>
                          {I18N.t('任务池')}
                        </Typography.Text>
                        <Typography.Text type={'secondary'}>
                          {I18N.t('当您创建了较多的流程任务时，会在任务池中进行排队，依次处理')}
                        </Typography.Text>
                        <Typography.Text type={'secondary'}>
                          {I18N.t(
                            '举例，您同时创建了向2000个达人发送手机私信的任务，但只有两部手机，如果不间断执行，手机账号很容易被封，此时，就需要通过任务池将这2000个私信任务分摊到不同的时间、不同的手机账号依次执行。',
                          )}
                        </Typography.Text>
                        <Typography.Text type={'secondary'}>
                          {I18N.t(
                            '您也无需担心同一时间同时执行若干个不同类型的流程任务而产生的干扰问题，此时会由任务池根据不同流程任务的优先级自行调度。',
                          )}
                        </Typography.Text>
                        <Typography.Text type={'secondary'}>
                          {I18N.t('更进一步信息，请您阅读 {{link}} 一文', {
                            link: (
                              <HelpLink href={'/tkshop2/rpaway#pool'}>
                                {I18N.t('任务池与历史任务')}
                              </HelpLink>
                            ),
                          })}
                        </Typography.Text>
                      </div>
                    </div>
                  </div>
                );
              }
              return false;
            }}
          >
            <ProTable<API.GhJobDto>
              actionRef={actionRef}
              params={{ order }}
              rowSelection={{
                selectedRowKeys: selected,
                onChange(selectedRowKeys) {
                  changeSelected(selectedRowKeys);
                },
              }}
              {...scrollProTableOptionFn({
                pageId: 'task-list',
                footer: (dataSource: readonly any[]) => {
                  return (
                    <>
                      <Tooltip title={I18N.t('请选中后操作')} placement={'topLeft'}>
                        <Space>
                          <Button
                            ghost
                            type={'primary'}
                            onClick={() => {
                              DMConfirm({
                                width: 460,
                                title: I18N.t('确定要取消选中的任务吗？'),
                                content: I18N.t('任务一旦取消不可恢复，只能重新发起'),
                                onOk() {
                                  ghJobsBatchCancelJobsDelete({
                                    jobIds: selected.join(','),
                                  }).then(() => {
                                    message.success(I18N.t('选中的任务已取消'));
                                    changeSelected([]);
                                    actionRef.current?.reload();
                                  });
                                },
                              });
                            }}
                            icon={<IconFontIcon iconName={'Close-Circle_24'} />}
                            disabled={!selected.length}
                          >
                            {I18N.t('批量取消')}
                          </Button>
                        </Space>
                      </Tooltip>
                      <Button
                        hidden={!dataSource?.length || !isSpecialRole}
                        type={'ghost'}
                        onClick={() => {
                          DMConfirm({
                            width: 460,
                            title: I18N.t('确定要取消所有的任务吗？'),
                            content: I18N.t('任务一旦取消不可恢复，只能重新发起'),
                            onOk() {
                              // 这里不能传{},传了会导致取消不了任务
                              // @ts-ignore
                              ghJobsCancelAllJobsDelete().then(() => {
                                message.success(I18N.t('所有任务已取消'));
                                actionRef.current?.reload();
                              });
                            },
                          });
                        }}
                        danger
                        icon={<IconFontIcon iconName={'Close-Circle_24'} />}
                      >
                        {I18N.t('全部取消')}
                      </Button>
                      <InfoTip
                        message={
                          <div>
                            <span>
                              {I18N.t(
                                '任务池中的任务从调度中到开始执行，大概需要1-2分钟左右的时间',
                              )}
                            </span>
                            <Typography.Link onClick={showConfirmInfo} style={{ marginLeft: 16 }}>
                              了解更多
                            </Typography.Link>
                          </div>
                        }
                      />
                    </>
                  );
                },
                scroll: {
                  x: _.sumBy(columns, (item) => {
                    let width;
                    if (item.width) {
                      width = parseInt(item.width);
                    }
                    return isNaN(width) ? 120 : width;
                  }),
                },
                pagination: {
                  onChange: () => {
                    userEvent.current = true;
                  },
                },
              })}
              columns={columns}
              request={async (params) => {
                setTableLoading(true);
                try {
                  const { current, pageSize, order: _order } = params;
                  const { shopId, mobileAccountId, _range, jobType, ...rest } = trimValues(
                    form.getFieldsValue(),
                  );
                  let _shopId;
                  if (shopId && mobileAccountId) {
                    _shopId = new Date().getTime();
                  } else if (mobileAccountId) {
                    _shopId = mobileAccountId;
                  } else if (shopId) {
                    _shopId = shopId;
                  }
                  const res = await ghJobsListJobsPendingGet(
                    {
                      pageSize,
                      pageNum: current,
                      createFrom: _range?.[0]?.format('YYYY-MM-DD 00:00:00'),
                      createTo: _range?.[1]?.format('YYYY-MM-DD 23:59:59'),
                      userEvent: userEvent.current,
                      orderBy: `${_order.key} ${_order.ascend ? 'asc' : 'desc'}`,
                      shopId: _shopId,
                      ...rest,
                      ghPlatformType,
                      jobType,
                    },
                    {
                      errorHandler(err: any) {
                        if (userEvent.current) {
                          message.error(err?.message);
                        } else {
                          console.error(err);
                        }
                      },
                    },
                  );
                  clearTimeout(timer.current);
                  triggerTaskCountRefresh();
                  loaded.current = true;
                  timer.current = setTimeout(() => {
                    userEvent.current = false;
                    actionRef.current?.reload();
                  }, 1000 * 10);
                  return {
                    total: res.data?.total,
                    data: res.data?.list,
                  };
                } finally {
                  setTableLoading(false);
                }
              }}
            />
          </ConfigProvider>
        </StyledTableWrapper>
      </Layout.Content>
    </>
  );
};
export default Task;
