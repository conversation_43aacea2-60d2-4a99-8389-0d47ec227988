@import '/src/style/theme';
@import '/src/style/size';

@global-tips-bar-height: 50px;

.content-wrap {
  display: flex;
  flex-direction: column;
  min-width: 1280px;
  height: 100%;
  overflow: auto;
  &:global(.has-global-tips-bar) {
    height: calc(100% - @global-tips-bar-height);
  }
}

.new-app-version-tips :global(.ant-message-notice-content) {
  max-width: 600px;
}

.sys-broadcast-tips :global(.ant-message-notice-content) {
  max-width: 600px;
}

.sys-broadcast-inner {
  display: inline-flex;
  align-items: center;
  > span {
    flex: 1;
  }
  > span:first-child {
    flex: 0 0 auto;
  }
  :global(.dm-iconFontIcon) {
    flex: 0 0 auto;
    margin-left: auto;
  }
}

