import I18N from '@/i18n';
import { Badge, Layout, Menu, message, notification, Tooltip, Typography } from 'antd';
import type { RequestConfig, RunTimeLayoutConfig } from 'umi';
import { useLocation, useRouteMatch } from 'umi';
import type { RequestOptionsInit, ResponseError } from 'umi-request';
import _ from 'lodash';
import { accountDeviceHeartbeatGet } from '@/services/api-Account/LoginDeviceController';
import {
  autoRefreshToken,
  doRefreshToken,
  errorMessageI18n,
  getApiUrl,
  getJwt,
  getTeamIdFromUrl,
  setJwt,
  SkipErrorNotifyOption,
} from '@/utils/utils';
import ContentLayout from '@/components/ContentLayout';
import useCurrentUser, {
  clearCurrentUser,
  fetchCurrentUser,
  getCurrentUser,
} from '@/hooks/useCurrentUser';
import axios from 'axios';
import {
  MEMBER_ERROR_CODE_COLLECTION,
  TEAM_BLOCKED,
  TEAM_MEMBER_BLOCKED,
} from '@/constants/ErrorCode';
import DMConfirm from '@/components/Common/DMConfirm';
import { dispatchClose } from '@/components/Common/Modal/DMModal';
import GlobalHeaderRight from '@/components/Layout';
import useCurrentTeam, { fetchCurrentTeam, getCurrentTeamId } from '@/hooks/useCurrentTeam';
import { fetchTeams } from '@/hooks/useTeams';
import type { ReactNode } from 'react';
import React from 'react';
import { useEffect, useState } from 'react';
import { useCallback, useMemo } from 'react';
import routes from '../config/routes';
import {
  noNeedLogin,
  redirectToCurrentTeamPath,
  redirectToHome,
  redirectToLogin,
} from '@/utils/pageUtils';
import MiddleSpin from '@/components/Common/MiddleSpin';
import ColoursIcon from '@/components/Common/ColoursIcon';
import IconFontIcon from '@/components/Common/IconFontIcon';
import styles from './app.less';
import { onServiceAvailable, onServiceUnavailable } from '@/hooks/useSysBroadcast';
import classNames from 'classnames';
import { useAuthJudgeCallback } from '@/components/Common/FunctionButton';
import { useRequest } from '@@/plugin-request/request';
import { ghJobsListJobsPendingGet } from '@/services/api-TKGHAPI/GhJobController';
import { offTaskCountRefresh, onTaskCountRefresh } from '@/pages/Task';
import useTkTeamStatusRequest, {
  offTeamStatusUpdate,
  onTeamStatusUpdate,
} from '@/hooks/useTkShopTeam';
import HeaderNavs from '@/components/Layout/HeaderNavs';

let deviceHeartbeatTimer: any = 0;
// 设备心跳
const deviceHeartbeat = () => {
  clearInterval(deviceHeartbeatTimer);
  deviceHeartbeatTimer = setInterval(() => {
    accountDeviceHeartbeatGet(SkipErrorNotifyOption);
  }, 60 * 1000);
  accountDeviceHeartbeatGet(SkipErrorNotifyOption);
};
/** 获取用户信息比较慢的时候会展示一个 loading */
export const initialStateConfig = {
  loading: <MiddleSpin />,
};

/**
 * @see  https://umijs.org/zh-CN/plugins/plugin-initial-state
 * */
export async function getInitialState(): Promise<{
  loading?: boolean;
}> {
  const user = await fetchCurrentUser();
  await doRefreshToken();
  let teamId: string | number | undefined;
  try {
    teamId = getTeamIdFromUrl();
  } catch (e) {
    console.log(e);
  }

  if (user) {
    const teams = await fetchTeams();

    if (teamId) {
      if (_.find(teams, ({ id }) => id === teamId)) {
        await fetchCurrentTeam(teamId);
      } else {
        // 不属于这个团队
        redirectToHome();
      }
    }
  }
  return {};
}

// 自动将storage中的jwt带到header中
const authHeaderInterceptor = (url: string, options: RequestOptionsInit) => {
  const jwt: string = getJwt();
  const authHeader = { Authorization: jwt };
  return {
    url,
    options: { ...options, interceptors: true, headers: authHeader, credentials: 'include' },
  };
};

// 自动将当前url中的teamId带到header中
const teamIdHeaderInterceptor = (url: string, options: RequestOptionsInit) => {
  const teamId = getCurrentTeamId();
  let headers = {};
  if (options.headers) {
    headers = { ...options.headers };
  }
  if (teamId && getCurrentUser()) {
    headers['x-dm-team-id'] = teamId;
  }
  if (options?.ignoreTeamId) {
    delete headers['x-dm-team-id'];
  }
  return {
    url,
    options: { ...options, interceptors: true, headers },
  };
};

// 处理网络异常
const responseStatusInterceptor = (response: Response) => {
  const key = 'global_network_error';
  if (!response.status || response.status >= 500) {
    message.error({
      content: I18N.t('您的网络发生异常，无法连接服务器'),
      duration: 0,
      key,
    });
    onServiceUnavailable();
  } else {
    if (!response.url.includes('ajax-event/query')) {
      onServiceAvailable();
    }
    message.destroy(key);
  }
  return response;
};
let modalConfirm;
const MEMBER_STATUS_KEY = 'invalid-member-status-error';

// 处理用户状态异常
const onMemberStatusNotValid = async (response: Response) => {
  if (response.status === 200) {
    const data = await response.clone().json();
    if (
      MEMBER_ERROR_CODE_COLLECTION.includes(data.code) &&
      !response.url.includes('ajax-event/query')
    ) {
      const { code } = data;
      const title =
        code === TEAM_MEMBER_BLOCKED
          ? I18N.t('您在当前团队中被禁用')
          : I18N.t('您已从当前团队中被踢出');
      // 不一致弹就要加duration
      message.error({
        content: title,
        duration: 0,
        key: MEMBER_STATUS_KEY,
      });
      if (!modalConfirm) {
        modalConfirm = DMConfirm({
          type: 'info',
          title,
          content: I18N.t('请联络当前团队管理员，以获取进一步的信息'),
          onOk() {
            modalConfirm.destroy();
            redirectToHome();
          },
        });
      }
    } else {
      message.destroy(MEMBER_STATUS_KEY);
    }
  } else {
    message.destroy(MEMBER_STATUS_KEY);
  }
  return response;
};

// 处理团队状态异常
const goHome = _.throttle(redirectToHome, 3000);
const onTeamBlocked = async (response: Response) => {
  if (response.status === 200) {
    const data = await response.clone().json();
    if (data.code === TEAM_BLOCKED && location.pathname !== `/team/${getTeamIdFromUrl()}/`) {
      goHome();
    }
  }
  return response;
};
// 处理数据里面返回的jwt
const onJwtRes = async (response: Response) => {
  if (response.status === 200) {
    const data = await response.clone().json();
    if (data?.data?.jwt && data?.data?.jwtExpireTime) {
      if (new Date(data?.data?.jwtExpireTime).getTime() - Date.now()) {
        setJwt(data?.data?.jwt);
      }
    }
  }
  return response;
};
const commonResponseInterceptor = async (response: Response) => {
  // 防止SkipErrorNotifyOption未走到ErrorHandle
  if (response?.status === 401) {
    clearCurrentUser();
    if (!noNeedLogin()) {
      redirectToLogin();
    }
  } else if (response?.status === 200) {
    const data = await response.clone().json();
    // 过滤一些路径方法
    const is_excluded = _.some(['/api/resource/checkAuthorized'], (p) => {
      return response.url.includes(p);
    });

    if (data?.code === 401 && !is_excluded) {
      clearCurrentUser();
      if (!noNeedLogin()) {
        redirectToLogin();
      }
      return;
    }
  }
  return response;
};
/**
 * 异常处理程序
 const codeMessage = {
    200: '服务器成功返回请求的数据。',
    201: '新建或修改数据成功。',
    202: '一个请求已经进入后台排队（异步任务）。',
    204: '删除数据成功。',
    400: '发出的请求有错误，服务器没有进行新建或修改数据的操作。',
    401: '用户没有权限（令牌、用户名、密码错误）。',
    403: '用户得到授权，但是访问是被禁止的。',
    404: '发出的请求针对的是不存在的记录，服务器没有进行操作。',
    405: '请求方法不被允许。',
    406: '请求的格式不可得。',
    410: '请求的资源被永久删除，且不会再得到的。',
    422: '当创建一个对象时，发生一个验证错误。',
    500: '服务器发生错误，请检查服务器。',
    502: '网关错误。',
    503: '服务不可用，服务器暂时过载或维护。',
    504: '网关超时。',
 };
 * @see https://beta-pro.ant.design/docs/request-cn
 */
const acceptLanguage = (url: string, options: RequestOptionsInit) => {
  let headers = {};
  if (options.headers) {
    headers = { ...options.headers };
  }
  headers['Accept-Language'] = I18N.getLocale();
  return {
    url,
    options: { ...options, interceptors: true, headers },
  };
};
export const request: RequestConfig = {
  prefix: getApiUrl().substr(0, getApiUrl().length - 1),
  errorConfig: {
    adaptor: (resData) => {
      return {
        ...resData,
        errorMessage: resData.message,
        errorCode: resData.code,
      };
    },
  },
  errorHandler: (error: ResponseError) => {
    const { response } = error;
    if (response && response.status) {
      const { status, url, statusText } = response;
      if (status < 500) {
        const requestErrorMessage = I18N.t('请求失败');
        const errorMessage = `${requestErrorMessage} ${status}: ${url.split('?')[0]}`;
        notification.error({
          message: errorMessage,
          description: statusText,
        });
      }
    } else if (error.name === 'BizError') {
      if (!MEMBER_ERROR_CODE_COLLECTION.includes(error?.data?.code)) {
        message.error(errorMessageI18n(error.message));
      }
    }

    throw error;
  },
  // 新增自动添加AccessToken的请求前拦截器
  requestInterceptors: [authHeaderInterceptor, teamIdHeaderInterceptor, acceptLanguage],
  responseInterceptors: [
    commonResponseInterceptor,
    responseStatusInterceptor,
    onTeamBlocked,
    onMemberStatusNotValid,
    onJwtRes,
  ],
};

/**
 * 轨迹记录防抖
 */
const tgAccessFn = _.debounce((loc) => {
  try {
    let prefix = getApiUrl();
    const isProduct = window.location.hostname.includes('szdamai.com');
    if (isProduct) {
      // 官网部署
      prefix = `https://api.szdamai.com/`;
    }
    axios.post(
      `${prefix}api/webhook/tg/access`,
      {},
      {
        params: {
          accessUrl: window.location.href,
          refUrl: document.referrer,
          module: loc?.pathname,
          _wht: 'TG_3Ugqcz1es56zPSmYS7ECf',
        },
        withCredentials: true,
      },
    );
  } catch (e) {
    console.log(e, 'onPageChange');
  }
}, 1000);

export const layout: RunTimeLayoutConfig = () => {
  autoRefreshToken();
  deviceHeartbeat();

  return {
    menu: {
      // 关闭菜单国际化
    },
    title: I18N.t('花漾TK'),
    headerHeight: 50,
    headerContentRender() {
      return (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: 8,
            height: '100%',
            padding: '0 16px',
          }}
        >
          <HeaderNavs />
        </div>
      );
    },
    className: styles.contentWrap,
    rightContentRender: () => <GlobalHeaderRight />,
    footerRender: false,
    onPageChange: (loc: any) => {
      tgAccessFn(loc);
      if (!getCurrentUser() && !getJwt() && !noNeedLogin()) {
        redirectToLogin();
      }
      dispatchClose();
    },
    menuRender(_config, dom) {
      const { props } = dom;
      return <LeftMenu {..._.omit(props, 'children')} />;
    },

    childrenRender: (dom: ReactNode) => {
      return <ContentLayout>{dom}</ContentLayout>;
    },
  };
};
