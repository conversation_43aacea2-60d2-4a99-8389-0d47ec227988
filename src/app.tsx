import I18N from '@/i18n';
import { Badge, Layout, Menu, message, notification, Tooltip, Typography } from 'antd';
import type { RequestConfig, RunTimeLayoutConfig } from 'umi';
import { useLocation, useRouteMatch } from 'umi';
import type { RequestOptionsInit, ResponseError } from 'umi-request';
import _ from 'lodash';
import { accountDeviceHeartbeatGet } from '@/services/api-Account/LoginDeviceController';
import {
  autoRefreshToken,
  doRefreshToken,
  errorMessageI18n,
  getApiUrl,
  getJwt,
  getTeamIdFromUrl,
  setJwt,
  SkipErrorNotifyOption,
} from '@/utils/utils';
import ContentLayout from '@/components/ContentLayout';
import useCurrentUser, {
  clearCurrentUser,
  fetchCurrentUser,
  getCurrentUser,
} from '@/hooks/useCurrentUser';
import axios from 'axios';
import {
  MEMBER_ERROR_CODE_COLLECTION,
  TEAM_BLOCKED,
  TEAM_MEMBER_BLOCKED,
} from '@/constants/ErrorCode';
import DMConfirm from '@/components/Common/DMConfirm';
import { dispatchClose } from '@/components/Common/Modal/DMModal';
import GlobalHeaderRight from '@/components/Layout';
import useCurrentTeam, { fetchCurrentTeam, getCurrentTeamId } from '@/hooks/useCurrentTeam';
import { fetchTeams } from '@/hooks/useTeams';
import type { ReactNode } from 'react';
import React from 'react';
import { useEffect, useState } from 'react';
import { useCallback, useMemo } from 'react';
import routes from '../config/routes';
import {
  noNeedLogin,
  redirectToCurrentTeamPath,
  redirectToHome,
  redirectToLogin,
} from '@/utils/pageUtils';
import MiddleSpin from '@/components/Common/MiddleSpin';
import ColoursIcon from '@/components/Common/ColoursIcon';
import IconFontIcon from '@/components/Common/IconFontIcon';
import styles from './app.less';
import { onServiceAvailable, onServiceUnavailable } from '@/hooks/useSysBroadcast';
import classNames from 'classnames';
import { useAuthJudgeCallback } from '@/components/Common/FunctionButton';
import { useRequest } from '@@/plugin-request/request';
import { ghJobsListJobsPendingGet } from '@/services/api-TKGHAPI/GhJobController';
import { offTaskCountRefresh, onTaskCountRefresh } from '@/pages/Task';
import useTkTeamStatusRequest, {
  offTeamStatusUpdate,
  onTeamStatusUpdate,
} from '@/hooks/useTkShopTeam';
import HeaderNavs from '@/components/Layout/HeaderNavs';

let deviceHeartbeatTimer: any = 0;
// 设备心跳
const deviceHeartbeat = () => {
  clearInterval(deviceHeartbeatTimer);
  deviceHeartbeatTimer = setInterval(() => {
    accountDeviceHeartbeatGet(SkipErrorNotifyOption);
  }, 60 * 1000);
  accountDeviceHeartbeatGet(SkipErrorNotifyOption);
};
/** 获取用户信息比较慢的时候会展示一个 loading */
export const initialStateConfig = {
  loading: <MiddleSpin />,
};

/**
 * @see  https://umijs.org/zh-CN/plugins/plugin-initial-state
 * */
export async function getInitialState(): Promise<{
  loading?: boolean;
}> {
  const user = await fetchCurrentUser();
  await doRefreshToken();
  let teamId: string | number | undefined;
  try {
    teamId = getTeamIdFromUrl();
  } catch (e) {
    console.log(e);
  }

  if (user) {
    const teams = await fetchTeams();

    if (teamId) {
      if (_.find(teams, ({ id }) => id === teamId)) {
        await fetchCurrentTeam(teamId);
      } else {
        // 不属于这个团队
        redirectToHome();
      }
    }
  }
  return {};
}

// 自动将storage中的jwt带到header中
const authHeaderInterceptor = (url: string, options: RequestOptionsInit) => {
  const jwt: string = getJwt();
  const authHeader = { Authorization: jwt };
  return {
    url,
    options: { ...options, interceptors: true, headers: authHeader, credentials: 'include' },
  };
};

// 自动将当前url中的teamId带到header中
const teamIdHeaderInterceptor = (url: string, options: RequestOptionsInit) => {
  const teamId = getCurrentTeamId();
  let headers = {};
  if (options.headers) {
    headers = { ...options.headers };
  }
  if (teamId && getCurrentUser()) {
    headers['x-dm-team-id'] = teamId;
  }
  if (options?.ignoreTeamId) {
    delete headers['x-dm-team-id'];
  }
  return {
    url,
    options: { ...options, interceptors: true, headers },
  };
};

// 处理网络异常
const responseStatusInterceptor = (response: Response) => {
  const key = 'global_network_error';
  if (!response.status || response.status >= 500) {
    message.error({
      content: I18N.t('您的网络发生异常，无法连接服务器'),
      duration: 0,
      key,
    });
    onServiceUnavailable();
  } else {
    if (!response.url.includes('ajax-event/query')) {
      onServiceAvailable();
    }
    message.destroy(key);
  }
  return response;
};
let modalConfirm;
const MEMBER_STATUS_KEY = 'invalid-member-status-error';

// 处理用户状态异常
const onMemberStatusNotValid = async (response: Response) => {
  if (response.status === 200) {
    const data = await response.clone().json();
    if (
      MEMBER_ERROR_CODE_COLLECTION.includes(data.code) &&
      !response.url.includes('ajax-event/query')
    ) {
      const { code } = data;
      const title =
        code === TEAM_MEMBER_BLOCKED
          ? I18N.t('您在当前团队中被禁用')
          : I18N.t('您已从当前团队中被踢出');
      // 不一致弹就要加duration
      message.error({
        content: title,
        duration: 0,
        key: MEMBER_STATUS_KEY,
      });
      if (!modalConfirm) {
        modalConfirm = DMConfirm({
          type: 'info',
          title,
          content: I18N.t('请联络当前团队管理员，以获取进一步的信息'),
          onOk() {
            modalConfirm.destroy();
            redirectToHome();
          },
        });
      }
    } else {
      message.destroy(MEMBER_STATUS_KEY);
    }
  } else {
    message.destroy(MEMBER_STATUS_KEY);
  }
  return response;
};

// 处理团队状态异常
const goHome = _.throttle(redirectToHome, 3000);
const onTeamBlocked = async (response: Response) => {
  if (response.status === 200) {
    const data = await response.clone().json();
    if (data.code === TEAM_BLOCKED && location.pathname !== `/team/${getTeamIdFromUrl()}/`) {
      goHome();
    }
  }
  return response;
};
// 处理数据里面返回的jwt
const onJwtRes = async (response: Response) => {
  if (response.status === 200) {
    const data = await response.clone().json();
    if (data?.data?.jwt && data?.data?.jwtExpireTime) {
      if (new Date(data?.data?.jwtExpireTime).getTime() - Date.now()) {
        setJwt(data?.data?.jwt);
      }
    }
  }
  return response;
};
const commonResponseInterceptor = async (response: Response) => {
  // 防止SkipErrorNotifyOption未走到ErrorHandle
  if (response?.status === 401) {
    clearCurrentUser();
    if (!noNeedLogin()) {
      redirectToLogin();
    }
  } else if (response?.status === 200) {
    const data = await response.clone().json();
    // 过滤一些路径方法
    const is_excluded = _.some(['/api/resource/checkAuthorized'], (p) => {
      return response.url.includes(p);
    });

    if (data?.code === 401 && !is_excluded) {
      clearCurrentUser();
      if (!noNeedLogin()) {
        redirectToLogin();
      }
      return;
    }
  }
  return response;
};
/**
 * 异常处理程序
 const codeMessage = {
    200: '服务器成功返回请求的数据。',
    201: '新建或修改数据成功。',
    202: '一个请求已经进入后台排队（异步任务）。',
    204: '删除数据成功。',
    400: '发出的请求有错误，服务器没有进行新建或修改数据的操作。',
    401: '用户没有权限（令牌、用户名、密码错误）。',
    403: '用户得到授权，但是访问是被禁止的。',
    404: '发出的请求针对的是不存在的记录，服务器没有进行操作。',
    405: '请求方法不被允许。',
    406: '请求的格式不可得。',
    410: '请求的资源被永久删除，且不会再得到的。',
    422: '当创建一个对象时，发生一个验证错误。',
    500: '服务器发生错误，请检查服务器。',
    502: '网关错误。',
    503: '服务不可用，服务器暂时过载或维护。',
    504: '网关超时。',
 };
 * @see https://beta-pro.ant.design/docs/request-cn
 */
const acceptLanguage = (url: string, options: RequestOptionsInit) => {
  let headers = {};
  if (options.headers) {
    headers = { ...options.headers };
  }
  headers['Accept-Language'] = I18N.getLocale();
  return {
    url,
    options: { ...options, interceptors: true, headers },
  };
};
export const request: RequestConfig = {
  prefix: getApiUrl().substr(0, getApiUrl().length - 1),
  errorConfig: {
    adaptor: (resData) => {
      return {
        ...resData,
        errorMessage: resData.message,
        errorCode: resData.code,
      };
    },
  },
  errorHandler: (error: ResponseError) => {
    const { response } = error;
    if (response && response.status) {
      const { status, url, statusText } = response;
      if (status < 500) {
        const requestErrorMessage = I18N.t('请求失败');
        const errorMessage = `${requestErrorMessage} ${status}: ${url.split('?')[0]}`;
        notification.error({
          message: errorMessage,
          description: statusText,
        });
      }
    } else if (error.name === 'BizError') {
      if (!MEMBER_ERROR_CODE_COLLECTION.includes(error?.data?.code)) {
        message.error(errorMessageI18n(error.message));
      }
    }

    throw error;
  },
  // 新增自动添加AccessToken的请求前拦截器
  requestInterceptors: [authHeaderInterceptor, teamIdHeaderInterceptor, acceptLanguage],
  responseInterceptors: [
    commonResponseInterceptor,
    responseStatusInterceptor,
    onTeamBlocked,
    onMemberStatusNotValid,
    onJwtRes,
  ],
};

/**
 * 轨迹记录防抖
 */
const tgAccessFn = _.debounce((loc) => {
  try {
    let prefix = getApiUrl();
    const isProduct = window.location.hostname.includes('szdamai.com');
    if (isProduct) {
      // 官网部署
      prefix = `https://api.szdamai.com/`;
    }
    axios.post(
      `${prefix}api/webhook/tg/access`,
      {},
      {
        params: {
          accessUrl: window.location.href,
          refUrl: document.referrer,
          module: loc?.pathname,
          _wht: 'TG_3Ugqcz1es56zPSmYS7ECf',
        },
        withCredentials: true,
      },
    );
  } catch (e) {
    console.log(e, 'onPageChange');
  }
}, 1000);

const LeftMenu = (props: { matchMenuKeys?: never[] | undefined }) => {
  const { pathname } = useLocation();
  const { matchMenuKeys } = props;
  const { params } = useRouteMatch(['/team/:teamId/sample/:shopId']) || {};
  const [openKeys, setOpenKeys] = useState<string[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [collapsed, setCollapsed] = useState(false);
  const hasAuthFn = useAuthJudgeCallback();
  const { data, run: fetchStatus, reset } = useTkTeamStatusRequest(true);
  const team = useCurrentTeam();
  const { data: taskCount, run: fetchTaskCount } = useRequest(
    async () => {
      if (getTeamIdFromUrl()) {
        return ghJobsListJobsPendingGet(
          {
            ghPlatformType: 'tkshop',
            userEvent: false,
            pageNum: 1,
            pageSize: 1,
          },
          SkipErrorNotifyOption,
        );
      }
      return {
        data: undefined,
      };
    },
    {
      manual: true,
      formatResult(res) {
        return res.data?.total || 0;
      },
      pollingInterval: 1000 * 60,
    },
  );
  const menus = useMemo(() => {
    if (routes?.length) {
      const target = _.find(routes, ({ key }) => key === 'team');
      if (target?.routes) {
        const list = _.cloneDeep(target?.routes);

        list.forEach((item, index) => {
          if (item.path === '/team/:teamId/task') {
            list[index].locale = (
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  overflow: 'hidden',
                  flexWrap: 'nowrap',
                  position: 'relative',
                  gap: 8,
                }}
              >
                <span
                  style={{
                    flex: 1,
                    overflow: 'hidden',
                    whiteSpace: 'nowrap',
                    textOverflow: 'ellipsis',
                  }}
                  title={list[index].locale}
                >
                  {list[index].locale}
                </span>
                <Badge size={'small'} overflowCount={99} count={taskCount || 0} showZero={false} />
              </div>
            );
          }
        });

        return list;
      }
    }
    return [];
  }, [taskCount]);

  const user = useCurrentUser();

  const genItem = useCallback(
    (list: any[]) => {
      const _list: any[] = [];
      if (list?.length) {
        list.forEach((menu) => {
          const { locale, path, hideInMenu, meta, routes: children, className } = menu;
          if (!hideInMenu && locale) {
            const { icon, iconType, access, teamId } = meta || {};
            let iconNode;
            if (icon) {
              if (iconType === 'link') {
                iconNode = (
                  <Typography.Link>
                    <IconFontIcon iconName={icon} size={18} />
                  </Typography.Link>
                );
              } else {
                iconNode = (
                  <span>
                    <ColoursIcon className={icon} size={18} />
                  </span>
                );
              }
            }
            const key = path.replace(':teamId', data?.teamId);
            const childActive = pathname.includes(key) && pathname !== key;

            const item = {
              icon: iconNode,
              label: React.isValidElement(locale) ? locale : I18N.t(locale),
              key,
              children: genItem(children),
              className: classNames(
                {
                  'child-active': childActive,
                },
                className,
              ),
            };
            if (collapsed || !item?.children?.length) {
              delete item.children;
            }
            if (access) {
              if (hasAuthFn(access)) {
                _list.push(item);
              }
            } else if (teamId) {
              try {
                if (getTeamIdFromUrl() === teamId) {
                  _list.push(item);
                }
              } catch (e) {
                console.log(e);
              }
            } else {
              _list.push(item);
            }
          }
        });
      }

      return _list?.length ? _list : [];
    },
    [collapsed, data?.teamId, hasAuthFn, pathname],
  );
  const items = useMemo(() => {
    if (menus?.length) {
      return genItem(menus);
    }
    return [];
  }, [genItem, menus]);
  const collapsible_items = useMemo(() => {
    return _.filter(items, (i) => {
      return i?.children?.length > 0;
    });
  }, [items]);
  const allOpened = useMemo(() => {
    return !_.some(collapsible_items, ({ key }) => {
      return !_.some(openKeys, (j) => {
        return key === j;
      });
    });
  }, [collapsible_items, openKeys]);
  useEffect(() => {
    if (user && data && team) {
      fetchTaskCount();
    }
  }, [data, user, team, fetchTaskCount]);
  useEffect(() => {
    onTaskCountRefresh(fetchTaskCount);
    return () => {
      offTaskCountRefresh(fetchTaskCount);
    };
  }, [fetchTaskCount]);
  useEffect(() => {
    if (!pathname.includes('/team/')) {
      reset();
    }
  }, [pathname, reset]);
  useEffect(() => {
    if (data?.teamId && matchMenuKeys.length > 1) {
      const _keys = matchMenuKeys.map((item: string) => {
        return item
          .replace(':teamId', String(data.teamId!))
          .replace(':shopId', params?.shopId || '');
      });
      setSelectedKeys(_keys);
    }
  }, [data?.teamId, matchMenuKeys, params?.shopId]);
  useEffect(() => {
    if (selectedKeys.length) {
      setOpenKeys(selectedKeys);
    }
  }, [selectedKeys]);
  useEffect(() => {
    onTeamStatusUpdate(fetchStatus);
    return () => {
      offTeamStatusUpdate(fetchStatus);
    };
  }, [fetchStatus]);

  if (!user || !data || !team) {
    return null;
  }
  if (data?.status !== 'Ready' && data?.status !== 'Expiring') {
    return null;
  }
  return (
    <Layout.Sider
      className={styles.rootLayoutSider}
      theme={'light'}
      collapsible
      collapsed={collapsed}
      collapsedWidth={50}
      onCollapse={(val) => {
        setCollapsed(val);
      }}
      trigger={
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            padding: '0 16px',
            cursor: 'unset',
            justifyContent: 'space-between',
          }}
          onClick={(e) => {
            e.stopPropagation();
            return false;
          }}
        >
          <Tooltip placement={'right'} title={collapsed ? I18N.t('展开') : I18N.t('收起')}>
            <Typography.Link
              style={{ padding: 2, cursor: 'pointer' }}
              onClick={() => {
                setCollapsed(!collapsed);
              }}
            >
              <IconFontIcon iconName={collapsed ? 'zhankai' : 'shouqi'} />
            </Typography.Link>
          </Tooltip>
          <Tooltip title={allOpened ? I18N.t('全部收起') : I18N.t('全部展开')}>
            {!collapsed && collapsible_items.length > 0 && (
              <Typography.Link
                style={{ padding: 2, cursor: 'pointer' }}
                onClick={() => {
                  setOpenKeys(() => {
                    if (allOpened) {
                      return [];
                    }
                    return collapsible_items.map((item) => item.key as string);
                  });
                }}
              >
                <IconFontIcon
                  size={12}
                  iconName={!allOpened ? 'angle-right_24' : 'angle-down_24'}
                />
              </Typography.Link>
            )}
          </Tooltip>
        </div>
      }
    >
      <Menu
        selectedKeys={selectedKeys}
        onOpenChange={setOpenKeys}
        openKeys={openKeys}
        multiple={false}
        inlineIndent={18}
        expandIcon={(_props) => {
          const { isSubMenu, isOpen } = _props;
          if (isSubMenu) {
            if (isOpen) {
              return (
                <Typography.Link className={'expand-icon'}>
                  <IconFontIcon size={12} iconName={'angle-down_24'} />
                </Typography.Link>
              );
            }
            return (
              <Typography.Link className={'expand-icon'}>
                <IconFontIcon size={12} iconName={'angle-right_24'} />
              </Typography.Link>
            );
          }
          return null;
        }}
        items={items}
        style={{ height: '100%', border: 'none', overflowY: 'auto', overflowX: 'hidden' }}
        mode={'inline'}
        onClick={({ key }) => {
          redirectToCurrentTeamPath(key);
        }}
      />
    </Layout.Sider>
  );
};
export const layout: RunTimeLayoutConfig = () => {
  autoRefreshToken();
  deviceHeartbeat();

  return {
    menu: {
      // 关闭菜单国际化
    },
    title: I18N.t('花漾TK'),
    headerHeight: 50,
    headerContentRender() {
      return (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: 8,
            height: '100%',
            padding: '0 16px',
          }}
        >
          <HeaderNavs />
        </div>
      );
    },
    className: styles.contentWrap,
    rightContentRender: () => <GlobalHeaderRight />,
    footerRender: false,
    onPageChange: (loc: any) => {
      tgAccessFn(loc);
      if (!getCurrentUser() && !getJwt() && !noNeedLogin()) {
        redirectToLogin();
      }
      dispatchClose();
    },
    menuRender(_config, dom) {
      const { props } = dom;
      return <LeftMenu {..._.omit(props, 'children')} />;
    },

    childrenRender: (dom: ReactNode) => {
      return <ContentLayout>{dom}</ContentLayout>;
    },
  };
};
