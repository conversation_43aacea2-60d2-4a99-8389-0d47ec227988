import I18N from '@/i18n';
import { useCallback, useMemo, useState } from 'react';
import DMModal from '@/components/Common/Modal/DMModal';
import { Alert, Form, message, Radio, Typography } from 'antd';
import { GhostModalCaller } from '@/mixins/modal';
import DMConfirm from '@/components/Common/DMConfirm';
import ExportCreatorsModal from '@/pages/components/ExportTiktokCreatorsModal';
import CollectOperation from '@/components/Common/CollectOperation';
import {
  ghFavoriteByQueryPost,
  ghListTagByQueryPost,
  ghTagByQueryPost,
} from '@/services/api-TKGHAPI/GhUserController';
import useTeamFavor from '@/hooks/useTeamFavor';
import {
  tkshopCreatorDeleteByQueryPost,
  tkshopCreatorGroupByRegionPost,
} from '@/services/api-TKShopAPI/TkshopCreatorController';
import { useRequest } from '@@/plugin-request/request';
import { useLocationGroupConfirmModal } from '@/pages/TikTok/Live/components/LocationGroupConfirmModal';
import { useSyncCreatorsModal } from '@/pages/TikTok/components/SyncCreatorSelectedModal';
import InviteSelectedModal from '@/pages/TikTok/components/InviteSelectedModal';
import SendMsgToCreatorSelectedModal from '@/pages/TikTok/components/SendMsgToCreatorSelectedModal';
import { showFunctionCodeAlert, useAuthJudgeCallback } from '@/components/Common/FunctionButton';
import Functions from '@/constants/Functions';
import TagOperationSelected from '@/components/Common/TagOperationSelected';

export const BATCH_LIMIT = 2000;

const CreatorOptionByQuery = (props: {
  total: number;
  getParams: () => any;
  onUpdate: (reset?: boolean) => void;
}) => {
  const { total, getParams, onUpdate } = props;
  const [visible, setVisible] = useState(true);
  const [form] = Form.useForm();
  const scene = 'ShopCreator';
  const groupRegionCallback = useLocationGroupConfirmModal();
  const syncCreators = useSyncCreatorsModal();
  const [type, setType] = useState<
    'invite' | 'tag' | 'favor' | 'export' | 'clear' | 'message' | 'sync'
  >(() => {
    return 'invite';
  });

  const groupRegion = useCallback(() => {
    return tkshopCreatorGroupByRegionPost({
      ...getParams(),
    }).then((res) => {
      const _map: any = {};
      (res?.data || []).forEach(({ region, creators }) => {
        if (creators?.length && region) {
          _map[region] = creators;
        }
      });
      return _map;
    });
  }, [getParams]);
  const label = useMemo(() => {
    return I18N.t('达人');
  }, []);
  const { refreshFavor } = useTeamFavor('TkshopCreator');
  const hasAuth = useAuthJudgeCallback();

  const tip = useMemo(() => {
    let suffix = (
      <span>
        {I18N.t('您希望对这些{{label}}：', {
          label,
        })}
      </span>
    );
    if (total > BATCH_LIMIT) {
      suffix = (
        <span>
          {I18N.t('您希望对前 {{total}} 个{{label}}：', {
            total: (
              <Typography.Link style={{ margin: '0 4px' }}>
                {BATCH_LIMIT.toLocaleString()}
              </Typography.Link>
            ),
            label,
          })}
        </span>
      );
    }
    return (
      <div>
        {I18N.t('当前条件下，共检索出 {{total}} 个{{label}}，', {
          total: total.toLocaleString(),
          label,
        })}
        {suffix}
      </div>
    );
  }, [label, total]);
  const { run: submit, loading } = useRequest(
    async () => {
      switch (type) {
        case 'invite':
        case 'sync':
        case 'message':
          await groupRegion().then((group) => {
            if (type === 'sync') {
              setVisible(false);
              syncCreators({
                group,
              });
            } else {
              groupRegionCallback({
                group: group,
                onSubmit: async ({ region, creators }) => {
                  setVisible(false);
                  if (type === 'message') {
                    GhostModalCaller(
                      <SendMsgToCreatorSelectedModal
                        region={region}
                        refer={global ? 'global' : 'list'}
                        selected={creators}
                      />,
                    );
                  } else if (type === 'invite') {
                    GhostModalCaller(
                      <InviteSelectedModal
                        refer={global ? 'global' : 'list'}
                        selected={creators}
                        region={region}
                      />,
                    );
                  }
                },
              });
            }
          });

          break;
        case 'export':
          const fn = () => {
            GhostModalCaller(<ExportCreatorsModal getParams={getParams} />);
          };
          if (total > BATCH_LIMIT) {
            DMConfirm({
              width: 460,
              title: I18N.t('一次最多导出 {{total}} 个{{label}}', {
                total: BATCH_LIMIT.toLocaleString(),
                label,
              }),
              content: I18N.t('请确认是否继续导出前 {{total}} 个{{label}}', {
                total: BATCH_LIMIT.toLocaleString(),
                label,
              }),
              onOk() {
                fn();
              },
            });
          } else {
            fn();
          }
          break;
        case 'clear':
          if (!hasAuth(Functions.TKSHOP_CREATOR_ALLOCATE)) {
            showFunctionCodeAlert();
            return;
          }
          DMConfirm({
            title: `${I18N.t('确定屏蔽搜索出的 {{count}} 个{{label}}吗？', {
              count: Math.min(BATCH_LIMIT, total)?.toLocaleString(),
              label,
            })}`,
            onOk() {
              tkshopCreatorDeleteByQueryPost(
                {
                  force: false,
                },
                getParams(),
              ).then(() => {
                message.success(I18N.t('屏蔽成功'));
                onUpdate(true);
              });
            },
          });
          break;
        case 'tag': {
          setVisible(false);
          GhostModalCaller(
            <TagOperationSelected
              fetchClearTags={() => {
                return ghListTagByQueryPost({
                  scene: 'ShopCreator',
                  query: getParams(),
                });
              }}
              resourceType={'TkshopCreator'}
              onSubmit={async (addTagsId, clearTagsId) => {
                if (addTagsId.length) {
                  await ghTagByQueryPost({
                    query: getParams(),
                    tagIds: addTagsId,
                    scene,
                    tag: true,
                  });
                }
                if (clearTagsId.length) {
                  await ghTagByQueryPost({
                    query: getParams(),
                    tagIds: clearTagsId,
                    scene,
                    tag: false,
                  });
                }
                onUpdate(false);
              }}
            />,
          );
          break;
        }
        case 'favor': {
          GhostModalCaller(
            <CollectOperation
              showLoading
              onLike={async () => {
                setVisible(false);
                await ghFavoriteByQueryPost({
                  favorite: true,
                  query: getParams(),
                  scene,
                });
                refreshFavor();
              }}
              resourceType={'TkshopCreator'}
              onDislike={async () => {
                setVisible(false);
                await ghFavoriteByQueryPost({
                  favorite: false,
                  query: getParams(),
                  scene,
                });
                refreshFavor();
              }}
            />,
          );
          break;
        }
        default: {
          throw new Error('未定义的操作类型');
        }
      }
    },
    {
      manual: true,
    },
  );

  const actions = useMemo(() => {
    return (
      <Radio.Group
        value={type}
        onChange={(e) => {
          setType(e.target.value);
        }}
      >
        <div style={{ display: 'flex', flexDirection: 'column', gap: 12 }}>
          <Radio value={'invite'}>
            <div>{I18N.t('定向邀约')}</div>
          </Radio>
          <Radio value={'message'}>
            <div>{I18N.t('发送消息')}</div>
          </Radio>
          <Radio value={'sync'}>
            <div>{I18N.t('信息更新')}</div>
          </Radio>
          <Radio value={'tag'}>
            <div>{I18N.t('增加/清空标签')}</div>
          </Radio>
          <Radio value={'favor'}>
            <div>{I18N.t('关注/取消关注')}</div>
          </Radio>
          <Radio value={'export'}>
            <div>{I18N.t('批量导出')}</div>
          </Radio>
          <Radio value={'clear'}>
            <div>{I18N.t('批量屏蔽')}</div>
          </Radio>
        </div>
      </Radio.Group>
    );
  }, [type]);

  return (
    <DMModal
      open={visible}
      width={640}
      title={I18N.t('全量操作')}
      onOk={submit}
      confirmLoading={loading}
      onCancel={() => {
        setVisible(false);
      }}
    >
      <Alert
        showIcon
        message={I18N.t('全量操作一次最多操作 {{limit}} 个{{label}}', {
          limit: BATCH_LIMIT,
          label,
        })}
      />
      <div style={{ margin: '16px 0' }}>
        <Typography.Text>{tip}</Typography.Text>
      </div>
      <Form form={form}>{actions}</Form>
    </DMModal>
  );
};
export default CreatorOptionByQuery;
