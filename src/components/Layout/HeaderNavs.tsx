import { useMemo } from 'react';
import { useLocation, history } from 'umi';
import styled from 'styled-components';
import { Badge } from 'antd';

// 菜单配置
const MENU_ITEMS = [
  { key: 'shop', label: '首页', path: 'shop' },
  { key: 'creator', label: '达人管理', path: 'creator/store' },
  { key: 'sample', label: '索样管理', path: 'sample' },
  { key: 'video', label: '作品管理', path: 'video' },
  { key: 'buyer', label: '买家管理', path: 'buyer' },
  { key: 'schedule', label: '任务与编排', path: 'schedule' },
  { key: 'message', label: '消息中心', path: 'message', badge: 99 },
  { key: 'settings', label: '设置', path: 'settings' },
];

const NavContainer = styled.div`
  display: flex;
  align-items: center;
  height: 100%;
  gap: 0;
`;

const NavItem = styled.div<{ active?: boolean }>`
  position: relative;
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 16px;
  cursor: pointer;
  color: ${(props) => (props.active ? '#0f7cf4' : '#666')};
  font-size: 14px;
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;

  &:hover {
    color: #0f7cf4;
    background-color: rgba(24, 144, 255, 0.05);
  }

  ${(props) =>
    props.active &&
    `
    color: #0f7cf4;
    border-bottom-color: #0f7cf4;
    background-color: rgba(24, 144, 255, 0.05);
  `}
`;

const BadgeWrapper = styled.div`
  position: relative;
  display: inline-block;
`;

// 从 URL 中提取 teamId 和 menuKey
const useCurrentMenuKey = () => {
  const { pathname } = useLocation();

  return useMemo(() => {
    const match = pathname.match(/^\/team\/(\d+)\/(.+)/);
    if (match) {
      const [, teamId, path] = match;
      // 提取第一级路径作为 menuKey
      const menuKey = path.split('/')[0];
      return { teamId, menuKey, fullPath: path };
    }
    return { teamId: null, menuKey: null, fullPath: null };
  }, [pathname]);
};

const HeaderNavs = () => {
  const { teamId, menuKey } = useCurrentMenuKey();

  const handleMenuClick = (item: (typeof MENU_ITEMS)[0]) => {
    if (teamId) {
      history.push(`/team/${teamId}/${item.path}`);
    }
  };

  return (
    <NavContainer>
      {MENU_ITEMS.map((item) => {
        const isActive = menuKey === item.key;

        return (
          <NavItem key={item.key} active={isActive} onClick={() => handleMenuClick(item)}>
            <BadgeWrapper>
              {item.badge ? (
                <Badge count={item.badge} size="small" offset={[10, -5]}>
                  <span>{item.label}</span>
                </Badge>
              ) : (
                <span>{item.label}</span>
              )}
            </BadgeWrapper>
          </NavItem>
        );
      })}
    </NavContainer>
  );
};

export default HeaderNavs;
