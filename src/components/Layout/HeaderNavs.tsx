import { useMemo } from 'react';
import { useLocation, history } from 'umi';
import styled from 'styled-components';
import { Badge } from 'antd';
import routes from '../../../config/routes';

// 从路由配置中提取团队路由下的菜单项
const getTeamRoutes = () => {
  const teamRoute = routes.find((route: any) => route.key === 'team');
  if (!teamRoute || !teamRoute.routes) return [];

  const menuItems: any[] = [];
  const seenKeys = new Set<string>();

  teamRoute.routes
    .filter((route: any) => !route.hideInMenu && route.locale && route.path)
    .forEach((route: any) => {
      // 从路径中提取菜单键 /team/:teamId/xxx -> xxx
      const pathMatch = route.path.match(/^\/team\/:teamId\/(.+)$/);
      const pathSegment = pathMatch ? pathMatch[1] : '';
      const key = pathSegment.split('/')[0]; // 取第一级路径作为key

      // 避免重复的key
      if (!seenKeys.has(key) && key) {
        seenKeys.add(key);
        menuItems.push({
          key,
          label: route.locale,
          path: pathSegment,
          icon: route.meta?.icon,
          iconType: route.meta?.iconType,
        });
      }
    });

  return menuItems;
};

// 菜单配置 - 从路由配置中动态生成
const MENU_ITEMS = getTeamRoutes();

const NavContainer = styled.div`
  display: flex;
  align-items: center;
  height: 100%;
  gap: 0;
`;

const NavItem = styled.div<{ active?: boolean }>`
  position: relative;
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 16px;
  cursor: pointer;
  color: ${(props) => (props.active ? '#0f7cf4' : '#666')};
  font-size: 14px;
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;

  &:hover {
    color: #0f7cf4;
    background-color: rgba(24, 144, 255, 0.05);
  }

  ${(props) =>
    props.active &&
    `
    color: #0f7cf4;
    border-bottom-color: #0f7cf4;
    background-color: rgba(24, 144, 255, 0.05);
  `}
`;

const BadgeWrapper = styled.div`
  position: relative;
  display: inline-block;
`;

// 从 URL 中提取 teamId 和 menuKey
const useCurrentMenuKey = () => {
  const { pathname } = useLocation();

  return useMemo(() => {
    const match = pathname.match(/^\/team\/(\d+)\/(.+)/);
    if (match) {
      const [, teamId, path] = match;
      // 提取第一级路径作为 menuKey
      const menuKey = path.split('/')[0];
      return { teamId, menuKey, fullPath: path };
    }
    return { teamId: null, menuKey: null, fullPath: null };
  }, [pathname]);
};

const HeaderNavs = () => {
  const { teamId, menuKey } = useCurrentMenuKey();

  const handleMenuClick = (item: (typeof MENU_ITEMS)[0]) => {
    if (teamId) {
      history.push(`/team/${teamId}/${item.path}`);
    }
  };

  return (
    <NavContainer>
      {MENU_ITEMS.map((item) => {
        const isActive = menuKey === item.key;

        return (
          <NavItem key={item.key} active={isActive} onClick={() => handleMenuClick(item)}>
            <BadgeWrapper>
              <span>{item.label}</span>
            </BadgeWrapper>
          </NavItem>
        );
      })}
    </NavContainer>
  );
};

export default HeaderNavs;
