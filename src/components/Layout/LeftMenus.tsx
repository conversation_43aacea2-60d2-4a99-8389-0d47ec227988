import { useLocation } from 'umi';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useAuthJudgeCallback } from '@/components/Common/FunctionButton';
import useTkTeamStatusRequest, {
  offTeamStatusUpdate,
  onTeamStatusUpdate,
} from '@/hooks/useTkShopTeam';
import useCurrentTeam from '@/hooks/useCurrentTeam';
import { getTeamIdFromUrl, SkipErrorNotifyOption } from '@/utils/utils';
import { ghJobsListJobsPendingGet } from '@/services/api-TKGHAPI/GhJobController';
import routes from '../../../config/routes';
import useCurrentUser from '@/hooks/useCurrentUser';
import { offTaskCountRefresh, onTaskCountRefresh } from '@/pages/Task';
import { redirectToCurrentTeamPath } from '@/utils/pageUtils';
import { useRequest } from '@@/plugin-request/request';
import _ from 'lodash';
import { Layout, Menu, Tooltip, Typography } from 'antd';
import IconFontIcon from '@/components/Common/IconFontIcon';
import classNames from 'classnames';
import styled from 'styled-components';
import I18N from '@/i18n';

const StyledLayoutSider = styled(Layout.Sider)`
  padding-top: 50px;
  :global {
    .ant-menu-item {
      width: 100% !important;
      height: 50px !important;
      margin-top: 0 !important;
      margin-bottom: 0 !important;
      line-height: 50px !important;
      &::after {
        display: none;
      }
      .hover-icon {
        display: none;
        color: #0f7cf4;
      }
      &:not(.ant-menu-item-selected) {
        &:hover {
          .hover-icon {
            display: inline-block !important;
          }
        }
      }
    }
    .ant-menu-submenu-title {
      height: 50px !important;
      margin: 0 !important;
      line-height: 50px !important;
    }
    .ant-menu-submenu-selected:not(.child-active) {
      color: #0f7cf4;
      background: #e6f6ff;
    }
    .expand-icon {
      position: absolute;
      right: 16px;
    }
  }
`;

const LeftMenus = (props: { matchMenuKeys?: never[] | undefined }) => {
  const { pathname } = useLocation();
  const { matchMenuKeys } = props;
  const [openKeys, setOpenKeys] = useState<string[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [collapsed, setCollapsed] = useState(false);
  const hasAuthFn = useAuthJudgeCallback();
  const { data, run: fetchStatus, reset } = useTkTeamStatusRequest(true);
  const team = useCurrentTeam();
  const { data: taskCount, run: fetchTaskCount } = useRequest(
    async () => {
      if (getTeamIdFromUrl()) {
        return ghJobsListJobsPendingGet(
          {
            ghPlatformType: 'tkshop',
            userEvent: false,
            pageNum: 1,
            pageSize: 1,
          },
          SkipErrorNotifyOption,
        );
      }
      return {
        data: undefined,
      };
    },
    {
      manual: true,
      formatResult(res) {
        return res.data?.total || 0;
      },
      pollingInterval: 1000 * 60,
    },
  );
  const menus = useMemo(() => {
    if (routes?.length) {
      const target = _.find(routes, ({ key }) => key === 'team');
      if (target?.routes) {
        const list = _.cloneDeep(target?.routes);

        list.forEach((item, index) => {
          if (item.path === '/team/:teamId/task') {
            list[index].locale = (
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  overflow: 'hidden',
                  flexWrap: 'nowrap',
                  position: 'relative',
                  gap: 8,
                }}
              >
                <span
                  style={{
                    flex: 1,
                    overflow: 'hidden',
                    whiteSpace: 'nowrap',
                    textOverflow: 'ellipsis',
                  }}
                  title={list[index].locale}
                >
                  {list[index].locale}
                </span>
                <Badge size={'small'} overflowCount={99} count={taskCount || 0} showZero={false} />
              </div>
            );
          }
        });

        return list;
      }
    }
    return [];
  }, [taskCount]);

  const user = useCurrentUser();

  const genItem = useCallback(
    (list: any[]) => {
      const _list: any[] = [];
      if (list?.length) {
        list.forEach((menu) => {
          const { locale, path, hideInMenu, meta, routes: children, className } = menu;
          if (!hideInMenu && locale) {
            const { icon, iconType, access, teamId } = meta || {};
            let iconNode;
            if (icon) {
              if (iconType === 'link') {
                iconNode = (
                  <Typography.Link>
                    <IconFontIcon iconName={icon} size={18} />
                  </Typography.Link>
                );
              } else {
                iconNode = (
                  <span>
                    <ColoursIcon className={icon} size={18} />
                  </span>
                );
              }
            }
            const key = path.replace(':teamId', data?.teamId);
            const childActive = pathname.includes(key) && pathname !== key;

            const item = {
              icon: iconNode,
              label: React.isValidElement(locale) ? locale : I18N.t(locale),
              key,
              children: genItem(children),
              className: classNames(
                {
                  'child-active': childActive,
                },
                className,
              ),
            };
            if (collapsed || !item?.children?.length) {
              delete item.children;
            }
            if (access) {
              if (hasAuthFn(access)) {
                _list.push(item);
              }
            } else if (teamId) {
              try {
                if (getTeamIdFromUrl() === teamId) {
                  _list.push(item);
                }
              } catch (e) {
                console.log(e);
              }
            } else {
              _list.push(item);
            }
          }
        });
      }

      return _list?.length ? _list : [];
    },
    [collapsed, data?.teamId, hasAuthFn, pathname],
  );
  const items = useMemo(() => {
    if (menus?.length) {
      return genItem(menus);
    }
    return [];
  }, [genItem, menus]);
  const collapsible_items = useMemo(() => {
    return _.filter(items, (i) => {
      return i?.children?.length > 0;
    });
  }, [items]);
  const allOpened = useMemo(() => {
    return !_.some(collapsible_items, ({ key }) => {
      return !_.some(openKeys, (j) => {
        return key === j;
      });
    });
  }, [collapsible_items, openKeys]);
  useEffect(() => {
    if (user && data && team) {
      fetchTaskCount();
    }
  }, [data, user, team, fetchTaskCount]);
  useEffect(() => {
    onTaskCountRefresh(fetchTaskCount);
    return () => {
      offTaskCountRefresh(fetchTaskCount);
    };
  }, [fetchTaskCount]);
  useEffect(() => {
    if (!pathname.includes('/team/')) {
      reset();
    }
  }, [pathname, reset]);
  useEffect(() => {
    if (data?.teamId && matchMenuKeys.length > 1) {
      const _keys = matchMenuKeys.map((item: string) => {
        return item.replace(':teamId', String(data.teamId!));
      });
      setSelectedKeys(_keys);
    }
  }, [data?.teamId, matchMenuKeys]);
  useEffect(() => {
    if (selectedKeys.length) {
      setOpenKeys(selectedKeys);
    }
  }, [selectedKeys]);
  useEffect(() => {
    onTeamStatusUpdate(fetchStatus);
    return () => {
      offTeamStatusUpdate(fetchStatus);
    };
  }, [fetchStatus]);

  if (!user || !data || !team) {
    return null;
  }
  if (data?.status !== 'Ready' && data?.status !== 'Expiring') {
    return null;
  }
  return (
    <StyledLayoutSider
      theme={'light'}
      collapsible
      collapsed={collapsed}
      collapsedWidth={50}
      onCollapse={(val) => {
        setCollapsed(val);
      }}
      trigger={
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            padding: '0 16px',
            cursor: 'unset',
            justifyContent: 'space-between',
          }}
          onClick={(e) => {
            e.stopPropagation();
            return false;
          }}
        >
          <Tooltip placement={'right'} title={collapsed ? I18N.t('展开') : I18N.t('收起')}>
            <Typography.Link
              style={{ padding: 2, cursor: 'pointer' }}
              onClick={() => {
                setCollapsed(!collapsed);
              }}
            >
              <IconFontIcon iconName={collapsed ? 'zhankai' : 'shouqi'} />
            </Typography.Link>
          </Tooltip>
          <Tooltip title={allOpened ? I18N.t('全部收起') : I18N.t('全部展开')}>
            {!collapsed && collapsible_items.length > 0 && (
              <Typography.Link
                style={{ padding: 2, cursor: 'pointer' }}
                onClick={() => {
                  setOpenKeys(() => {
                    if (allOpened) {
                      return [];
                    }
                    return collapsible_items.map((item) => item.key as string);
                  });
                }}
              >
                <IconFontIcon
                  size={12}
                  iconName={!allOpened ? 'angle-right_24' : 'angle-down_24'}
                />
              </Typography.Link>
            )}
          </Tooltip>
        </div>
      }
    >
      <Menu
        selectedKeys={selectedKeys}
        onOpenChange={setOpenKeys}
        openKeys={openKeys}
        multiple={false}
        inlineIndent={18}
        expandIcon={(_props) => {
          const { isSubMenu, isOpen } = _props;
          if (isSubMenu) {
            if (isOpen) {
              return (
                <Typography.Link className={'expand-icon'}>
                  <IconFontIcon size={12} iconName={'angle-down_24'} />
                </Typography.Link>
              );
            }
            return (
              <Typography.Link className={'expand-icon'}>
                <IconFontIcon size={12} iconName={'angle-right_24'} />
              </Typography.Link>
            );
          }
          return null;
        }}
        items={items}
        style={{ height: '100%', border: 'none', overflowY: 'auto', overflowX: 'hidden' }}
        mode={'inline'}
        onClick={({ key }) => {
          redirectToCurrentTeamPath(key);
        }}
      />
    </StyledLayoutSider>
  );
};

export default LeftMenus;
