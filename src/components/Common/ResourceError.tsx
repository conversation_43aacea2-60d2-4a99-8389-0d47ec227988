import I18N from '@/i18n';
import ErrorTips from '@/components/Common/ErrorTips';
import { useMemo } from 'react';
import { t } from '@/utils/utils';

type Props = {
  error?: { code: 403 | 404 | 0; message?: string } | undefined;
  resourceType: 'Shop' | 'Ip';
};

/**
 * 无权限访问/资源不存在
 * @param props
 * @constructor
 */
const ResourceError = (props: Props) => {
  const { error, resourceType } = props;
  const { code, message } = error || {};
  const type = useMemo(() => {
    if (resourceType === 'Shop') {
      return I18N.t('账号');
    }
    if (resourceType === 'Ip') {
      return I18N.t('IP');
    }
    return I18N.t('资源');
  }, [resourceType]);
  const title = useMemo(() => {
    if (code === 403) {
      return I18N.t('无权限执行此类操作');
    }
    if (code === 404) {
      return `${I18N.t('该')}${type}${I18N.t('不存在')}`;
    }
    return `${I18N.t('该')}${type}${I18N.t('无法访问')}`;
  }, [code, type]);
  const desc = useMemo(() => {
    if (code === 403) {
      return `${I18N.t(
        '您当前的角色身份没有权限执行此类操作，请联络团队管理员以获取进一步的信息',
      )}`;
    }
    if (code === 404) {
      return `${I18N.t('该')}${type}${I18N.t('不存在，可能已经被删除，无法查看其详情信息')}`;
    }
    return message || `${I18N.t('该')}${type}${I18N.t('无法访问，无法查看其详情信息')}`;
  }, [code, message, type]);

  return <ErrorTips title={title} desc={desc} />;
};
export default ResourceError;
