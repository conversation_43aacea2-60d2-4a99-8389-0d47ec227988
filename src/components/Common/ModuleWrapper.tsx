import I18N from '@/i18n';
import useKolConfig from '@/hooks/useKolConfig';
import MiddleSpin from '@/components/Common/MiddleSpin';
import ErrorTips from '@/components/Common/ErrorTips';

export type TikTokModuleControlCode = 'browser' | 'mobile' | 'util';
const ModuleWrapper = (props: {
  children: any;
  code: TikTokModuleControlCode;
  direction?: 'vertical' | 'horizontal';
}) => {
  const { children, code, direction } = props;
  const { loading, data } = useKolConfig();
  if (loading || !data) {
    return <MiddleSpin />;
  }
  if (data.platformModules?.includes(code)) {
    return children;
  }
  return (
    <ErrorTips
      direction={direction}
      title={I18N.t('您尚未开通此模块')}
      desc={I18N.t('请您联系在线客服开通使用')}
    />
  );
};
export default ModuleWrapper;
