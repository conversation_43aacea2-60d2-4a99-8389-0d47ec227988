import type { HTMLAttributes } from 'react';
import { useEffect } from 'react';
import React, { useState, useMemo } from 'react';
import { Avatar, Avatar as AntdAvatar } from 'antd';
import classNames from 'classnames';
import type { AvatarProps } from 'antd/lib/avatar/avatar';
import styles from './UserAvatarAndName.less';
import constants from '@/constants';
import { SkipErrorNotifyOption } from '@/utils/utils';
import { tkshopCreatorByIdGet } from '@/services/api-TKShopAPI/TkshopCreatorController';
import CreatorHandleCell from '@/pages/TikTok/Live/components/CreatorHandleCell';
import I18N from '@/i18n';

interface Props {
  creator: { id: number; handle: string; avatar?: string };
  avatarSize?: number;
  avatarProps?: AvatarProps;
}

/**
 * 头像，处理图片加载失败的情况，显示默认头像
 * @param props
 * @constructor
 */
export function CreatorAvatar(props: AvatarProps & { border?: boolean; src?: string }) {
  const { src, border, ...otherProps } = props;
  const [isDefaultAvatar, setIsDefaultAvatar] = useState(true);
  const [innerSrc, setInnerSrc] = useState(constants.defaultAvatar);

  useEffect(() => {
    if (src) {
      const img = new Image();
      img.src = src;
      img.onload = () => {
        setInnerSrc(src);
        setIsDefaultAvatar(false);
      };
    }
  }, [src]);
  return (
    <AntdAvatar
      className={classNames({
        [styles.border]: !isDefaultAvatar && border,
      })}
      src={innerSrc}
      {...otherProps}
    />
  );
}

/**
 * 用户头像、昵称和ID
 * 头像优先使用 user.avatar，如果没有传 avatar ，就使用 user.id 去获取
 * @param props
 * @constructor
 */
const TkShopCreator: React.FC<Props & HTMLAttributes<any>> = (props) => {
  const { creator, avatarSize = 24, avatarProps, className, ...otherProps } = props;
  const { handle, avatar, id } = creator;

  const img = useMemo(() => {
    if (avatar) {
      return (
        <Avatar
          src={avatar}
          style={{ minWidth: avatarSize }}
          size={avatarSize}
          shape={'circle'}
          {...avatarProps}
        />
      );
    }
    return <CreatorAvatar size={avatarSize} id={id} style={{ flexShrink: 0 }} {...avatarProps} />;
  }, [avatar, avatarProps, avatarSize, id]);
  return (
    <span
      title={handle}
      className={classNames(styles.userAvatarAndName, className)}
      {...otherProps}
    >
      {img}
      <span title={handle} className="nickname">
        {handle}
      </span>
    </span>
  );
};
const CreatorByIdCache: Record<number, Promise<API.TkshopCreatorDetailVo>> = {};

export function useLiveCreatorById(creatorId: number) {
  const [data, setData] = useState<API.TkshopCreatorDetailVo>();
  useEffect(() => {
    if (!Number.isNaN(Number(creatorId)) && creatorId) {
      if (!CreatorByIdCache[creatorId]) {
        CreatorByIdCache[creatorId] = tkshopCreatorByIdGet(
          {
            id: creatorId,
          },
          SkipErrorNotifyOption,
        )
          .then((res) => {
            return (
              res.data! || {
                code: 404,
                id: creatorId,
                error: true,
                message: I18N.t('该达人可能已经被删除'),
              }
            );
          })
          .catch((e) => {
            return {
              code: e?.data?.code,
              id: creatorId,
              error: true,
              message: e.message,
            };
          });
      }
      CreatorByIdCache[creatorId].then((res) => {
        setData(res);
      });
    }
  }, [creatorId]);
  return data;
}

/**
 * 公会达人
 * @param props
 * @constructor
 */
export const TkShopCreatorById = (props: { id: number; size?: number; handle?: string }) => {
  const { id, size = 20, handle } = props;
  const creator = useLiveCreatorById(id);
  return (
    <CreatorHandleCell
      validate
      size={size}
      creator={creator?.error ? { id: id, handle: handle } : creator}
    />
  );
};
export default TkShopCreator;
