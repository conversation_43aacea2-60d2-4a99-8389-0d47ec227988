import I18N from '@/i18n';
import type { ReactNode } from 'react';
import { useEffect } from 'react';
import { useState } from 'react';
import { SkipErrorNotifyOption } from '@/utils/utils';
import {
  shopMobileAccountGetAuthorizedAccountGet,
  shopMobileAccountGetGet,
} from '@/services/api-ShopAPI/MobileAccountController';
import { Typography } from 'antd';
import { useRequest } from '@@/plugin-request/request';
import DMConfirm from '@/components/Common/DMConfirm';
import { openClient } from '@/utils/pageUtils';
import { getCurrentTeamId } from '@/hooks/useCurrentTeam';

const MobileAccountByIdCache: Record<number, Promise<API.MobileAccountVo>> = {};

export function useMobileAccountById(accountId: number) {
  const [data, setData] = useState<API.MobileAccountVo>();
  useEffect(() => {
    if (!Number.isNaN(Number(accountId)) && accountId) {
      if (!MobileAccountByIdCache[accountId]) {
        MobileAccountByIdCache[accountId] = shopMobileAccountGetGet(
          {
            id: accountId,
          },
          SkipErrorNotifyOption,
        )
          .then((res) => {
            return res.data!;
          })
          .catch((e) => {
            return {
              code: e?.data?.code,
              id: accountId,
              error: true,
              message: e.message,
            };
          });
      }
      MobileAccountByIdCache[accountId].then((res) => {
        setData(res);
      });
    }
  }, [accountId]);
  return data;
}

export const MobileAccountToolTip = (props: { id: number; children: ReactNode }) => {
  const { id, children } = props;
  const mobileAccount = useMobileAccountById(id);
  const { run: onClick, loading } = useRequest(
    async (switchAccount?: boolean) => {
      if (!mobileAccount) {
        return;
      }
      const options: any = {
        teamId: getCurrentTeamId(),
        // rpaFlowBizCode: 'mobile.account.switch',
        action: 'popupMobile',
        mobileId: mobileAccount?.mobileId,
        // handle: switchAccount ? mobileAccount?.username : undefined,
      };
      await shopMobileAccountGetAuthorizedAccountGet(
        {
          mobileId: options.mobileId,
          username: mobileAccount.username!,
        },
        SkipErrorNotifyOption,
      );
      await openClient(options);
    },
    {
      manual: true,
      onError(e) {
        const err_msg = e.message;
        if (err_msg === 'NOT_FOUND') {
          DMConfirm({
            type: 'info',
            title: I18N.t('该手机账号已删除或不存在'),
            content: I18N.t('指定的手机账号已删除或不存在，无法访问'),
          });
        } else if (err_msg === 'NOT_AUTHORIZED') {
          DMConfirm({
            type: 'info',
            title: I18N.t('您无权访问该手机账号'),
            content: I18N.t('请和团队管理员联系'),
          });
        }
        console.log(e.message);
      },
    },
  );
  return (
    <Typography.Link
      onClick={() => {
        if (!loading) {
          onClick();
        }
      }}
      ellipsis
    >
      {children}
    </Typography.Link>
  );
};
