import I18N from '@/i18n';
import type { CSSProperties } from 'react';
import { useCallback, useMemo, useRef, useState } from 'react';
import type { ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { shopByCategoryByCategoryGet } from '@/services/api-ShopAPI/ShopController';
import { Form, Input, Typography, Tooltip } from 'antd';
import { scrollProTableOptionFn } from '@/mixins/table';
import styles from './index.less';
import DMModal from '@/components/Common/Modal/DMModal';
import { TagMoreDropdown } from '@/components/Common/MoreDropdown';
import { useRequest } from '@@/plugin-request/request';
import _ from 'lodash';
import TkShopTypeSelector from '@/components/Common/Selector/TkShopTypeSelector';
import TkRegionSelector from '@/components/Common/Selector/TkRegionSelector';
import { OptionPlaceholder } from '@/components/Common/Placeholder';
import { useVT } from 'virtualizedtableforantd4';
import type { TkShopDetailVo } from '@/pages/Shop/components/utils';
import { getShopType } from '@/pages/Shop/components/utils';
import { getShopExtensionType } from '@/pages/Shop/components/utils';
import { ShopDetailNode } from '@/components/Common/ShopNode';

export type IdRecord = { id: number; extension: TkShopDetailVo['extension']; [key: string]: any };

export function isShopDisabledByExtension(
  record: TkShopDetailVo,
  value: TkShopDetailVo[],
  key = 'extension',
): boolean {
  const isCurrentSelected = value?.some((item) => item.id === record.id);
  if (isCurrentSelected) {
    return false;
  }
  const hasExtensionSelected = value?.some((item) => item[key] === 'extension');
  if (hasExtensionSelected) {
    return true;
  }
  const hasOtherSelected = value?.some((item) => item[key] !== 'extension');
  if (hasOtherSelected) {
    return record[key] === 'extension';
  }

  return false;
}
export function getNewRowsByExtension(
  rows: TkShopDetailVo[],
  type: 'radio' | 'checkbox',
  key = 'extension',
) {
  if (!rows?.length) {
    return [];
  }

  if (type === 'radio') {
    return rows;
  } else {
    // 这里要rows处理一下,如果只选中了一个extension="extension"的则可以,否则过滤掉
    if (rows.length === 1 && rows[0][key] === 'extension') {
      return rows;
    } else {
      return rows.filter((item) => item[key] !== 'extension');
    }
  }
}
export const ShopTableField = (props: {
  type: 'radio' | 'checkbox';
  value?: IdRecord[];
  onChange?: (data: IdRecord[]) => void;
  autoInit?: boolean;
  area?: string;
  disabledExtension?: boolean;
  pure?: boolean;
  style?: CSSProperties;
}) => {
  const [form] = Form.useForm();
  const { type, value = [], onChange, autoInit, area, pure, style = {}, disabledExtension } = props;
  const table = useRef<ActionType>();
  const [vt] = useVT(() => {
    return {
      scroll: {
        y: 400,
      },
    };
  }, []);
  const selectedRowKeys = useMemo(() => {
    return (
      value?.map((item) => {
        return item.id!;
      }) || []
    );
  }, [value]);

  const header = useMemo(() => {
    if (pure) {
      return false;
    }
    return (
      <Form
        className={styles.searchForm}
        form={form}
        style={{ justifyContent: 'flex-end', height: 50, paddingRight: 0 }}
        layout={'inline'}
      >
        <Form.Item label={false} name={'shopType'}>
          <TkShopTypeSelector
            onChange={() => {
              table.current?.reload();
            }}
            placeholder={<OptionPlaceholder text={I18N.t('全部类型')} type={'platform'} />}
          />
        </Form.Item>
        <Form.Item label={false} name={'areas'} initialValue={area}>
          <TkRegionSelector
            placeholder={<OptionPlaceholder type={'site'} />}
            allowClear={!area}
            onChange={() => {
              table.current?.reload();
            }}
            valuePropName={'countryEn'}
            filter={(item) => {
              if (area) {
                return area === item.countryEn;
              }
              return true;
            }}
          />
        </Form.Item>
        <Form.Item label={false} name="query" style={{ width: 200 }}>
          <Input.Search
            allowClear
            placeholder={I18N.t('根据名称或备注检索')}
            onSearch={(_value) => {
              form.setFieldsValue({
                query: _.trim(_value),
              });
              table.current?.reload();
            }}
          />
        </Form.Item>
      </Form>
    );
  }, [area, form, pure]);
  const isRecordDisabled = useCallback(
    (record: TkShopDetailVo) => {
      if (disabledExtension) {
        return record.extension === 'extension';
      }
      if (type === 'radio') {
        return false;
      }
      return isShopDisabledByExtension(record, value);
    },
    [disabledExtension, type, value],
  );
  return (
    <div
      style={{
        height: '100%',
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column',
        ...style,
      }}
    >
      {header}
      <div style={{ flex: 1, overflow: 'hidden' }}>
        <ProTable<API.ShopDetailVo>
          {...scrollProTableOptionFn({
            size: 'small',
            search: false,
            rowSelection: {
              type,
              selectedRowKeys,
              onChange: (keys, rows) => {
                const newRows = getNewRowsByExtension(rows, type);
                onChange?.(newRows);
              },
              getCheckboxProps(record) {
                return {
                  disabled: isRecordDisabled(record),
                };
              },
              renderCell(value, record, index, originNode) {
                const isDisabled = isRecordDisabled(record);
                if (disabledExtension && record.extension === 'extension') {
                  return (
                    <Tooltip placement="left" title={I18N.t('暂不支持对其它浏览器的自动化流程')}>
                      <span>{originNode}</span>
                    </Tooltip>
                  );
                }
                if (isDisabled) {
                  if (type === 'checkbox') {
                    return (
                      <Tooltip
                        placement="left"
                        title={
                          record.extension === 'extension'
                            ? I18N.t('不支持同时多个其他浏览器')
                            : I18N.t('不支持同时选择花漾浏览器与其他浏览器')
                        }
                      >
                        <span>{originNode}</span>
                      </Tooltip>
                    );
                  }
                }
                return originNode;
              },
            },
            onRow(record) {
              return {
                onClick() {
                  if (onChange) {
                    if (isRecordDisabled(record)) {
                      return;
                    }
                    // 实现一个选择与反选的方法,根据多选还是单选来
                    if (type === 'radio') {
                      onChange([record]);
                    } else {
                      // 多选
                      const index = selectedRowKeys.indexOf(record.id!);
                      if (index > -1) {
                        onChange(value?.filter((item) => item.id !== record.id) || []);
                      } else {
                        onChange([...(value || []), record]);
                      }
                    }
                  }
                },
                style: {
                  cursor: 'pointer',
                },
              };
            },
            pagination: false,
          })}
          columns={[
            {
              title: I18N.t('分身名称'),
              dataIndex: 'name',
              render(text, record) {
                return <ShopDetailNode data={record} />;
              },
            },
            {
              title: I18N.t('类型'),
              dataIndex: 'shopType',
              render(text, record) {
                return getShopType(record);
              },
            },
            {
              title: I18N.t('浏览器'),
              width: 190,
              dataIndex: 'extension',
              render(text, record) {
                return getShopExtensionType(record);
              },
            },
            !pure && {
              title: I18N.t('标签'),
              dataIndex: 'tags',
              render: (dom, record) => {
                const { tags } = record;
                return <TagMoreDropdown tags={tags} />;
              },
            },
          ].filter(Boolean)}
          debounceTime={500}
          request={() => {
            const params = form.getFieldsValue();
            const body: API.shopByCategoryByCategoryGetParams = {
              category: 'all',
              platformTypes: 'TikTok',
              pageSize: 9999,
              pageNum: 1,
              ...params,
            };
            return shopByCategoryByCategoryGet(body).then((rs) => {
              const { list = [], total = 0 } = rs.data ?? {};
              const __list = _.orderBy(list, 'extension', 'desc');
              const _list = list.filter((item) => item.extension !== 'extension') || [];

              if (autoInit && !value?.length && onChange && _list.length) {
                // 这里要过滤掉extension='extension'的店铺
                onChange((type === 'radio' ? [_list[0]] : _list) as any);
              }
              return {
                data: __list,
                total,
              };
            });
          }}
          components={vt}
          actionRef={table}
          rowKey="id"
        />
      </div>
    </div>
  );
};

const SelectTkShopModal = (props: {
  onSubmit: (shop: API.ShopDetailVo[]) => Promise<void>;
  onCancel?: () => void;
}) => {
  const { onCancel, onSubmit } = props;
  const [selectedRows, setSelectedRows] = useState<IdRecord[]>([]);
  const [open, setOpen] = useState(true);
  const { run: submit, loading } = useRequest(
    async () => {
      await onSubmit(selectedRows);
      setOpen(false);
    },
    {
      manual: true,
    },
  );

  return (
    <DMModal
      onOk={submit}
      confirmLoading={loading}
      title={I18N.t('选择执行流程的店铺')}
      okButtonProps={{
        disabled: !selectedRows.length,
      }}
      bodyStyle={{
        paddingTop: 0,
      }}
      width={720}
      open={open}
      onCancel={() => {
        setOpen(false);
        onCancel?.();
      }}
    >
      <div style={{ height: 500, overflow: 'hidden' }}>
        <ShopTableField
          autoInit
          type={'radio'}
          value={selectedRows}
          disabledExtension
          onChange={setSelectedRows}
        />
      </div>
    </DMModal>
  );
};

export default SelectTkShopModal;
