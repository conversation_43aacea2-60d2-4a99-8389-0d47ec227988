import I18N from '@/i18n';
import type { CSSProperties } from 'react';
import { useMemo, useRef, useState } from 'react';
import type { ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { shopByCategoryByCategoryGet } from '@/services/api-ShopAPI/ShopController';
import { Form, Input, Space, Typography } from 'antd';
import { scrollProTableOptionFn } from '@/mixins/table';
import styles from './index.less';
import DMModal from '@/components/Common/Modal/DMModal';
import { TagMoreDropdown } from '@/components/Common/MoreDropdown';
import { useRequest } from '@@/plugin-request/request';
import _ from 'lodash';
import TkShopTypeSelector from '@/components/Common/Selector/TkShopTypeSelector';
import CountryIcon from '@/components/Common/CountryIcon';
import constants from '@/constants';
import TkRegionSelector from '@/components/Common/Selector/TkRegionSelector';
import { OptionPlaceholder } from '@/components/Common/Placeholder';
import { useVT } from 'virtualizedtableforantd4';

export type IdRecord = { id: number; [key: string]: any };

export const ShopTableField = (props: {
  type: 'radio' | 'checkbox';
  value?: IdRecord[];
  onChange?: (data: IdRecord[]) => void;
  autoInit?: boolean;
  area?: string;
  pure?: boolean;
  style?: CSSProperties;
}) => {
  const [form] = Form.useForm();
  const { type, value, onChange, autoInit, area, pure, style = {} } = props;
  const table = useRef<ActionType>();
  const [vt] = useVT(() => {
    return {
      scroll: {
        y: 400,
      },
    };
  }, []);
  const selectedRowKeys = useMemo(() => {
    return (
      value?.map((item) => {
        return item.id!;
      }) || []
    );
  }, [value]);

  const header = useMemo(() => {
    if (pure) {
      return false;
    }
    return (
      <Form
        className={styles.searchForm}
        form={form}
        style={{ justifyContent: 'flex-end', height: 50, paddingRight: 0 }}
        layout={'inline'}
      >
        <Form.Item label={false} name={'shopType'}>
          <TkShopTypeSelector
            onChange={() => {
              table.current?.reload();
            }}
            placeholder={<OptionPlaceholder text={I18N.t('全部类型')} type={'platform'} />}
          />
        </Form.Item>
        <Form.Item label={false} name={'areas'} initialValue={area}>
          <TkRegionSelector
            placeholder={<OptionPlaceholder type={'site'} />}
            allowClear={!area}
            onChange={() => {
              table.current?.reload();
            }}
            valuePropName={'countryEn'}
            filter={(item) => {
              if (area) {
                return area === item.countryEn;
              }
              return true;
            }}
          />
        </Form.Item>
        <Form.Item label={false} name="query" style={{ width: 200 }}>
          <Input.Search
            allowClear
            placeholder={I18N.t('根据名称或备注检索')}
            onSearch={(_value) => {
              form.setFieldsValue({
                query: _.trim(_value),
              });
              table.current?.reload();
            }}
          />
        </Form.Item>
      </Form>
    );
  }, [area, form, pure]);
  return (
    <div
      style={{
        height: '100%',
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column',
        ...style,
      }}
    >
      {header}
      <div style={{ flex: 1, overflow: 'hidden' }}>
        <ProTable<API.ShopDetailVo>
          {...scrollProTableOptionFn({
            size: 'small',
            search: false,
            rowSelection: {
              type,
              selectedRowKeys,
              onChange: (keys, rows) => {
                onChange?.(rows);
              },
            },
            onRow(record) {
              return {
                onClick() {
                  if (onChange) {
                    // 实现一个选择与反选的方法,根据多选还是单选来
                    if (type === 'radio') {
                      onChange([record]);
                    } else {
                      // 多选
                      const index = selectedRowKeys.indexOf(record.id!);
                      if (index > -1) {
                        onChange(...(value?.filter((item) => item.id !== record.id) || []));
                      } else {
                        onChange([...(value || []), record]);
                      }
                    }
                  }
                },
                style: {
                  cursor: 'pointer',
                },
              };
            },
            pagination: false,
          })}
          columns={[
            {
              title: I18N.t('分身名称'),
              dataIndex: 'name',
              render(text, record) {
                return (
                  <Typography.Text ellipsis title={record.name}>
                    {record.name}
                  </Typography.Text>
                );
              },
            },
            !pure && {
              title: I18N.t('类型'),
              dataIndex: 'shopType',
              render(text, record) {
                const { type } = record;
                if (type === 'Global') {
                  return I18N.t('跨境店');
                } else {
                  return I18N.t('本土店');
                }
              },
            },
            {
              title: I18N.t('站点'),
              width: 120,
              dataIndex: 'area',
              render(text, record) {
                const { platform } = record;
                const area = platform?.area;
                return (
                  <Space>
                    <CountryIcon size={16} country={area} />
                    {constants.Area[area]}
                  </Space>
                );
              },
            },
            !pure && {
              title: I18N.t('标签'),
              dataIndex: 'tags',
              render: (dom, record) => {
                const { tags } = record;
                return <TagMoreDropdown tags={tags} />;
              },
            },
          ].filter(Boolean)}
          debounceTime={500}
          request={() => {
            const params = form.getFieldsValue();
            const body: API.shopByCategoryByCategoryGetParams = {
              category: 'all',
              platformTypes: 'TikTok',
              pageSize: 9999,
              pageNum: 1,
              ...params,
            };
            return shopByCategoryByCategoryGet(body).then((rs) => {
              const { list = [], total = 0 } = rs.data ?? {};
              if (autoInit && !value?.length && onChange && list.length) {
                onChange(type === 'radio' ? [list[0]] : list);
              }
              return {
                data: list,
                total,
              };
            });
          }}
          components={vt}
          actionRef={table}
          rowKey="id"
        />
      </div>
    </div>
  );
};

const SelectTkShopModal = (props: {
  onSubmit: (shop: API.ShopDetailVo[]) => Promise<void>;
  onCancel?: () => void;
}) => {
  const { onCancel, onSubmit } = props;
  const [selectedRows, setSelectedRows] = useState<IdRecord[]>([]);
  const [open, setOpen] = useState(true);
  const { run: submit, loading } = useRequest(
    async () => {
      await onSubmit(selectedRows);
      setOpen(false);
    },
    {
      manual: true,
    },
  );

  return (
    <DMModal
      onOk={submit}
      confirmLoading={loading}
      title={I18N.t('选择执行流程的店铺')}
      okButtonProps={{
        disabled: !selectedRows.length,
      }}
      bodyStyle={{
        paddingTop: 0,
      }}
      width={720}
      open={open}
      onCancel={() => {
        setOpen(false);
        onCancel?.();
      }}
    >
      <div style={{ height: 500, overflow: 'hidden' }}>
        <ShopTableField autoInit type={'radio'} value={selectedRows} onChange={setSelectedRows} />
      </div>
    </DMModal>
  );
};

export default SelectTkShopModal;
