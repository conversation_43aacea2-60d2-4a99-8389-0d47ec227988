@import '/src/style/theme';

.wrap {
  display: flex;
}

@gap: 24px;
.left {
  flex: 1;
  padding-right: @gap;
  overflow: hidden;
}

.right {
  flex: 0 0 220px;
  padding: @gap 0 @gap @gap;
  border-left: 1px solid @border-color-split;

  .tips-wrap {
    height: 100%;
    padding: 15px;
    background-color: #f5f5f5;
    > div {
      margin-bottom: 8px;
      color: #878787;
      font-size: 12px;
    }
    > div:first-child {
      color: @text-color;
      font-size: 14px;
    }
  }
}

.search-form {
  display: flex;
  align-items: center;
  padding: 0 16px;
  :global {
    .ant-select {
      width: 130px;
    }
    .ant-form-item {
      margin-right: 8px;
      &:nth-last-of-type(1) {
        margin-right: 0;
      }
    }
  }
}
